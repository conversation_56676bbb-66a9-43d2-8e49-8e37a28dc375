terraform {
  backend "s3" {
    key = "states/services/rat-logic-service"
  }
}

provider "aws" {
  region = var.region
}


data "aws_iam_policy_document" "rat_logic_service_account_policy" {
#   statement {
#     actions   = ["bedrock:*"]
#     resources = ["*"]
#   }
#   statement {
#     actions   = ["es:ESHttpPost", "es:ESHttpGet", "es:ESHttpPut"]
#     resources = [opensearch_arn]
#   }
}

resource "aws_iam_policy" "service_policy" {
  name   = "${var.environment}-${var.service_name}-policy"
  policy = data.aws_iam_policy_document.rat_logic_service_account_policy.json
}

module "service_account" {
  source = "**************:primesec-ai/devops-infra.git//shared/service_account"

  cluster_name = var.cluster_name
  prefix       = var.environment
  policy_arn   = aws_iam_policy.service_policy.arn
  service_name = var.service_name
  with_policy  = true
}
