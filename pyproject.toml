[tool.poetry]
name = "rat-logic-service"
version = "0.1.4"
description = ""
authors = ["matan markovics <<EMAIL>>", "roym <<EMAIL>>"]
packages = [{ from = "src", include = "service" }]
requires-poetry = ">=2.1"

[tool.poetry.dependencies]
python = "^3.13"
pydantic = "^2.8"
python-multipart = "^0"
sqlmodel = "*"
prime-service-kit = { version = "*", source = "main_repo" }
prime-db-utils = { version = "*", source = "main_repo" }
prime-file-manager-service-client = { version = "*", source = "main_repo" }
prime-config-service-client = { version = "*", source = "main_repo" }
prime-gen-ai-service-client = { version = "*", source = "main_repo" }
prime-fetcher-service-client = { version = "*", source = "main_repo" }
prime-chatbot-service-client = { version = "*", source = "main_repo" }
prime-policy-service-client = { version = "*", source = "main_repo" }
prime-shared = { version = "*", source = "main_repo" }
prime-jira-client = { version = "*", source = "main_repo" }
prime-source-service-client = { version = "*", source = "main_repo" }
prime-redis-utils = { version = "*", source = "main_repo" }
prime-utils = { version = "*", source = "main_repo" }
prime-notification-service-client = { version = "*", source = "main_repo" }
prime-events = { version = "*", source = "main_repo" }
prime-celery = { version = "*", source = "main_repo" }
prime-jobs = { version = "*", source = "main_repo" }
xmltodict = "^0.14.1"
tenacity = "^9.0.0"
python-dateutil = "^2.9.0.post0"
boto3 = { extras = ["opensearch"], version = "*" }
opensearch-py = { version = "2.8.0" }                                       # this one has less than 1k stars so im locking the version
datadog = "^0.51.0"
langfuse = "^2.60.2"
python-igraph = "*"
msgpack = "^1.1.0"
pydantic-ai-slim = { extras = ["bedrock"], version = "^0.1.9" }
fpdf = "^1.7.2"
pypdf = "^3.10.0"
prime-security-review-service-client = { version = "*", source = "main_repo" }

[tool.poetry.group.dev.dependencies]
prime-rat-logic-service-client = { path = "client", develop = true }
aiounittest = "*"
prime-tests = { version = "*", source = "main_repo" }
types-xmltodict = "^0.14.0.20241009"
types-python-dateutil = "*"
boto3-stubs = { extras = [
    "s3",
    "bedrock-agent",
    "bedrock-runtime",
    "opensearchserverless",
], version = "^1.37.23" }
celery = { extras = ["pytest"], version = "^5.4.0" }
aioresponses = "^0.7.7"
respx = "^0.22.0"
freezegun = "*"
msgpack-types = "*"
types-fpdf2 = "*"
pytest-mock = "*"

[tool.poetry.scripts]
server = "src.service.main:main"
generate_service_openapi = "src.service.generate_openapi:main"
# jobs
classification_job = "src.service.k8s_jobs.classification_job.main:main"
update_provider_fields_job = "src.service.k8s_jobs.update_provider_fields_job.main:main"
# cron jobs
watchdog_task = "prime_jobs.jobs_monitor_scripts.watchdog:main"
spawn_scheduled_jobs_task = "prime_jobs.jobs_monitor_scripts.spawn_scheduled_jobs:main"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "main_repo"
url = "https://primesec-797808101759.d.codeartifact.eu-central-1.amazonaws.com/pypi/main-repo/simple/"
priority = "supplemental"

[[tool.poetry.source]]
name = "main_repo_publish"
url = "https://primesec-797808101759.d.codeartifact.eu-central-1.amazonaws.com/pypi/main-repo/"
priority = "supplemental"