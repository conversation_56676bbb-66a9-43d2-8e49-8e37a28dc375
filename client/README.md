# prime-rat-logic-service-client
Service API

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0.0
- Package version: 1.0.0
- Generator version: 7.12.0
- Build package: org.openapitools.codegen.languages.PythonClientCodegen

## Requirements.

Python 3.8+

## Installation & Usage
### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import prime_rat_logic_service_client
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import prime_rat_logic_service_client
```

### Tests

Execute `pytest` to run the tests.

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python

import prime_rat_logic_service_client
from prime_rat_logic_service_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = prime_rat_logic_service_client.Configuration(
    host = "http://localhost"
)



# Enter a context with an instance of the API client
async with prime_rat_logic_service_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = prime_rat_logic_service_client.CasesApi(api_client)
    account_id = 'account_id_example' # str | Account ID
    source_id = 56 # int | 
    issue_id = 'issue_id_example' # str | 
    create_case_comment = prime_rat_logic_service_client.CreateCaseComment() # CreateCaseComment | 

    try:
        # Add Comment
        api_response = await api_instance.add_comment(account_id, source_id, issue_id, create_case_comment)
        print("The response of CasesApi->add_comment:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling CasesApi->add_comment: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*CasesApi* | [**add_comment**](docs/CasesApi.md#add_comment) | **PUT** /cases/{account_id}/source/{source_id}/issue/{issue_id}/comment | Add Comment
*CasesApi* | [**add_watcher**](docs/CasesApi.md#add_watcher) | **POST** /cases/{account_id}/source/{source_id}/issue/{issue_id}/watcher | Add Watcher
*CasesApi* | [**autocomplete**](docs/CasesApi.md#autocomplete) | **GET** /cases/{account_id}/search/{field}/{value} | Autocomplete
*CasesApi* | [**autocomplete_search_global_cases**](docs/CasesApi.md#autocomplete_search_global_cases) | **GET** /cases/{account_id}/search/{value} | Autocomplete Search Global Cases
*CasesApi* | [**bulk_update_cases**](docs/CasesApi.md#bulk_update_cases) | **POST** /cases/{account_id}/bulk-update/{source_id} | Bulk Update Cases
*CasesApi* | [**delete_cases_for_source**](docs/CasesApi.md#delete_cases_for_source) | **DELETE** /cases/{account_id}/source/{source_id} | Delete Cases For Source
*CasesApi* | [**export_for_account**](docs/CasesApi.md#export_for_account) | **GET** /cases/{account_id}/export | Export For Account
*CasesApi* | [**generate_recommendations_for_concern_ids**](docs/CasesApi.md#generate_recommendations_for_concern_ids) | **POST** /cases/{account_id}/{source_id}/{issue_id}/recommendations/generate | Generate Recommendations For Concern Ids
*CasesApi* | [**get_case**](docs/CasesApi.md#get_case) | **GET** /cases/{account_id}/source/{source_id}/issue/{issue_id} | Get Case
*CasesApi* | [**get_case_by_id**](docs/CasesApi.md#get_case_by_id) | **GET** /cases/{account_id}/case/{case_id} | Get Case By Id
*CasesApi* | [**get_case_id**](docs/CasesApi.md#get_case_id) | **GET** /cases/{account_id}/{source_id}/{issue_id}/case_id | Get Case Id
*CasesApi* | [**get_cases_for_account**](docs/CasesApi.md#get_cases_for_account) | **GET** /cases/{account_id} | Get Cases For Account
*CasesApi* | [**get_cases_for_account_and_source**](docs/CasesApi.md#get_cases_for_account_and_source) | **GET** /cases/{account_id}/source/{source_id} | Get Cases For Account And Source
*CasesApi* | [**get_labels**](docs/CasesApi.md#get_labels) | **GET** /cases/{account_id}/labels | Get Labels
*CasesApi* | [**set_labels**](docs/CasesApi.md#set_labels) | **PUT** /cases/{account_id}/source/{source_id}/issue/{issue_id}/label | Set Labels
*CasesApi* | [**update_recommendations**](docs/CasesApi.md#update_recommendations) | **PUT** /cases/{account_id}/source/{source_id}/issue/{issue_id}/recommendations | Update Recommendations
*CasesApi* | [**update_risk_score_category**](docs/CasesApi.md#update_risk_score_category) | **PUT** /cases/{account_id}/source/{source_id}/issue/{issue_id}/risk-score-category | Update Risk Score Category
*CasesApi* | [**update_status**](docs/CasesApi.md#update_status) | **PUT** /cases/{account_id}/source/{source_id}/issue/{issue_id}/status | Update Status
*CasesApi* | [**write_back**](docs/CasesApi.md#write_back) | **POST** /cases/{account_id}/source/{source_id}/issue/{issue_id}/write-back | Write Back
*ContainersApi* | [**get_container**](docs/ContainersApi.md#get_container) | **GET** /containers/{account_id}/source/{source_id}/issue/{issue_id} | Get Container
*HealthApi* | [**is_alive**](docs/HealthApi.md#is_alive) | **GET** /is-alive | Is Alive
*IssuesApi* | [**get_container_children_data**](docs/IssuesApi.md#get_container_children_data) | **GET** /issues/{account_id}/children/{case_id} | Get Container Children Data
*IssuesApi* | [**get_issues_keys**](docs/IssuesApi.md#get_issues_keys) | **GET** /issues/{account_id}/{source_id} | Get-Issues-Keys
*JiraFieldsApi* | [**get_all_provider_fields**](docs/JiraFieldsApi.md#get_all_provider_fields) | **GET** /jira-fields/{account_id}/all/{source_id} | Get All Provider Fields
*JiraFieldsApi* | [**get_selected_provider_fields**](docs/JiraFieldsApi.md#get_selected_provider_fields) | **GET** /jira-fields/{account_id}/selected/{source_id} | Get Selected Provider Fields
*JiraFieldsApi* | [**get_workroom_fields**](docs/JiraFieldsApi.md#get_workroom_fields) | **GET** /jira-fields/{account_id}/workroom | Get Workroom Fields
*JobsApi* | [**add_job**](docs/JobsApi.md#add_job) | **POST** /jobs/{account_id}/ | Add Job
*JobsApi* | [**get_job**](docs/JobsApi.md#get_job) | **GET** /jobs/{account_id}/{job_id} | Get Job
*JobsApi* | [**get_jobs**](docs/JobsApi.md#get_jobs) | **GET** /jobs/{account_id} | Get Jobs
*LlmContextApi* | [**cases**](docs/LlmContextApi.md#cases) | **GET** /llm-context/{account_id}/cases/{container_id} | Cases
*PsvApi* | [**bulk_update_psv_status**](docs/PsvApi.md#bulk_update_psv_status) | **POST** /psv/{account_id}/bulk-update | Bulk Update Psv Status
*PsvApi* | [**delete_source_psv**](docs/PsvApi.md#delete_source_psv) | **DELETE** /psv/{account_id}/source/{source_id} | Delete Source Psv
*PsvApi* | [**export_psv_for_account**](docs/PsvApi.md#export_psv_for_account) | **GET** /psv/{account_id}/export | Export Psv For Account
*PsvApi* | [**get_psv**](docs/PsvApi.md#get_psv) | **GET** /psv/{account_id} | Get Psv
*PsvApi* | [**update_psv_status**](docs/PsvApi.md#update_psv_status) | **PUT** /psv/{account_id}/{psv_id} | Update Psv Status
*TrendsApi* | [**get_cases_by_risk_category**](docs/TrendsApi.md#get_cases_by_risk_category) | **GET** /trends/{account_id}/cases-by-risk-category | Get Cases By Risk Category
*TrendsApi* | [**get_cases_by_status**](docs/TrendsApi.md#get_cases_by_status) | **GET** /trends/{account_id}/cases-by-status | Get Cases By Status
*TrendsApi* | [**get_count_psv**](docs/TrendsApi.md#get_count_psv) | **GET** /trends/{account_id}/count-psv | Get Count Psv
*TrendsApi* | [**get_linddun**](docs/TrendsApi.md#get_linddun) | **GET** /trends/{account_id}/linddun | Get Linddun
*TrendsApi* | [**get_mitre**](docs/TrendsApi.md#get_mitre) | **GET** /trends/{account_id}/mitre | Get Mitre


## Documentation For Models

 - [AnalysisRecord](docs/AnalysisRecord.md)
 - [BulkUpdateCasesRequest](docs/BulkUpdateCasesRequest.md)
 - [BulkUpdatePsvRequest](docs/BulkUpdatePsvRequest.md)
 - [CaseAuditAction](docs/CaseAuditAction.md)
 - [CaseComment](docs/CaseComment.md)
 - [CaseStatus](docs/CaseStatus.md)
 - [CasesByRiskCategoryStats](docs/CasesByRiskCategoryStats.md)
 - [CasesByStatusStats](docs/CasesByStatusStats.md)
 - [CasesPerCategoryCount](docs/CasesPerCategoryCount.md)
 - [CodeType](docs/CodeType.md)
 - [ConcernType](docs/ConcernType.md)
 - [ConfidenceScoreLevel](docs/ConfidenceScoreLevel.md)
 - [ContentOutput](docs/ContentOutput.md)
 - [CreateCaseComment](docs/CreateCaseComment.md)
 - [DatePoint](docs/DatePoint.md)
 - [ExportedRecord](docs/ExportedRecord.md)
 - [ExportedSummary](docs/ExportedSummary.md)
 - [ExternalCase](docs/ExternalCase.md)
 - [ExternalCaseHistory](docs/ExternalCaseHistory.md)
 - [ExternalCaseWorkroom](docs/ExternalCaseWorkroom.md)
 - [ExternalContainer](docs/ExternalContainer.md)
 - [ExternalContainerRisk](docs/ExternalContainerRisk.md)
 - [ExternalControl](docs/ExternalControl.md)
 - [ExternalFrameworkConcern](docs/ExternalFrameworkConcern.md)
 - [ExternalImplementation](docs/ExternalImplementation.md)
 - [ExternalIssueAnalysis](docs/ExternalIssueAnalysis.md)
 - [ExternalIssueAnalysisWorkroom](docs/ExternalIssueAnalysisWorkroom.md)
 - [ExternalPrimeConcern](docs/ExternalPrimeConcern.md)
 - [GenerateRecommendationsForConcernIdsRequest](docs/GenerateRecommendationsForConcernIdsRequest.md)
 - [HTTPValidationError](docs/HTTPValidationError.md)
 - [How](docs/How.md)
 - [HowOutput](docs/HowOutput.md)
 - [ImplementationStatus](docs/ImplementationStatus.md)
 - [ImplementationStatusUpdate](docs/ImplementationStatusUpdate.md)
 - [IsAliveResponse](docs/IsAliveResponse.md)
 - [Issue](docs/Issue.md)
 - [IssueAnalysisConcernMethodology](docs/IssueAnalysisConcernMethodology.md)
 - [IssueLinkType](docs/IssueLinkType.md)
 - [IssueLinks](docs/IssueLinks.md)
 - [JobBuildFieldsDataCreateArgs](docs/JobBuildFieldsDataCreateArgs.md)
 - [JobClassificationCreateArgs](docs/JobClassificationCreateArgs.md)
 - [JobCreateArgs](docs/JobCreateArgs.md)
 - [JobCreatedResponse](docs/JobCreatedResponse.md)
 - [JobPsvCreateArgs](docs/JobPsvCreateArgs.md)
 - [JobStatus](docs/JobStatus.md)
 - [JobStatusResponse](docs/JobStatusResponse.md)
 - [JobSummaryCreateArgs](docs/JobSummaryCreateArgs.md)
 - [JobType](docs/JobType.md)
 - [JobUpdateIssuesCreateArgs](docs/JobUpdateIssuesCreateArgs.md)
 - [LocationInner](docs/LocationInner.md)
 - [MethodologyStats](docs/MethodologyStats.md)
 - [OrderBy](docs/OrderBy.md)
 - [OrderDirection](docs/OrderDirection.md)
 - [PaginationResponseExportedRecord](docs/PaginationResponseExportedRecord.md)
 - [PaginationResponseExternalCaseWorkroom](docs/PaginationResponseExternalCaseWorkroom.md)
 - [PaginationResponsePotentialSecurityViolation](docs/PaginationResponsePotentialSecurityViolation.md)
 - [PotentialSecurityViolation](docs/PotentialSecurityViolation.md)
 - [PrimeRecommendation](docs/PrimeRecommendation.md)
 - [ProviderFieldInfo](docs/ProviderFieldInfo.md)
 - [ProviderFieldInfoOptions](docs/ProviderFieldInfoOptions.md)
 - [ProviderFieldType](docs/ProviderFieldType.md)
 - [ProviderFieldsOptionsList](docs/ProviderFieldsOptionsList.md)
 - [PsvCount](docs/PsvCount.md)
 - [PsvStatus](docs/PsvStatus.md)
 - [QuestionsOutput](docs/QuestionsOutput.md)
 - [ResearchUtilsModelsContextQuote](docs/ResearchUtilsModelsContextQuote.md)
 - [ResearchUtilsModelsContextQuotesOutput](docs/ResearchUtilsModelsContextQuotesOutput.md)
 - [RiskFactorLevel](docs/RiskFactorLevel.md)
 - [RiskFactors](docs/RiskFactors.md)
 - [RiskScoreCategory](docs/RiskScoreCategory.md)
 - [SearchResponse](docs/SearchResponse.md)
 - [SecurityFramework](docs/SecurityFramework.md)
 - [SinglePsvUpdateRequest](docs/SinglePsvUpdateRequest.md)
 - [Summary5W](docs/Summary5W.md)
 - [UserImplementationStatus](docs/UserImplementationStatus.md)
 - [ValidationError](docs/ValidationError.md)
 - [What](docs/What.md)
 - [WhatOutput](docs/WhatOutput.md)
 - [Where](docs/Where.md)
 - [WhereOutput](docs/WhereOutput.md)
 - [Who](docs/Who.md)
 - [WhoOutput](docs/WhoOutput.md)
 - [Why](docs/Why.md)
 - [WhyOutput](docs/WhyOutput.md)


<a id="documentation-for-authorization"></a>
## Documentation For Authorization

Endpoints do not require authorization.


## Author




