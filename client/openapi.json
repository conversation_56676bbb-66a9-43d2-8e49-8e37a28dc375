{"openapi": "3.0.3", "info": {"title": "Service API ", "description": "Service API", "version": "1.0.0"}, "paths": {"/issues/{account_id}/{source_id}": {"get": {"tags": ["issues"], "summary": "Get-Issues-<PERSON>", "description": "Get all issues keys that scanned", "operationId": "get-issues-keys", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "since", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Since"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Get-Issues-<PERSON>"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/issues/{account_id}/children/{case_id}": {"get": {"tags": ["issues"], "summary": "Get Container Children Data", "operationId": "get_container_children_data", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "case_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Case Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Issue"}, "title": "Response Get Container Children Data"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{account_id}/{job_id}": {"get": {"tags": ["jobs"], "summary": "Get Job", "description": "Get job", "operationId": "get_job", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "job_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{account_id}": {"get": {"tags": ["jobs"], "summary": "Get Jobs", "description": "Get jobs", "operationId": "get_jobs", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "job_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/JobStatus"}}, {"type": "null"}], "title": "Job Status"}}, {"name": "source_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "exclusiveMaximum": 10000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "include_deleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include Deleted"}}, {"name": "order_by", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/OrderBy", "default": "created_at"}}, {"name": "order_direction", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/OrderDirection", "default": "desc"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobStatusResponse"}, "title": "Response Get Jobs"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jobs/{account_id}/": {"post": {"tags": ["jobs"], "summary": "Add Job", "description": "Adds a job for the given account and arguments", "operationId": "add_job", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/JobPsvCreateArgs"}, {"$ref": "#/components/schemas/JobUpdateIssuesCreateArgs"}, {"$ref": "#/components/schemas/JobBuildFieldsDataCreateArgs"}, {"$ref": "#/components/schemas/JobSummaryCreateArgs"}, {"$ref": "#/components/schemas/JobClassificationCreateArgs"}], "title": "Job Create Args"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobCreatedResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}": {"get": {"tags": ["cases"], "summary": "Get Cases For Account", "description": "Get all open security cases for account", "operationId": "get_cases_for_account", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "query_cases_view_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query Cases View Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "exclusiveMaximum": 10000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "source_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}}, {"name": "f", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter", "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, {"name": "s", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Sort", "description": "Sort string"}, "description": "Sort string"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse_ExternalCaseWorkroom_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/export": {"get": {"tags": ["cases"], "summary": "Export For Account", "description": "Export to CSV file", "operationId": "export_for_account", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "selected_columns", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Selected Columns"}}, {"name": "source_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}}, {"name": "f", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter", "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, {"name": "s", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Sort", "description": "Sort string"}, "description": "Sort string"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}": {"get": {"tags": ["cases"], "summary": "Get Cases For Account And Source", "description": "Get all open security cases for source", "operationId": "get_cases_for_account_and_source", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "exclusiveMaximum": 10000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "f", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter", "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, {"name": "s", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Sort", "description": "Sort string"}, "description": "Sort string"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse_ExternalCaseWorkroom_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["cases"], "summary": "Delete Cases For Source", "operationId": "delete_cases_for_source", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}": {"get": {"tags": ["cases"], "summary": "Get <PERSON>", "operationId": "get_case", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalCase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/case/{case_id}": {"get": {"tags": ["cases"], "summary": "Get Case By Id", "operationId": "get_case_by_id", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "case_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Case Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalCase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}/comment": {"put": {"tags": ["cases"], "summary": "Add Comment", "description": "Add comment to case", "operationId": "add_comment", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCaseComment"}}}}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}/recommendations": {"put": {"tags": ["cases"], "summary": "Update Recommendations", "description": "Update recommendations for case", "operationId": "update_recommendations", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImplementationStatusUpdate"}, "title": "Recommendations Update"}}}}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}/status": {"put": {"tags": ["cases"], "summary": "Update Status", "description": "Update status for case", "operationId": "update_status", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}, {"name": "status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CaseStatus"}}, {"name": "dismissed_reason", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dismissed Reason"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}/risk-score-category": {"put": {"tags": ["cases"], "summary": "Update Risk Score Category", "description": "Update risk score category for case", "operationId": "update_risk_score_category", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}, {"name": "risk_score_category", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/RiskScoreCategory"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}/write-back": {"post": {"tags": ["cases"], "summary": "Write Back", "description": "Write back the recommendations to source", "operationId": "write_back", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}], "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Response Write Back"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}/watcher": {"post": {"tags": ["cases"], "summary": "Add Watcher", "description": "Add watcher to case", "operationId": "add_watcher", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}, {"name": "watcher_email", "in": "query", "required": true, "schema": {"type": "string", "format": "email", "title": "Watcher <PERSON><PERSON>"}}], "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/search/{field}/{value}": {"get": {"tags": ["cases"], "summary": "Autocomplete", "description": "Get possible values for a field", "operationId": "autocomplete", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "field", "in": "path", "required": true, "schema": {"type": "string", "title": "Field"}}, {"name": "value", "in": "path", "required": true, "schema": {"type": "string", "title": "Value"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Autocomplete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/search/{value}": {"get": {"tags": ["cases"], "summary": "Autocomplete Search Global Cases", "operationId": "autocomplete_search_global_cases", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "value", "in": "path", "required": true, "schema": {"type": "string", "title": "Value"}}, {"name": "limit", "in": "query", "required": true, "schema": {"type": "integer", "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResponse"}, "title": "Response Autocomplete Search Global Cases"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/source/{source_id}/issue/{issue_id}/label": {"put": {"tags": ["cases"], "summary": "Set Labels", "description": "Add label to case", "operationId": "set_labels", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "minLength": 1, "pattern": "^[^\\s]+$"}, "title": "Labels"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/labels": {"get": {"tags": ["cases"], "summary": "Get Labels", "operationId": "get_labels", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "minLength": 1, "pattern": "^[^\\s]+$"}, "title": "Response Get Labels"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/bulk-update/{source_id}": {"post": {"tags": ["cases"], "summary": "Bulk Update Cases", "operationId": "bulk_update_cases", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateCasesRequest"}}}}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/{source_id}/{issue_id}/recommendations/generate": {"post": {"tags": ["cases"], "summary": "Generate Recommendations For Concern Ids", "description": "Generate recommendations for list of concern ids - recommendations on demand", "operationId": "generate_recommendations_for_concern_ids", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}, {"name": "created_by", "in": "query", "required": true, "schema": {"type": "string", "title": "Created By"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateRecommendationsForConcernIdsRequest"}}}}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "boolean", "title": "Response Generate Recommendations For Concern Ids"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/cases/{account_id}/{source_id}/{issue_id}/case_id": {"get": {"tags": ["cases"], "summary": "Get Case Id", "description": "Get case id for issue id and source id", "operationId": "get_case_id", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Issue Id"}}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Get Case Id"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/trends/{account_id}/cases-by-status": {"get": {"tags": ["trends"], "summary": "Get Cases By Status", "operationId": "get_cases_by_status", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Account Id"}}, {"name": "query_cases_view_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query Cases View Id"}}, {"name": "start", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time", "description": "Start date of the stats", "title": "Start"}, "description": "Start date of the stats"}, {"name": "end", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time", "description": "End date of the stats", "title": "End"}, "description": "End date of the stats"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CasesByStatusStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/trends/{account_id}/cases-by-risk-category": {"get": {"tags": ["trends"], "summary": "Get Cases By Risk Category", "operationId": "get_cases_by_risk_category", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Account Id"}}, {"name": "query_cases_view_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query Cases View Id"}}, {"name": "start", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time", "description": "Start date of the stats", "title": "Start"}, "description": "Start date of the stats"}, {"name": "end", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time", "description": "End date of the stats", "title": "End"}, "description": "End date of the stats"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CasesByRiskCategoryStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/trends/{account_id}/count-psv": {"get": {"tags": ["trends"], "summary": "Get Count Psv", "operationId": "get_count_psv", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PsvCount"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/trends/{account_id}/mitre": {"get": {"tags": ["trends"], "summary": "Get Mitre", "operationId": "get_mitre", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "query_cases_view_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query Cases View Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MethodologyStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/trends/{account_id}/linddun": {"get": {"tags": ["trends"], "summary": "<PERSON>", "operationId": "get_linddun", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "query_cases_view_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query Cases View Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MethodologyStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jira-fields/{account_id}/all/{source_id}": {"get": {"tags": ["jira-fields"], "summary": "Get All Provider Fields", "operationId": "get_all_provider_fields", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProviderFieldsOptionsList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jira-fields/{account_id}/selected/{source_id}": {"get": {"tags": ["jira-fields"], "summary": "Get Selected Provider Fields", "operationId": "get_selected_provider_fields", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderFieldInfo"}, "title": "Response Get Selected Provider Fields"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/jira-fields/{account_id}/workroom": {"get": {"tags": ["jira-fields"], "summary": "Get Workroom Fields", "operationId": "get_workroom_fields", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "is_container_view", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Container View"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderFieldInfoOptions"}, "title": "Response Get Workroom Fields"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/psv/{account_id}": {"get": {"tags": ["psv"], "summary": "Get Psv", "operationId": "get_psv", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "exclusiveMaximum": 10000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "source_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}}, {"name": "f", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter", "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, {"name": "s", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Sort", "description": "Sort string"}, "description": "Sort string"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse_PotentialSecurityViolation_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/psv/{account_id}/export": {"get": {"tags": ["psv"], "summary": "Export Psv For Account", "description": "Export to CSV file", "operationId": "export_psv_for_account", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Source Id"}}, {"name": "f", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter", "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}, "description": "Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). op is (eq|ne)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/psv/{account_id}/{psv_id}": {"put": {"tags": ["psv"], "summary": "Update Psv Status", "description": "Update the status of PSV", "operationId": "update_psv_status", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "psv_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Psv Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SinglePsvUpdateRequest"}}}}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PotentialSecurityViolation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/psv/{account_id}/bulk-update": {"post": {"tags": ["psv"], "summary": "Bulk Update Psv Status", "description": "Bulk update the status of PSV's", "operationId": "bulk_update_psv_status", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdatePsvRequest"}}}}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PotentialSecurityViolation"}, "title": "Response Bulk Update Psv Status"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/psv/{account_id}/source/{source_id}": {"delete": {"tags": ["psv"], "summary": "Delete Source Psv", "description": "Delete all PSV's for a source", "operationId": "delete_source_psv", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/containers/{account_id}/source/{source_id}/issue/{issue_id}": {"get": {"tags": ["containers"], "summary": "Get Container", "operationId": "get_container", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "source_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Source Id"}}, {"name": "issue_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Issue ID", "title": "Issue Id"}, "description": "Issue ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalContainer"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/llm-context/{account_id}/cases/{container_id}": {"get": {"tags": ["llm-context"], "summary": "Cases", "description": "LLM context on container and sorted by provider_fields.updated", "operationId": "cases", "parameters": [{"name": "account_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Account ID", "title": "Account Id"}, "description": "Account ID"}, {"name": "container_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "Container case ID to filter by", "title": "Container Id"}, "description": "Container case ID to filter by"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "exclusiveMaximum": 10000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse_ExportedRecord_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/is-alive": {"get": {"tags": ["health"], "summary": "Is Alive", "description": "returns the servers localtime, uptime and hit-counter.", "operationId": "is_alive", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsAliveResponse"}}}}}}}}, "components": {"schemas": {"AnalysisRecord": {"properties": {"availability": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Availability"}, "confidentiality": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Confidentiality"}, "integrity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Integrity"}, "risk_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Risk Score"}, "risk_score_category": {"anyOf": [{"$ref": "#/components/schemas/RiskScoreCategory"}, {"type": "null"}]}, "classification": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Classification", "default": false}, "concerns": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Concerns"}, "is_automated": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Automated"}, "is_security_enhancement": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Security Enhancement"}}, "type": "object", "title": "AnalysisRecord"}, "BulkUpdateCasesRequest": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/CaseStatus"}, {"type": "null"}]}, "dismissed_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dismissed Reason"}, "labels": {"anyOf": [{"items": {"type": "string", "minLength": 1, "pattern": "^[^\\s]+$"}, "type": "array", "uniqueItems": true}, {"type": "null"}], "title": "Labels"}, "risk_score_category": {"anyOf": [{"$ref": "#/components/schemas/RiskScoreCategory"}, {"type": "null"}]}, "issues_ids": {"items": {"type": "string"}, "type": "array", "uniqueItems": true, "title": "Issues Ids"}}, "type": "object", "required": ["issues_ids"], "title": "BulkUpdateCasesRequest"}, "BulkUpdatePsvRequest": {"properties": {"violations": {"additionalProperties": {"$ref": "#/components/schemas/SinglePsvUpdateRequest"}, "type": "object", "title": "Violations"}}, "type": "object", "required": ["violations"], "title": "BulkUpdatePsvRequest"}, "CaseAuditAction": {"type": "string", "enum": ["create_case", "user_view_case", "update_status", "override_risk_category"], "title": "CaseAuditAction"}, "CaseComment": {"properties": {"user": {"type": "string", "title": "User"}, "text": {"type": "string", "title": "Text"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "id": {"type": "integer", "title": "Id"}}, "type": "object", "required": ["user", "text", "created_at", "id"], "title": "CaseComment"}, "CaseStatus": {"type": "string", "enum": ["open", "done", "dismissed"], "title": "CaseStatus"}, "CasesByRiskCategoryStats": {"properties": {"start": {"type": "string", "format": "date-time", "title": "Start"}, "end": {"type": "string", "format": "date-time", "title": "End"}, "query_name": {"type": "string", "title": "Query Name"}, "intervene": {"items": {"$ref": "#/components/schemas/DatePoint"}, "type": "array", "minItems": 1, "title": "Intervene"}, "analyze": {"items": {"$ref": "#/components/schemas/DatePoint"}, "type": "array", "minItems": 1, "title": "Analyze"}, "monitor": {"items": {"$ref": "#/components/schemas/DatePoint"}, "type": "array", "minItems": 1, "title": "Monitor"}}, "type": "object", "required": ["start", "end", "query_name", "intervene", "analyze", "monitor"], "title": "CasesByRiskCategoryStats"}, "CasesByStatusStats": {"properties": {"start": {"type": "string", "format": "date-time", "title": "Start"}, "end": {"type": "string", "format": "date-time", "title": "End"}, "query_name": {"type": "string", "title": "Query Name"}, "scanned": {"items": {"$ref": "#/components/schemas/DatePoint"}, "type": "array", "minItems": 1, "title": "Scanned"}, "identified": {"items": {"$ref": "#/components/schemas/DatePoint"}, "type": "array", "minItems": 1, "title": "Identified"}, "close": {"items": {"$ref": "#/components/schemas/DatePoint"}, "type": "array", "minItems": 1, "title": "Close"}}, "type": "object", "required": ["start", "end", "query_name", "scanned", "identified", "close"], "title": "CasesByStatusStats"}, "CasesPerCategoryCount": {"properties": {"risk_scores": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Risk Scores"}}, "type": "object", "required": ["risk_scores"], "title": "CasesPerCategoryCount"}, "CodeType": {"type": "string", "enum": ["python", "shell", "powershell", "go", "javascript", "java", "generic"], "title": "CodeType"}, "ConcernType": {"type": "string", "enum": ["Generic", "Linddun", "<PERSON><PERSON>"], "title": "ConcernType"}, "ConfidenceScoreLevel": {"type": "string", "enum": ["low", "medium", "high"], "title": "ConfidenceScoreLevel"}, "ContentOutput": {"properties": {"text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Text"}, "quotes": {"anyOf": [{"$ref": "#/components/schemas/ResearchUtilsModelsContextQuotesOutput"}, {"type": "null"}]}}, "type": "object", "title": "ContentOutput", "description": "ContentOutput"}, "CreateCaseComment": {"properties": {"user": {"type": "string", "minLength": 2, "title": "User"}, "text": {"type": "string", "minLength": 1, "title": "Text"}}, "type": "object", "required": ["user", "text"], "title": "CreateCaseComment"}, "DatePoint": {"properties": {"x": {"type": "string", "format": "date-time", "title": "X"}, "y": {"type": "integer", "title": "Y"}}, "type": "object", "required": ["x", "y"], "title": "DatePoint"}, "ExportedRecord": {"properties": {"created_at": {"type": "string", "title": "Created At"}, "issue_id": {"type": "string", "title": "Issue Id"}, "parent_issue_id": {"type": "string", "title": "Parent Issue Id"}, "status": {"type": "string", "title": "Status"}, "analysis": {"$ref": "#/components/schemas/AnalysisRecord"}, "summary": {"$ref": "#/components/schemas/ExportedSummary"}, "provider_fields": {"additionalProperties": true, "type": "object", "title": "Provider <PERSON>"}}, "type": "object", "required": ["issue_id", "parent_issue_id", "status", "analysis", "summary", "provider_fields"], "title": "ExportedRecord"}, "ExportedSummary": {"properties": {"summary": {"type": "string", "title": "Summary", "default": "No summary found"}, "questions": {"anyOf": [{"$ref": "#/components/schemas/QuestionsOutput"}, {"type": "null"}]}, "short": {"type": "string", "title": "Short", "default": "No short summary found"}}, "type": "object", "title": "ExportedSummary"}, "ExternalCase": {"properties": {"account_id": {"type": "string", "title": "Account Id"}, "source_id": {"type": "integer", "title": "Source Id"}, "issue_id": {"type": "string", "title": "Issue Id"}, "case_id": {"type": "integer", "title": "Case Id"}, "status": {"$ref": "#/components/schemas/CaseStatus"}, "issue_analysis": {"$ref": "#/components/schemas/ExternalIssueAnalysis"}, "write_back_recommendations": {"type": "boolean", "title": "Write Back Recommendations"}, "title": {"type": "string", "title": "Title"}, "link": {"type": "string", "title": "Link"}, "labels": {"items": {"type": "string"}, "type": "array", "title": "Labels"}, "provider_fields": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Provider <PERSON>"}, "provider_fields_min_schema": {"additionalProperties": {"$ref": "#/components/schemas/ProviderFieldInfo"}, "type": "object", "title": "Provider <PERSON>"}, "parents": {"items": {"type": "string"}, "type": "array", "title": "Parents"}, "progress_percentage": {"type": "integer", "title": "Progress Percentage"}, "framework_concerns": {"additionalProperties": {"items": {"$ref": "#/components/schemas/ExternalFrameworkConcern"}, "type": "array"}, "type": "object", "title": "Framework Concerns"}, "prime_concerns": {"items": {"$ref": "#/components/schemas/ExternalPrimeConcern"}, "type": "array", "title": "Prime Concerns"}, "history": {"items": {"$ref": "#/components/schemas/ExternalCaseHistory"}, "type": "array", "title": "History"}, "comments": {"anyOf": [{"items": {"$ref": "#/components/schemas/CaseComment"}, "type": "array"}, {"type": "null"}], "title": "Comments"}}, "type": "object", "required": ["account_id", "source_id", "issue_id", "case_id", "status", "issue_analysis", "write_back_recommendations", "title", "link", "labels", "provider_fields", "progress_percentage", "framework_concerns", "prime_concerns", "history", "comments"], "title": "ExternalCase"}, "ExternalCaseHistory": {"properties": {"user": {"type": "string", "title": "User"}, "audit_action": {"$ref": "#/components/schemas/CaseAuditAction"}, "audit_action_args": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Audit Action Args"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["user", "audit_action", "audit_action_args", "created_at"], "title": "ExternalCaseHistory"}, "ExternalCaseWorkroom": {"properties": {"account_id": {"type": "string", "title": "Account Id"}, "source_id": {"type": "integer", "title": "Source Id"}, "issue_id": {"type": "string", "title": "Issue Id"}, "case_id": {"type": "integer", "title": "Case Id"}, "status": {"$ref": "#/components/schemas/CaseStatus"}, "issue_analysis": {"$ref": "#/components/schemas/ExternalIssueAnalysisWorkroom"}, "write_back_recommendations": {"type": "boolean", "title": "Write Back Recommendations"}, "title": {"type": "string", "title": "Title"}, "link": {"type": "string", "title": "Link"}, "labels": {"items": {"type": "string"}, "type": "array", "title": "Labels"}, "provider_fields": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Provider <PERSON>"}, "provider_fields_min_schema": {"additionalProperties": {"$ref": "#/components/schemas/ProviderFieldInfo"}, "type": "object", "title": "Provider <PERSON>"}, "parents": {"items": {"type": "string"}, "type": "array", "title": "Parents"}, "progress_percentage": {"type": "integer", "title": "Progress Percentage"}}, "type": "object", "required": ["account_id", "source_id", "issue_id", "case_id", "status", "issue_analysis", "write_back_recommendations", "title", "link", "labels", "provider_fields", "progress_percentage"], "title": "ExternalCaseWorkroom"}, "ExternalContainer": {"properties": {"id": {"type": "integer", "title": "Id", "default": -1}, "source_id": {"type": "integer", "title": "Source Id"}, "issue_id": {"type": "string", "title": "Issue Id"}, "risk_score": {"type": "integer", "title": "Risk Score"}, "title": {"type": "string", "title": "Title"}, "provider_fields": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Provider <PERSON>"}, "issue_summary_short": {"type": "string", "title": "Issue Summary Short"}, "issue_summary_5w": {"$ref": "#/components/schemas/Summary5W"}, "risk": {"$ref": "#/components/schemas/ExternalContainerRisk"}, "concerns": {"items": {"$ref": "#/components/schemas/ExternalPrimeConcern"}, "type": "array", "title": "Concerns"}}, "type": "object", "required": ["source_id", "issue_id", "risk_score", "title", "provider_fields", "issue_summary_short", "issue_summary_5w", "risk", "concerns"], "title": "ExternalContainer"}, "ExternalContainerRisk": {"properties": {"monitor": {"type": "integer", "title": "Monitor"}, "analyze": {"type": "integer", "title": "Analyze"}, "intervene": {"type": "integer", "title": "Intervene"}}, "type": "object", "required": ["monitor", "analyze", "intervene"], "title": "ExternalContainerRisk"}, "ExternalControl": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "control_names": {"items": {"type": "string"}, "type": "array", "title": "Control Names"}, "framework": {"$ref": "#/components/schemas/SecurityFramework"}, "implementations": {"items": {"$ref": "#/components/schemas/ExternalImplementation"}, "type": "array", "title": "Implementations"}}, "type": "object", "required": ["id", "name", "description", "control_names", "framework", "implementations"], "title": "ExternalControl"}, "ExternalFrameworkConcern": {"properties": {"id": {"type": "integer", "title": "Id"}, "short_description": {"type": "string", "title": "Short Description"}, "long_description": {"type": "string", "title": "Long Description"}, "methodology": {"$ref": "#/components/schemas/IssueAnalysisConcernMethodology"}, "controls": {"items": {"$ref": "#/components/schemas/ExternalControl"}, "type": "array", "title": "Controls"}}, "type": "object", "required": ["id", "short_description", "long_description", "methodology", "controls"], "title": "ExternalFrameworkConcern"}, "ExternalImplementation": {"properties": {"id": {"type": "integer", "title": "Id"}, "concern_id": {"type": "integer", "title": "Concern Id"}, "recommendation": {"type": "string", "title": "Recommendation"}, "status": {"$ref": "#/components/schemas/ImplementationStatus"}, "raci": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON>"}, "code_snippets": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Code Snippets"}, "control_id": {"type": "string", "title": "Control Id"}, "controls": {"additionalProperties": {"items": {"type": "string"}, "type": "array", "uniqueItems": true}, "type": "object", "title": "Controls"}}, "type": "object", "required": ["id", "concern_id", "recommendation", "status", "control_id"], "title": "ExternalImplementation"}, "ExternalIssueAnalysis": {"properties": {"account_id": {"type": "string", "title": "Account Id"}, "source_id": {"type": "integer", "title": "Source Id"}, "issue_id": {"type": "string", "title": "Issue Id"}, "risk_factors": {"$ref": "#/components/schemas/RiskFactors"}, "issue_hash": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Issue Hash"}, "confidence": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Confidence"}, "is_automated": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Automated"}, "risk_score": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Risk Score"}, "mitre_categories": {"items": {"type": "string"}, "type": "array", "title": "Mitre Categories"}, "linddun_categories": {"items": {"type": "string"}, "type": "array", "title": "Linddun Categories"}, "fire_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fire Summary"}, "long_ai_summary_5w": {"$ref": "#/components/schemas/Summary5W"}, "short_ai_summary": {"type": "string", "title": "Short Ai Summary"}, "short_assessment": {"type": "string", "title": "Short Assessment"}, "long_assessment": {"type": "string", "title": "Long Assessment"}, "issue_links": {"items": {"$ref": "#/components/schemas/IssueLinks"}, "type": "array", "title": "Issue Links"}, "keywords": {"items": {"type": "string"}, "type": "array", "title": "Keywords"}, "risk_score_category": {"$ref": "#/components/schemas/RiskScoreCategory", "readOnly": true}, "confidence_level": {"$ref": "#/components/schemas/ConfidenceScoreLevel", "readOnly": true}}, "type": "object", "required": ["account_id", "source_id", "issue_id", "risk_factors", "issue_hash", "is_automated", "long_ai_summary_5w", "short_ai_summary", "short_assessment", "long_assessment", "risk_score_category", "confidence_level"], "title": "ExternalIssueAnalysis"}, "ExternalIssueAnalysisWorkroom": {"properties": {"account_id": {"type": "string", "title": "Account Id"}, "source_id": {"type": "integer", "title": "Source Id"}, "issue_id": {"type": "string", "title": "Issue Id"}, "risk_factors": {"$ref": "#/components/schemas/RiskFactors"}, "issue_hash": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Issue Hash"}, "confidence": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Confidence"}, "is_automated": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Automated"}, "risk_score": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Risk Score"}, "mitre_categories": {"items": {"type": "string"}, "type": "array", "title": "Mitre Categories"}, "linddun_categories": {"items": {"type": "string"}, "type": "array", "title": "Linddun Categories"}, "fire_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fire Summary"}, "risk_score_category": {"$ref": "#/components/schemas/RiskScoreCategory", "readOnly": true}, "confidence_level": {"$ref": "#/components/schemas/ConfidenceScoreLevel", "readOnly": true}}, "type": "object", "required": ["account_id", "source_id", "issue_id", "risk_factors", "issue_hash", "is_automated", "risk_score_category", "confidence_level"], "title": "ExternalIssueAnalysisWorkroom"}, "ExternalPrimeConcern": {"properties": {"id": {"type": "integer", "title": "Id"}, "short_description": {"type": "string", "title": "Short Description"}, "long_description": {"type": "string", "title": "Long Description"}, "methodology": {"$ref": "#/components/schemas/IssueAnalysisConcernMethodology"}, "recommendations": {"items": {"$ref": "#/components/schemas/PrimeRecommendation"}, "type": "array", "title": "Recommendations"}}, "type": "object", "required": ["id", "short_description", "long_description", "methodology", "recommendations"], "title": "ExternalPrimeConcern"}, "GenerateRecommendationsForConcernIdsRequest": {"properties": {"concern_ids": {"items": {"type": "integer"}, "type": "array", "uniqueItems": true, "title": "Concern Ids"}}, "type": "object", "required": ["concern_ids"], "title": "GenerateRecommendationsForConcernIdsRequest"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HowOutput": {"properties": {"approach": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}, "acceptance": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}}, "type": "object", "required": ["approach", "acceptance"], "title": "HowOutput", "description": "HowOutput"}, "ImplementationStatus": {"type": "string", "enum": ["unknown", "approved", "dismissed"], "title": "ImplementationStatus"}, "ImplementationStatusUpdate": {"properties": {"id": {"type": "integer", "title": "Id"}, "status": {"$ref": "#/components/schemas/UserImplementationStatus"}, "concern_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Concern Id"}, "control_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Control Id"}}, "type": "object", "required": ["id", "status"], "title": "ImplementationStatusUpdate"}, "IsAliveResponse": {"properties": {"now": {"type": "string", "format": "date-time", "title": "Now"}, "uptime": {"type": "string", "title": "Uptime"}, "hit_count": {"type": "integer", "title": "Hit Count"}}, "type": "object", "required": ["now", "uptime", "hit_count"], "title": "IsAliveResponse"}, "Issue": {"properties": {"id": {"type": "string", "title": "Id"}, "summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Summary"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "hierarchy_level": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Hierarchy Level"}, "parent": {"anyOf": [{"$ref": "#/components/schemas/Issue"}, {"type": "null"}]}, "parent_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON> Summary"}}, "type": "object", "required": ["id", "type"], "title": "Issue", "description": "Issue"}, "IssueAnalysisConcernMethodology": {"properties": {"category": {"type": "string", "title": "Category"}, "type": {"$ref": "#/components/schemas/ConcernType"}}, "type": "object", "required": ["category", "type"], "title": "IssueAnalysisConcernMethodology"}, "IssueLinkType": {"type": "string", "enum": ["confluence", "gdrive", "unknown", "jira"], "title": "IssueLinkType"}, "IssueLinks": {"properties": {"url": {"type": "string", "title": "Url"}, "link_type": {"anyOf": [{"$ref": "#/components/schemas/IssueLinkType"}, {"type": "null"}]}}, "type": "object", "required": ["url", "link_type"], "title": "IssueLinks"}, "JobBuildFieldsDataCreateArgs": {"properties": {"job": {"$ref": "#/components/schemas/JobType", "default": "build_fields_data"}, "created_by": {"type": "string", "title": "Created By"}, "source_id": {"type": "integer", "title": "Source Id"}}, "type": "object", "required": ["created_by", "source_id"], "title": "JobBuildFieldsDataCreateArgs"}, "JobClassificationCreateArgs": {"properties": {"job": {"$ref": "#/components/schemas/JobType", "default": "classification"}, "created_by": {"type": "string", "title": "Created By"}, "source_id": {"type": "integer", "title": "Source Id"}, "force": {"type": "boolean", "title": "Force", "default": false}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id"}}, "type": "object", "required": ["created_by", "source_id"], "title": "JobClassificationCreateArgs"}, "JobCreatedResponse": {"properties": {"job_id": {"type": "integer", "title": "Job Id"}, "status": {"type": "string", "title": "Status"}}, "type": "object", "required": ["job_id", "status"], "title": "JobCreatedResponse"}, "JobPsvCreateArgs": {"properties": {"job": {"$ref": "#/components/schemas/JobType", "default": "security_violation"}, "created_by": {"type": "string", "title": "Created By"}, "source_id": {"type": "integer", "title": "Source Id"}, "force": {"type": "boolean", "title": "Force", "default": false}}, "type": "object", "required": ["created_by", "source_id"], "title": "JobPsvCreateArgs"}, "JobStatus": {"type": "string", "enum": ["SCHEDULED", "PENDING", "RUNNING", "FINALIZING", "COMPLETED", "FAILED", "CANCELED"], "title": "JobStatus"}, "JobStatusResponse": {"properties": {"name": {"$ref": "#/components/schemas/JobType"}, "status": {"$ref": "#/components/schemas/JobStatus"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "progress": {"type": "integer", "title": "Progress"}, "source_id": {"type": "integer", "title": "Source Id"}, "created_by": {"type": "string", "title": "Created By"}}, "type": "object", "required": ["name", "status", "error", "id", "created_at", "progress", "source_id", "created_by"], "title": "JobStatusResponse"}, "JobSummaryCreateArgs": {"properties": {"job": {"$ref": "#/components/schemas/JobType", "default": "summary"}, "created_by": {"type": "string", "title": "Created By"}, "source_id": {"type": "integer", "title": "Source Id"}, "force": {"type": "boolean", "title": "Force", "default": false}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id"}}, "type": "object", "required": ["created_by", "source_id"], "title": "JobSummaryCreateArgs"}, "JobType": {"type": "string", "enum": ["classification", "update_issues", "build_fields_data", "summary", "security_violation"], "title": "JobType"}, "JobUpdateIssuesCreateArgs": {"properties": {"job": {"$ref": "#/components/schemas/JobType", "default": "update_issues"}, "created_by": {"type": "string", "title": "Created By"}, "source_id": {"type": "integer", "title": "Source Id"}, "force": {"type": "boolean", "title": "Force", "default": false}, "update_fields_only": {"type": "boolean", "title": "Update Fields Only", "default": false}}, "type": "object", "required": ["created_by", "source_id"], "title": "JobUpdateIssuesCreateArgs"}, "MethodologyStats": {"properties": {"start": {"type": "string", "format": "date-time", "title": "Start"}, "end": {"type": "string", "format": "date-time", "title": "End"}, "query_name": {"type": "string", "title": "Query Name"}, "categories": {"additionalProperties": {"$ref": "#/components/schemas/CasesPerCategoryCount"}, "type": "object", "title": "Categories"}}, "type": "object", "required": ["start", "end", "query_name", "categories"], "title": "MethodologyStats"}, "OrderBy": {"type": "string", "enum": ["created_at", "modified_at", "deleted_at"], "title": "OrderBy"}, "OrderDirection": {"type": "string", "enum": ["asc", "desc"], "title": "OrderDirection"}, "PaginationResponse_ExportedRecord_": {"properties": {"results": {"items": {"$ref": "#/components/schemas/ExportedRecord"}, "type": "array", "title": "Results"}, "size": {"type": "integer", "title": "Size"}, "limit": {"type": "integer", "title": "Limit"}, "start": {"type": "integer", "title": "Start"}, "total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total"}, "has_next": {"type": "boolean", "title": "Has Next", "default": false}}, "type": "object", "required": ["results", "size", "limit", "start"], "title": "PaginationResponse[ExportedRecord]"}, "PaginationResponse_ExternalCaseWorkroom_": {"properties": {"results": {"items": {"$ref": "#/components/schemas/ExternalCaseWorkroom"}, "type": "array", "title": "Results"}, "size": {"type": "integer", "title": "Size"}, "limit": {"type": "integer", "title": "Limit"}, "start": {"type": "integer", "title": "Start"}, "total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total"}, "has_next": {"type": "boolean", "title": "Has Next", "default": false}}, "type": "object", "required": ["results", "size", "limit", "start"], "title": "PaginationResponse[ExternalCaseWorkroom]"}, "PaginationResponse_PotentialSecurityViolation_": {"properties": {"results": {"items": {"$ref": "#/components/schemas/PotentialSecurityViolation"}, "type": "array", "title": "Results"}, "size": {"type": "integer", "title": "Size"}, "limit": {"type": "integer", "title": "Limit"}, "start": {"type": "integer", "title": "Start"}, "total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total"}, "has_next": {"type": "boolean", "title": "Has Next", "default": false}}, "type": "object", "required": ["results", "size", "limit", "start"], "title": "PaginationResponse[PotentialSecurityViolation]"}, "PotentialSecurityViolation": {"properties": {"psv_id": {"type": "integer", "title": "Psv Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "type": {"type": "string", "title": "Type"}, "source_id": {"type": "integer", "title": "Source Id"}, "issue_id": {"type": "string", "title": "Issue Id"}, "status": {"$ref": "#/components/schemas/PsvStatus"}, "dismissed_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dismissed Reason"}, "project": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Project"}, "reporter": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reporter"}, "detection_date": {"type": "string", "format": "date-time", "title": "Detection Date"}, "issue_link": {"type": "string", "title": "Issue Link"}}, "type": "object", "required": ["psv_id", "created_at", "title", "description", "type", "source_id", "issue_id", "status", "dismissed_reason", "project", "reporter", "detection_date", "issue_link"], "title": "PotentialSecurityViolation"}, "PrimeRecommendation": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "implementations": {"items": {"$ref": "#/components/schemas/ExternalImplementation"}, "type": "array", "title": "Implementations"}}, "type": "object", "required": ["id", "name", "description", "implementations"], "title": "PrimeRecommendation"}, "ProviderFieldInfo": {"properties": {"type": {"$ref": "#/components/schemas/ProviderFieldType"}, "id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["type", "id", "name"], "title": "ProviderFieldInfo"}, "ProviderFieldInfoOptions": {"properties": {"type": {"$ref": "#/components/schemas/ProviderFieldType"}, "id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "options": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Options"}}, "type": "object", "required": ["type", "id", "name"], "title": "ProviderFieldInfoOptions"}, "ProviderFieldType": {"type": "string", "enum": ["string", "number", "boolean", "date", "array", "enum"], "title": "ProviderFieldType"}, "ProviderFieldsOptionsList": {"properties": {"fields": {"items": {"$ref": "#/components/schemas/ProviderFieldInfoOptions"}, "type": "array", "title": "Fields"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["fields", "total"], "title": "ProviderFieldsOptionsList"}, "PsvCount": {"properties": {"count": {"type": "integer", "title": "Count"}, "by_type": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "By Type"}}, "type": "object", "required": ["count", "by_type"], "title": "PsvCount"}, "PsvStatus": {"type": "string", "enum": ["open", "done", "dismissed"], "title": "PsvStatus"}, "QuestionsOutput": {"properties": {"what": {"$ref": "#/components/schemas/WhatOutput"}, "who": {"$ref": "#/components/schemas/WhoOutput"}, "where": {"$ref": "#/components/schemas/WhereOutput"}, "why": {"$ref": "#/components/schemas/WhyOutput"}, "how": {"$ref": "#/components/schemas/HowOutput"}}, "type": "object", "required": ["what", "who", "where", "why", "how"], "title": "QuestionsOutput", "description": "QuestionsOutput"}, "ResearchUtilsModelsContextQuote": {"properties": {"quote_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Quote Text"}, "source": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source"}}, "type": "object", "title": "ResearchUtilsModelsContextQuote", "description": "ResearchUtilsModelsContextQuote"}, "ResearchUtilsModelsContextQuotesOutput": {"properties": {"quote": {"anyOf": [{"items": {"$ref": "#/components/schemas/ResearchUtilsModelsContextQuote"}, "type": "array"}, {"type": "null"}], "title": "Quote"}}, "type": "object", "title": "ResearchUtilsModelsContextQuotesOutput", "description": "ResearchUtilsModelsContextQuotesOutput"}, "RiskFactorLevel": {"type": "string", "enum": ["low", "medium", "high"], "title": "RiskFactorLevel"}, "RiskFactors": {"properties": {"confidentiality": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Confidentiality"}, "confidentiality_explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Confidentiality Explanation"}, "integrity": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Integrity"}, "integrity_explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Integrity Explanation"}, "availability": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Availability"}, "availability_explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Availability Explanation"}, "third_party_management": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Third Party Management"}, "third_party_management_explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Third Party Management Explanation"}, "compliance": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Compliance"}, "compliance_explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Compliance Explanation"}, "severity": {"anyOf": [{"type": "integer", "maximum": 3.0, "minimum": 1.0}, {"type": "null"}], "title": "Severity"}, "severity_explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Severity Explanation"}, "scope": {"anyOf": [{"type": "integer", "maximum": 3.0, "minimum": 1.0}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "scope_explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Scope Explanation"}, "confidentiality_level": {"anyOf": [{"$ref": "#/components/schemas/RiskFactorLevel"}, {"type": "null"}], "readOnly": true}, "integrity_level": {"anyOf": [{"$ref": "#/components/schemas/RiskFactorLevel"}, {"type": "null"}], "readOnly": true}, "availability_level": {"anyOf": [{"$ref": "#/components/schemas/RiskFactorLevel"}, {"type": "null"}], "readOnly": true}, "third_party_management_level": {"anyOf": [{"$ref": "#/components/schemas/RiskFactorLevel"}, {"type": "null"}], "readOnly": true}, "compliance_level": {"anyOf": [{"$ref": "#/components/schemas/RiskFactorLevel"}, {"type": "null"}], "readOnly": true}}, "type": "object", "required": ["confidentiality_level", "integrity_level", "availability_level", "third_party_management_level", "compliance_level"], "title": "RiskFactors"}, "RiskScoreCategory": {"type": "string", "enum": ["intervene", "analyze", "monitor", "None"], "title": "RiskScoreCategory"}, "SearchResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "issue_id": {"type": "string", "title": "Issue Id"}, "title": {"type": "string", "title": "Title"}, "issue_type": {"type": "string", "title": "Issue Type"}, "is_container": {"type": "boolean", "title": "Is Container"}, "source_id": {"type": "integer", "title": "Source Id"}}, "type": "object", "required": ["id", "issue_id", "title", "issue_type", "is_container", "source_id"], "title": "SearchResponse"}, "SecurityFramework": {"type": "string", "enum": ["NIST", "HITRUST", "PCI", "CIS", "PRIME"], "title": "SecurityFramework"}, "SinglePsvUpdateRequest": {"properties": {"new_status": {"$ref": "#/components/schemas/PsvStatus"}, "dismissed_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dismissed Reason"}}, "type": "object", "required": ["new_status"], "title": "SinglePsvUpdateRequest"}, "Summary5W": {"properties": {"what": {"$ref": "#/components/schemas/_What"}, "where": {"$ref": "#/components/schemas/_Where"}, "who": {"$ref": "#/components/schemas/_Who"}, "why": {"$ref": "#/components/schemas/_Why"}, "how": {"$ref": "#/components/schemas/_How"}}, "type": "object", "required": ["what", "where", "who", "why", "how"], "title": "Summary5W"}, "UserImplementationStatus": {"type": "string", "enum": ["approved", "dismissed"], "title": "UserImplementationStatus"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WhatOutput": {"properties": {"summary": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}, "description": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}}, "type": "object", "required": ["summary", "description"], "title": "WhatOutput", "description": "WhatOutput"}, "WhereOutput": {"properties": {"environment": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}, "components": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}, "products": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}}, "type": "object", "required": ["environment", "components", "products"], "title": "WhereOutput", "description": "WhereOutput"}, "WhoOutput": {"properties": {"stakeholders": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}, "affected": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}}, "type": "object", "required": ["stakeholders", "affected"], "title": "WhoOutput", "description": "WhoOutput"}, "WhyOutput": {"properties": {"purpose": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}, "impact": {"anyOf": [{"$ref": "#/components/schemas/ContentOutput"}, {"type": "null"}]}}, "type": "object", "required": ["purpose", "impact"], "title": "WhyOutput", "description": "WhyOutput"}, "_How": {"properties": {"approach": {"type": "string", "title": "Approach"}, "acceptance": {"type": "string", "title": "Acceptance"}}, "type": "object", "required": ["approach", "acceptance"], "title": "_How"}, "_What": {"properties": {"summary": {"type": "string", "title": "Summary"}, "description": {"type": "string", "title": "Description"}}, "type": "object", "required": ["summary", "description"], "title": "_What"}, "_Where": {"properties": {"environment": {"type": "string", "title": "Environment"}, "components": {"type": "string", "title": "Components"}, "products": {"type": "string", "title": "Products"}}, "type": "object", "required": ["environment", "components", "products"], "title": "_Where"}, "_Who": {"properties": {"stakeholders": {"type": "string", "title": "Stakeholders"}, "affected": {"type": "string", "title": "Affected"}}, "type": "object", "required": ["stakeholders", "affected"], "title": "_Who"}, "_Why": {"properties": {"purpose": {"type": "string", "title": "Purpose"}, "impact": {"type": "string", "title": "Impact"}}, "type": "object", "required": ["purpose", "impact"], "title": "_Why"}}}}