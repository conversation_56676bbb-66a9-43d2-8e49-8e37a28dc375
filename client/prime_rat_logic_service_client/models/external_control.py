# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr
from typing import Any, ClassVar, Dict, List
from prime_rat_logic_service_client.models.external_implementation import ExternalImplementation
from prime_rat_logic_service_client.models.security_framework import SecurityFramework
from typing import Optional, Set
from typing_extensions import Self

class ExternalControl(BaseModel):
    """
    ExternalControl
    """ # noqa: E501
    id: StrictStr
    name: StrictStr
    description: StrictStr
    control_names: List[StrictStr]
    framework: SecurityFramework
    implementations: List[ExternalImplementation]
    __properties: ClassVar[List[str]] = ["id", "name", "description", "control_names", "framework", "implementations"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ExternalControl from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in implementations (list)
        _items = []
        if self.implementations:
            for _item_implementations in self.implementations:
                if _item_implementations:
                    _items.append(_item_implementations.to_dict())
            _dict['implementations'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ExternalControl from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "name": obj.get("name"),
            "description": obj.get("description"),
            "control_names": obj.get("control_names"),
            "framework": obj.get("framework"),
            "implementations": [ExternalImplementation.from_dict(_item) for _item in obj["implementations"]] if obj.get("implementations") is not None else None
        })
        return _obj


