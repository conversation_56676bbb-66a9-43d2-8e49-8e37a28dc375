# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict
from typing import Any, ClassVar, Dict, List
from prime_rat_logic_service_client.models.how import How
from prime_rat_logic_service_client.models.what import What
from prime_rat_logic_service_client.models.where import Where
from prime_rat_logic_service_client.models.who import Who
from prime_rat_logic_service_client.models.why import Why
from typing import Optional, Set
from typing_extensions import Self

class Summary5W(BaseModel):
    """
    Summary5W
    """ # noqa: E501
    what: What
    where: Where
    who: Who
    why: Why
    how: How
    __properties: ClassVar[List[str]] = ["what", "where", "who", "why", "how"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of Summary5W from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of what
        if self.what:
            _dict['what'] = self.what.to_dict()
        # override the default output from pydantic by calling `to_dict()` of where
        if self.where:
            _dict['where'] = self.where.to_dict()
        # override the default output from pydantic by calling `to_dict()` of who
        if self.who:
            _dict['who'] = self.who.to_dict()
        # override the default output from pydantic by calling `to_dict()` of why
        if self.why:
            _dict['why'] = self.why.to_dict()
        # override the default output from pydantic by calling `to_dict()` of how
        if self.how:
            _dict['how'] = self.how.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of Summary5W from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "what": What.from_dict(obj["what"]) if obj.get("what") is not None else None,
            "where": Where.from_dict(obj["where"]) if obj.get("where") is not None else None,
            "who": Who.from_dict(obj["who"]) if obj.get("who") is not None else None,
            "why": Why.from_dict(obj["why"]) if obj.get("why") is not None else None,
            "how": How.from_dict(obj["how"]) if obj.get("how") is not None else None
        })
        return _obj


