# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict
from typing import Any, ClassVar, Dict, List, Optional
from prime_rat_logic_service_client.models.content_output import ContentOutput
from typing import Optional, Set
from typing_extensions import Self

class WhoOutput(BaseModel):
    """
    WhoOutput
    """ # noqa: E501
    stakeholders: Optional[ContentOutput]
    affected: Optional[ContentOutput]
    __properties: ClassVar[List[str]] = ["stakeholders", "affected"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of WhoOutput from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of stakeholders
        if self.stakeholders:
            _dict['stakeholders'] = self.stakeholders.to_dict()
        # override the default output from pydantic by calling `to_dict()` of affected
        if self.affected:
            _dict['affected'] = self.affected.to_dict()
        # set to None if stakeholders (nullable) is None
        # and model_fields_set contains the field
        if self.stakeholders is None and "stakeholders" in self.model_fields_set:
            _dict['stakeholders'] = None

        # set to None if affected (nullable) is None
        # and model_fields_set contains the field
        if self.affected is None and "affected" in self.model_fields_set:
            _dict['affected'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of WhoOutput from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "stakeholders": ContentOutput.from_dict(obj["stakeholders"]) if obj.get("stakeholders") is not None else None,
            "affected": ContentOutput.from_dict(obj["affected"]) if obj.get("affected") is not None else None
        })
        return _obj


