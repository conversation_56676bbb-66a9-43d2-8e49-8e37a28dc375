# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class Issue(BaseModel):
    """
    Issue
    """ # noqa: E501
    id: StrictStr
    summary: Optional[StrictStr] = None
    description: Optional[StrictStr] = None
    type: Optional[StrictStr]
    hierarchy_level: Optional[StrictInt] = None
    parent: Optional[Issue] = None
    parent_summary: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["id", "summary", "description", "type", "hierarchy_level", "parent", "parent_summary"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of Issue from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of parent
        if self.parent:
            _dict['parent'] = self.parent.to_dict()
        # set to None if summary (nullable) is None
        # and model_fields_set contains the field
        if self.summary is None and "summary" in self.model_fields_set:
            _dict['summary'] = None

        # set to None if description (nullable) is None
        # and model_fields_set contains the field
        if self.description is None and "description" in self.model_fields_set:
            _dict['description'] = None

        # set to None if type (nullable) is None
        # and model_fields_set contains the field
        if self.type is None and "type" in self.model_fields_set:
            _dict['type'] = None

        # set to None if hierarchy_level (nullable) is None
        # and model_fields_set contains the field
        if self.hierarchy_level is None and "hierarchy_level" in self.model_fields_set:
            _dict['hierarchy_level'] = None

        # set to None if parent (nullable) is None
        # and model_fields_set contains the field
        if self.parent is None and "parent" in self.model_fields_set:
            _dict['parent'] = None

        # set to None if parent_summary (nullable) is None
        # and model_fields_set contains the field
        if self.parent_summary is None and "parent_summary" in self.model_fields_set:
            _dict['parent_summary'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of Issue from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "summary": obj.get("summary"),
            "description": obj.get("description"),
            "type": obj.get("type"),
            "hierarchy_level": obj.get("hierarchy_level"),
            "parent": Issue.from_dict(obj["parent"]) if obj.get("parent") is not None else None,
            "parent_summary": obj.get("parent_summary")
        })
        return _obj

# TODO: Rewrite to not use raise_errors
Issue.model_rebuild(raise_errors=False)

