# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from prime_rat_logic_service_client.models.case_status import CaseStatus
from prime_rat_logic_service_client.models.risk_score_category import RiskScoreCategory
from typing import Optional, Set
from typing_extensions import Self

class BulkUpdateCasesRequest(BaseModel):
    """
    BulkUpdateCasesRequest
    """ # noqa: E501
    status: Optional[CaseStatus] = None
    dismissed_reason: Optional[StrictStr] = None
    labels: Optional[List[Annotated[str, Field(min_length=1, strict=True)]]] = None
    risk_score_category: Optional[RiskScoreCategory] = None
    issues_ids: List[StrictStr]
    __properties: ClassVar[List[str]] = ["status", "dismissed_reason", "labels", "risk_score_category", "issues_ids"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of BulkUpdateCasesRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # set to None if status (nullable) is None
        # and model_fields_set contains the field
        if self.status is None and "status" in self.model_fields_set:
            _dict['status'] = None

        # set to None if dismissed_reason (nullable) is None
        # and model_fields_set contains the field
        if self.dismissed_reason is None and "dismissed_reason" in self.model_fields_set:
            _dict['dismissed_reason'] = None

        # set to None if labels (nullable) is None
        # and model_fields_set contains the field
        if self.labels is None and "labels" in self.model_fields_set:
            _dict['labels'] = None

        # set to None if risk_score_category (nullable) is None
        # and model_fields_set contains the field
        if self.risk_score_category is None and "risk_score_category" in self.model_fields_set:
            _dict['risk_score_category'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of BulkUpdateCasesRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "status": obj.get("status"),
            "dismissed_reason": obj.get("dismissed_reason"),
            "labels": obj.get("labels"),
            "risk_score_category": obj.get("risk_score_category"),
            "issues_ids": obj.get("issues_ids")
        })
        return _obj


