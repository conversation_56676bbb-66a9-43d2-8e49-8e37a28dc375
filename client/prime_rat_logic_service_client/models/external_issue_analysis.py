# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from prime_rat_logic_service_client.models.confidence_score_level import ConfidenceScoreLevel
from prime_rat_logic_service_client.models.issue_links import IssueLinks
from prime_rat_logic_service_client.models.risk_factors import RiskFactors
from prime_rat_logic_service_client.models.risk_score_category import RiskScoreCategory
from prime_rat_logic_service_client.models.summary5_w import Summary5W
from typing import Optional, Set
from typing_extensions import Self

class ExternalIssueAnalysis(BaseModel):
    """
    ExternalIssueAnalysis
    """ # noqa: E501
    account_id: StrictStr
    source_id: StrictInt
    issue_id: StrictStr
    risk_factors: RiskFactors
    issue_hash: Optional[StrictStr]
    confidence: Optional[Annotated[int, Field(le=100, strict=True, ge=0)]] = None
    is_automated: Optional[StrictBool]
    risk_score: Optional[Annotated[int, Field(le=100, strict=True, ge=0)]] = None
    mitre_categories: Optional[List[StrictStr]] = None
    linddun_categories: Optional[List[StrictStr]] = None
    fire_summary: Optional[StrictStr] = None
    long_ai_summary_5w: Summary5W
    short_ai_summary: StrictStr
    short_assessment: StrictStr
    long_assessment: StrictStr
    issue_links: Optional[List[IssueLinks]] = None
    keywords: Optional[List[StrictStr]] = None
    risk_score_category: RiskScoreCategory
    confidence_level: ConfidenceScoreLevel
    __properties: ClassVar[List[str]] = ["account_id", "source_id", "issue_id", "risk_factors", "issue_hash", "confidence", "is_automated", "risk_score", "mitre_categories", "linddun_categories", "fire_summary", "long_ai_summary_5w", "short_ai_summary", "short_assessment", "long_assessment", "issue_links", "keywords", "risk_score_category", "confidence_level"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ExternalIssueAnalysis from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of risk_factors
        if self.risk_factors:
            _dict['risk_factors'] = self.risk_factors.to_dict()
        # override the default output from pydantic by calling `to_dict()` of long_ai_summary_5w
        if self.long_ai_summary_5w:
            _dict['long_ai_summary_5w'] = self.long_ai_summary_5w.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in issue_links (list)
        _items = []
        if self.issue_links:
            for _item_issue_links in self.issue_links:
                if _item_issue_links:
                    _items.append(_item_issue_links.to_dict())
            _dict['issue_links'] = _items
        # set to None if issue_hash (nullable) is None
        # and model_fields_set contains the field
        if self.issue_hash is None and "issue_hash" in self.model_fields_set:
            _dict['issue_hash'] = None

        # set to None if confidence (nullable) is None
        # and model_fields_set contains the field
        if self.confidence is None and "confidence" in self.model_fields_set:
            _dict['confidence'] = None

        # set to None if is_automated (nullable) is None
        # and model_fields_set contains the field
        if self.is_automated is None and "is_automated" in self.model_fields_set:
            _dict['is_automated'] = None

        # set to None if risk_score (nullable) is None
        # and model_fields_set contains the field
        if self.risk_score is None and "risk_score" in self.model_fields_set:
            _dict['risk_score'] = None

        # set to None if fire_summary (nullable) is None
        # and model_fields_set contains the field
        if self.fire_summary is None and "fire_summary" in self.model_fields_set:
            _dict['fire_summary'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ExternalIssueAnalysis from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "account_id": obj.get("account_id"),
            "source_id": obj.get("source_id"),
            "issue_id": obj.get("issue_id"),
            "risk_factors": RiskFactors.from_dict(obj["risk_factors"]) if obj.get("risk_factors") is not None else None,
            "issue_hash": obj.get("issue_hash"),
            "confidence": obj.get("confidence"),
            "is_automated": obj.get("is_automated"),
            "risk_score": obj.get("risk_score"),
            "mitre_categories": obj.get("mitre_categories"),
            "linddun_categories": obj.get("linddun_categories"),
            "fire_summary": obj.get("fire_summary"),
            "long_ai_summary_5w": Summary5W.from_dict(obj["long_ai_summary_5w"]) if obj.get("long_ai_summary_5w") is not None else None,
            "short_ai_summary": obj.get("short_ai_summary"),
            "short_assessment": obj.get("short_assessment"),
            "long_assessment": obj.get("long_assessment"),
            "issue_links": [IssueLinks.from_dict(_item) for _item in obj["issue_links"]] if obj.get("issue_links") is not None else None,
            "keywords": obj.get("keywords"),
            "risk_score_category": obj.get("risk_score_category"),
            "confidence_level": obj.get("confidence_level")
        })
        return _obj


