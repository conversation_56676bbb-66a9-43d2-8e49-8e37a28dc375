# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from prime_rat_logic_service_client.models.implementation_status import ImplementationStatus
from typing import Optional, Set
from typing_extensions import Self

class ExternalImplementation(BaseModel):
    """
    ExternalImplementation
    """ # noqa: E501
    id: StrictInt
    concern_id: StrictInt
    recommendation: StrictStr
    status: ImplementationStatus
    raci: Optional[List[StrictStr]] = None
    code_snippets: Optional[Dict[str, StrictStr]] = None
    control_id: StrictStr
    controls: Optional[Dict[str, List[StrictStr]]] = None
    __properties: ClassVar[List[str]] = ["id", "concern_id", "recommendation", "status", "raci", "code_snippets", "control_id", "controls"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ExternalImplementation from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ExternalImplementation from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "concern_id": obj.get("concern_id"),
            "recommendation": obj.get("recommendation"),
            "status": obj.get("status"),
            "raci": obj.get("raci"),
            "code_snippets": obj.get("code_snippets"),
            "control_id": obj.get("control_id"),
            "controls": obj.get("controls")
        })
        return _obj


