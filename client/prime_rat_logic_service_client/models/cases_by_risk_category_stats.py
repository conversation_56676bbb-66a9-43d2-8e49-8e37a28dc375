# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List
from typing_extensions import Annotated
from prime_rat_logic_service_client.models.date_point import DatePoint
from typing import Optional, Set
from typing_extensions import Self

class CasesByRiskCategoryStats(BaseModel):
    """
    CasesByRiskCategoryStats
    """ # noqa: E501
    start: datetime
    end: datetime
    query_name: StrictStr
    intervene: Annotated[List[DatePoint], Field(min_length=1)]
    analyze: Annotated[List[DatePoint], Field(min_length=1)]
    monitor: Annotated[List[DatePoint], Field(min_length=1)]
    __properties: ClassVar[List[str]] = ["start", "end", "query_name", "intervene", "analyze", "monitor"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CasesByRiskCategoryStats from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in intervene (list)
        _items = []
        if self.intervene:
            for _item_intervene in self.intervene:
                if _item_intervene:
                    _items.append(_item_intervene.to_dict())
            _dict['intervene'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in analyze (list)
        _items = []
        if self.analyze:
            for _item_analyze in self.analyze:
                if _item_analyze:
                    _items.append(_item_analyze.to_dict())
            _dict['analyze'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in monitor (list)
        _items = []
        if self.monitor:
            for _item_monitor in self.monitor:
                if _item_monitor:
                    _items.append(_item_monitor.to_dict())
            _dict['monitor'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CasesByRiskCategoryStats from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "start": obj.get("start"),
            "end": obj.get("end"),
            "query_name": obj.get("query_name"),
            "intervene": [DatePoint.from_dict(_item) for _item in obj["intervene"]] if obj.get("intervene") is not None else None,
            "analyze": [DatePoint.from_dict(_item) for _item in obj["analyze"]] if obj.get("analyze") is not None else None,
            "monitor": [DatePoint.from_dict(_item) for _item in obj["monitor"]] if obj.get("monitor") is not None else None
        })
        return _obj


