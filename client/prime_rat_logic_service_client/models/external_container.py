# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from prime_rat_logic_service_client.models.external_container_risk import ExternalContainerRisk
from prime_rat_logic_service_client.models.external_prime_concern import ExternalPrimeConcern
from prime_rat_logic_service_client.models.summary5_w import Summary5W
from typing import Optional, Set
from typing_extensions import Self

class ExternalContainer(BaseModel):
    """
    ExternalContainer
    """ # noqa: E501
    id: Optional[StrictInt] = -1
    source_id: StrictInt
    issue_id: StrictStr
    risk_score: StrictInt
    title: StrictStr
    provider_fields: Dict[str, StrictStr]
    issue_summary_short: StrictStr
    issue_summary_5w: Summary5W
    risk: ExternalContainerRisk
    concerns: List[ExternalPrimeConcern]
    __properties: ClassVar[List[str]] = ["id", "source_id", "issue_id", "risk_score", "title", "provider_fields", "issue_summary_short", "issue_summary_5w", "risk", "concerns"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ExternalContainer from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of issue_summary_5w
        if self.issue_summary_5w:
            _dict['issue_summary_5w'] = self.issue_summary_5w.to_dict()
        # override the default output from pydantic by calling `to_dict()` of risk
        if self.risk:
            _dict['risk'] = self.risk.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in concerns (list)
        _items = []
        if self.concerns:
            for _item_concerns in self.concerns:
                if _item_concerns:
                    _items.append(_item_concerns.to_dict())
            _dict['concerns'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ExternalContainer from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id") if obj.get("id") is not None else -1,
            "source_id": obj.get("source_id"),
            "issue_id": obj.get("issue_id"),
            "risk_score": obj.get("risk_score"),
            "title": obj.get("title"),
            "provider_fields": obj.get("provider_fields"),
            "issue_summary_short": obj.get("issue_summary_short"),
            "issue_summary_5w": Summary5W.from_dict(obj["issue_summary_5w"]) if obj.get("issue_summary_5w") is not None else None,
            "risk": ExternalContainerRisk.from_dict(obj["risk"]) if obj.get("risk") is not None else None,
            "concerns": [ExternalPrimeConcern.from_dict(_item) for _item in obj["concerns"]] if obj.get("concerns") is not None else None
        })
        return _obj


