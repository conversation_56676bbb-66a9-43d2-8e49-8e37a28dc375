# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List
from prime_rat_logic_service_client.models.external_control import ExternalControl
from prime_rat_logic_service_client.models.issue_analysis_concern_methodology import IssueAnalysisConcernMethodology
from typing import Optional, Set
from typing_extensions import Self

class ExternalFrameworkConcern(BaseModel):
    """
    ExternalFrameworkConcern
    """ # noqa: E501
    id: StrictInt
    short_description: StrictStr
    long_description: StrictStr
    methodology: IssueAnalysisConcernMethodology
    controls: List[ExternalControl]
    __properties: ClassVar[List[str]] = ["id", "short_description", "long_description", "methodology", "controls"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ExternalFrameworkConcern from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of methodology
        if self.methodology:
            _dict['methodology'] = self.methodology.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in controls (list)
        _items = []
        if self.controls:
            for _item_controls in self.controls:
                if _item_controls:
                    _items.append(_item_controls.to_dict())
            _dict['controls'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ExternalFrameworkConcern from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "short_description": obj.get("short_description"),
            "long_description": obj.get("long_description"),
            "methodology": IssueAnalysisConcernMethodology.from_dict(obj["methodology"]) if obj.get("methodology") is not None else None,
            "controls": [ExternalControl.from_dict(_item) for _item in obj["controls"]] if obj.get("controls") is not None else None
        })
        return _obj


