# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict
from typing import Any, ClassVar, Dict, List, Optional
from prime_rat_logic_service_client.models.research_utils_models_context_quote import ResearchUtilsModelsContextQuote
from typing import Optional, Set
from typing_extensions import Self

class ResearchUtilsModelsContextQuotesOutput(BaseModel):
    """
    ResearchUtilsModelsContextQuotesOutput
    """ # noqa: E501
    quote: Optional[List[ResearchUtilsModelsContextQuote]] = None
    __properties: ClassVar[List[str]] = ["quote"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ResearchUtilsModelsContextQuotesOutput from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in quote (list)
        _items = []
        if self.quote:
            for _item_quote in self.quote:
                if _item_quote:
                    _items.append(_item_quote.to_dict())
            _dict['quote'] = _items
        # set to None if quote (nullable) is None
        # and model_fields_set contains the field
        if self.quote is None and "quote" in self.model_fields_set:
            _dict['quote'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ResearchUtilsModelsContextQuotesOutput from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "quote": [ResearchUtilsModelsContextQuote.from_dict(_item) for _item in obj["quote"]] if obj.get("quote") is not None else None
        })
        return _obj


