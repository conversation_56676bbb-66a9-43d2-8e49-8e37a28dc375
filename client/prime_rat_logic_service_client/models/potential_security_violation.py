# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from prime_rat_logic_service_client.models.psv_status import PsvStatus
from typing import Optional, Set
from typing_extensions import Self

class PotentialSecurityViolation(BaseModel):
    """
    PotentialSecurityViolation
    """ # noqa: E501
    psv_id: StrictInt
    created_at: datetime
    title: StrictStr
    description: StrictStr
    type: StrictStr
    source_id: StrictInt
    issue_id: StrictStr
    status: PsvStatus
    dismissed_reason: Optional[StrictStr]
    project: Optional[StrictStr]
    reporter: Optional[StrictStr]
    detection_date: datetime
    issue_link: StrictStr
    __properties: ClassVar[List[str]] = ["psv_id", "created_at", "title", "description", "type", "source_id", "issue_id", "status", "dismissed_reason", "project", "reporter", "detection_date", "issue_link"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of PotentialSecurityViolation from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # set to None if dismissed_reason (nullable) is None
        # and model_fields_set contains the field
        if self.dismissed_reason is None and "dismissed_reason" in self.model_fields_set:
            _dict['dismissed_reason'] = None

        # set to None if project (nullable) is None
        # and model_fields_set contains the field
        if self.project is None and "project" in self.model_fields_set:
            _dict['project'] = None

        # set to None if reporter (nullable) is None
        # and model_fields_set contains the field
        if self.reporter is None and "reporter" in self.model_fields_set:
            _dict['reporter'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of PotentialSecurityViolation from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "psv_id": obj.get("psv_id"),
            "created_at": obj.get("created_at"),
            "title": obj.get("title"),
            "description": obj.get("description"),
            "type": obj.get("type"),
            "source_id": obj.get("source_id"),
            "issue_id": obj.get("issue_id"),
            "status": obj.get("status"),
            "dismissed_reason": obj.get("dismissed_reason"),
            "project": obj.get("project"),
            "reporter": obj.get("reporter"),
            "detection_date": obj.get("detection_date"),
            "issue_link": obj.get("issue_link")
        })
        return _obj


