# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from prime_rat_logic_service_client.models.risk_factor_level import RiskFactorLevel
from typing import Optional, Set
from typing_extensions import Self

class RiskFactors(BaseModel):
    """
    RiskFactors
    """ # noqa: E501
    confidentiality: Optional[Annotated[int, Field(le=10, strict=True, ge=0)]] = None
    confidentiality_explanation: Optional[StrictStr] = None
    integrity: Optional[Annotated[int, Field(le=10, strict=True, ge=0)]] = None
    integrity_explanation: Optional[StrictStr] = None
    availability: Optional[Annotated[int, Field(le=10, strict=True, ge=0)]] = None
    availability_explanation: Optional[StrictStr] = None
    third_party_management: Optional[Annotated[int, Field(le=10, strict=True, ge=0)]] = None
    third_party_management_explanation: Optional[StrictStr] = None
    compliance: Optional[Annotated[int, Field(le=10, strict=True, ge=0)]] = None
    compliance_explanation: Optional[StrictStr] = None
    severity: Optional[Annotated[int, Field(le=3, strict=True, ge=1)]] = None
    severity_explanation: Optional[StrictStr] = None
    scope: Optional[Annotated[int, Field(le=3, strict=True, ge=1)]] = None
    scope_explanation: Optional[StrictStr] = None
    confidentiality_level: Optional[RiskFactorLevel]
    integrity_level: Optional[RiskFactorLevel]
    availability_level: Optional[RiskFactorLevel]
    third_party_management_level: Optional[RiskFactorLevel]
    compliance_level: Optional[RiskFactorLevel]
    __properties: ClassVar[List[str]] = ["confidentiality", "confidentiality_explanation", "integrity", "integrity_explanation", "availability", "availability_explanation", "third_party_management", "third_party_management_explanation", "compliance", "compliance_explanation", "severity", "severity_explanation", "scope", "scope_explanation", "confidentiality_level", "integrity_level", "availability_level", "third_party_management_level", "compliance_level"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RiskFactors from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # set to None if confidentiality (nullable) is None
        # and model_fields_set contains the field
        if self.confidentiality is None and "confidentiality" in self.model_fields_set:
            _dict['confidentiality'] = None

        # set to None if confidentiality_explanation (nullable) is None
        # and model_fields_set contains the field
        if self.confidentiality_explanation is None and "confidentiality_explanation" in self.model_fields_set:
            _dict['confidentiality_explanation'] = None

        # set to None if integrity (nullable) is None
        # and model_fields_set contains the field
        if self.integrity is None and "integrity" in self.model_fields_set:
            _dict['integrity'] = None

        # set to None if integrity_explanation (nullable) is None
        # and model_fields_set contains the field
        if self.integrity_explanation is None and "integrity_explanation" in self.model_fields_set:
            _dict['integrity_explanation'] = None

        # set to None if availability (nullable) is None
        # and model_fields_set contains the field
        if self.availability is None and "availability" in self.model_fields_set:
            _dict['availability'] = None

        # set to None if availability_explanation (nullable) is None
        # and model_fields_set contains the field
        if self.availability_explanation is None and "availability_explanation" in self.model_fields_set:
            _dict['availability_explanation'] = None

        # set to None if third_party_management (nullable) is None
        # and model_fields_set contains the field
        if self.third_party_management is None and "third_party_management" in self.model_fields_set:
            _dict['third_party_management'] = None

        # set to None if third_party_management_explanation (nullable) is None
        # and model_fields_set contains the field
        if self.third_party_management_explanation is None and "third_party_management_explanation" in self.model_fields_set:
            _dict['third_party_management_explanation'] = None

        # set to None if compliance (nullable) is None
        # and model_fields_set contains the field
        if self.compliance is None and "compliance" in self.model_fields_set:
            _dict['compliance'] = None

        # set to None if compliance_explanation (nullable) is None
        # and model_fields_set contains the field
        if self.compliance_explanation is None and "compliance_explanation" in self.model_fields_set:
            _dict['compliance_explanation'] = None

        # set to None if severity (nullable) is None
        # and model_fields_set contains the field
        if self.severity is None and "severity" in self.model_fields_set:
            _dict['severity'] = None

        # set to None if severity_explanation (nullable) is None
        # and model_fields_set contains the field
        if self.severity_explanation is None and "severity_explanation" in self.model_fields_set:
            _dict['severity_explanation'] = None

        # set to None if scope (nullable) is None
        # and model_fields_set contains the field
        if self.scope is None and "scope" in self.model_fields_set:
            _dict['scope'] = None

        # set to None if scope_explanation (nullable) is None
        # and model_fields_set contains the field
        if self.scope_explanation is None and "scope_explanation" in self.model_fields_set:
            _dict['scope_explanation'] = None

        # set to None if confidentiality_level (nullable) is None
        # and model_fields_set contains the field
        if self.confidentiality_level is None and "confidentiality_level" in self.model_fields_set:
            _dict['confidentiality_level'] = None

        # set to None if integrity_level (nullable) is None
        # and model_fields_set contains the field
        if self.integrity_level is None and "integrity_level" in self.model_fields_set:
            _dict['integrity_level'] = None

        # set to None if availability_level (nullable) is None
        # and model_fields_set contains the field
        if self.availability_level is None and "availability_level" in self.model_fields_set:
            _dict['availability_level'] = None

        # set to None if third_party_management_level (nullable) is None
        # and model_fields_set contains the field
        if self.third_party_management_level is None and "third_party_management_level" in self.model_fields_set:
            _dict['third_party_management_level'] = None

        # set to None if compliance_level (nullable) is None
        # and model_fields_set contains the field
        if self.compliance_level is None and "compliance_level" in self.model_fields_set:
            _dict['compliance_level'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RiskFactors from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "confidentiality": obj.get("confidentiality"),
            "confidentiality_explanation": obj.get("confidentiality_explanation"),
            "integrity": obj.get("integrity"),
            "integrity_explanation": obj.get("integrity_explanation"),
            "availability": obj.get("availability"),
            "availability_explanation": obj.get("availability_explanation"),
            "third_party_management": obj.get("third_party_management"),
            "third_party_management_explanation": obj.get("third_party_management_explanation"),
            "compliance": obj.get("compliance"),
            "compliance_explanation": obj.get("compliance_explanation"),
            "severity": obj.get("severity"),
            "severity_explanation": obj.get("severity_explanation"),
            "scope": obj.get("scope"),
            "scope_explanation": obj.get("scope_explanation"),
            "confidentiality_level": obj.get("confidentiality_level"),
            "integrity_level": obj.get("integrity_level"),
            "availability_level": obj.get("availability_level"),
            "third_party_management_level": obj.get("third_party_management_level"),
            "compliance_level": obj.get("compliance_level")
        })
        return _obj


