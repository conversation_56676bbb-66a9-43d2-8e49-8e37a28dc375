# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import json
from enum import Enum
from typing_extensions import Self


class CaseAuditAction(str, Enum):
    """
    CaseAuditAction
    """

    """
    allowed enum values
    """
    CREATE_CASE = 'create_case'
    USER_VIEW_CASE = 'user_view_case'
    UPDATE_STATUS = 'update_status'
    OVERRIDE_RISK_CATEGORY = 'override_risk_category'

    @classmethod
    def from_json(cls, json_str: str) -> Self:
        """Create an instance of CaseAuditAction from a JSON string"""
        return cls(json.loads(json_str))


