# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List
from typing_extensions import Annotated
from prime_rat_logic_service_client.models.date_point import DatePoint
from typing import Optional, Set
from typing_extensions import Self

class CasesByStatusStats(BaseModel):
    """
    CasesByStatusStats
    """ # noqa: E501
    start: datetime
    end: datetime
    query_name: StrictStr
    scanned: Annotated[List[DatePoint], Field(min_length=1)]
    identified: Annotated[List[DatePoint], Field(min_length=1)]
    close: Annotated[List[DatePoint], Field(min_length=1)]
    __properties: ClassVar[List[str]] = ["start", "end", "query_name", "scanned", "identified", "close"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CasesByStatusStats from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in scanned (list)
        _items = []
        if self.scanned:
            for _item_scanned in self.scanned:
                if _item_scanned:
                    _items.append(_item_scanned.to_dict())
            _dict['scanned'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in identified (list)
        _items = []
        if self.identified:
            for _item_identified in self.identified:
                if _item_identified:
                    _items.append(_item_identified.to_dict())
            _dict['identified'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in close (list)
        _items = []
        if self.close:
            for _item_close in self.close:
                if _item_close:
                    _items.append(_item_close.to_dict())
            _dict['close'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CasesByStatusStats from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "start": obj.get("start"),
            "end": obj.get("end"),
            "query_name": obj.get("query_name"),
            "scanned": [DatePoint.from_dict(_item) for _item in obj["scanned"]] if obj.get("scanned") is not None else None,
            "identified": [DatePoint.from_dict(_item) for _item in obj["identified"]] if obj.get("identified") is not None else None,
            "close": [DatePoint.from_dict(_item) for _item in obj["close"]] if obj.get("close") is not None else None
        })
        return _obj


