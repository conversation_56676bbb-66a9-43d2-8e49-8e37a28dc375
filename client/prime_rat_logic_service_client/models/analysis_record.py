# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from prime_rat_logic_service_client.models.risk_score_category import RiskScoreCategory
from typing import Optional, Set
from typing_extensions import Self

class AnalysisRecord(BaseModel):
    """
    AnalysisRecord
    """ # noqa: E501
    availability: Optional[StrictInt] = None
    confidentiality: Optional[StrictInt] = None
    integrity: Optional[StrictInt] = None
    risk_score: Optional[StrictInt] = None
    risk_score_category: Optional[RiskScoreCategory] = None
    classification: Optional[StrictBool] = None
    concerns: Optional[List[StrictStr]] = None
    is_automated: Optional[StrictBool] = None
    is_security_enhancement: Optional[StrictBool] = None
    __properties: ClassVar[List[str]] = ["availability", "confidentiality", "integrity", "risk_score", "risk_score_category", "classification", "concerns", "is_automated", "is_security_enhancement"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of AnalysisRecord from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # set to None if availability (nullable) is None
        # and model_fields_set contains the field
        if self.availability is None and "availability" in self.model_fields_set:
            _dict['availability'] = None

        # set to None if confidentiality (nullable) is None
        # and model_fields_set contains the field
        if self.confidentiality is None and "confidentiality" in self.model_fields_set:
            _dict['confidentiality'] = None

        # set to None if integrity (nullable) is None
        # and model_fields_set contains the field
        if self.integrity is None and "integrity" in self.model_fields_set:
            _dict['integrity'] = None

        # set to None if risk_score (nullable) is None
        # and model_fields_set contains the field
        if self.risk_score is None and "risk_score" in self.model_fields_set:
            _dict['risk_score'] = None

        # set to None if risk_score_category (nullable) is None
        # and model_fields_set contains the field
        if self.risk_score_category is None and "risk_score_category" in self.model_fields_set:
            _dict['risk_score_category'] = None

        # set to None if classification (nullable) is None
        # and model_fields_set contains the field
        if self.classification is None and "classification" in self.model_fields_set:
            _dict['classification'] = None

        # set to None if concerns (nullable) is None
        # and model_fields_set contains the field
        if self.concerns is None and "concerns" in self.model_fields_set:
            _dict['concerns'] = None

        # set to None if is_automated (nullable) is None
        # and model_fields_set contains the field
        if self.is_automated is None and "is_automated" in self.model_fields_set:
            _dict['is_automated'] = None

        # set to None if is_security_enhancement (nullable) is None
        # and model_fields_set contains the field
        if self.is_security_enhancement is None and "is_security_enhancement" in self.model_fields_set:
            _dict['is_security_enhancement'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of AnalysisRecord from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "availability": obj.get("availability"),
            "confidentiality": obj.get("confidentiality"),
            "integrity": obj.get("integrity"),
            "risk_score": obj.get("risk_score"),
            "risk_score_category": obj.get("risk_score_category"),
            "classification": obj.get("classification"),
            "concerns": obj.get("concerns"),
            "is_automated": obj.get("is_automated"),
            "is_security_enhancement": obj.get("is_security_enhancement")
        })
        return _obj


