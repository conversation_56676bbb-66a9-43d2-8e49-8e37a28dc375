# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
from inspect import getfullargspec
import json
import pprint
import re  # noqa: F401
from pydantic import BaseModel, ConfigDict, Field, StrictStr, ValidationError, field_validator
from typing import Optional
from prime_rat_logic_service_client.models.job_build_fields_data_create_args import JobBuildFieldsDataCreateArgs
from prime_rat_logic_service_client.models.job_classification_create_args import JobClassificationCreateArgs
from prime_rat_logic_service_client.models.job_psv_create_args import JobPsvCreateArgs
from prime_rat_logic_service_client.models.job_summary_create_args import JobSummaryCreateArgs
from prime_rat_logic_service_client.models.job_update_issues_create_args import JobUpdateIssuesCreateArgs
from typing import Union, Any, List, Set, TYPE_CHECKING, Optional, Dict
from typing_extensions import Literal, Self
from pydantic import Field

JOBCREATEARGS_ANY_OF_SCHEMAS = ["JobBuildFieldsDataCreateArgs", "JobClassificationCreateArgs", "JobPsvCreateArgs", "JobSummaryCreateArgs", "JobUpdateIssuesCreateArgs"]

class JobCreateArgs(BaseModel):
    """
    JobCreateArgs
    """

    # data type: JobPsvCreateArgs
    anyof_schema_1_validator: Optional[JobPsvCreateArgs] = None
    # data type: JobUpdateIssuesCreateArgs
    anyof_schema_2_validator: Optional[JobUpdateIssuesCreateArgs] = None
    # data type: JobBuildFieldsDataCreateArgs
    anyof_schema_3_validator: Optional[JobBuildFieldsDataCreateArgs] = None
    # data type: JobSummaryCreateArgs
    anyof_schema_4_validator: Optional[JobSummaryCreateArgs] = None
    # data type: JobClassificationCreateArgs
    anyof_schema_5_validator: Optional[JobClassificationCreateArgs] = None
    if TYPE_CHECKING:
        actual_instance: Optional[Union[JobBuildFieldsDataCreateArgs, JobClassificationCreateArgs, JobPsvCreateArgs, JobSummaryCreateArgs, JobUpdateIssuesCreateArgs]] = None
    else:
        actual_instance: Any = None
    any_of_schemas: Set[str] = { "JobBuildFieldsDataCreateArgs", "JobClassificationCreateArgs", "JobPsvCreateArgs", "JobSummaryCreateArgs", "JobUpdateIssuesCreateArgs" }

    model_config = {
        "validate_assignment": True,
        "protected_namespaces": (),
    }

    def __init__(self, *args, **kwargs) -> None:
        if args:
            if len(args) > 1:
                raise ValueError("If a position argument is used, only 1 is allowed to set `actual_instance`")
            if kwargs:
                raise ValueError("If a position argument is used, keyword arguments cannot be used.")
            super().__init__(actual_instance=args[0])
        else:
            super().__init__(**kwargs)

    @field_validator('actual_instance')
    def actual_instance_must_validate_anyof(cls, v):
        instance = JobCreateArgs.model_construct()
        error_messages = []
        # validate data type: JobPsvCreateArgs
        if not isinstance(v, JobPsvCreateArgs):
            error_messages.append(f"Error! Input type `{type(v)}` is not `JobPsvCreateArgs`")
        else:
            return v

        # validate data type: JobUpdateIssuesCreateArgs
        if not isinstance(v, JobUpdateIssuesCreateArgs):
            error_messages.append(f"Error! Input type `{type(v)}` is not `JobUpdateIssuesCreateArgs`")
        else:
            return v

        # validate data type: JobBuildFieldsDataCreateArgs
        if not isinstance(v, JobBuildFieldsDataCreateArgs):
            error_messages.append(f"Error! Input type `{type(v)}` is not `JobBuildFieldsDataCreateArgs`")
        else:
            return v

        # validate data type: JobSummaryCreateArgs
        if not isinstance(v, JobSummaryCreateArgs):
            error_messages.append(f"Error! Input type `{type(v)}` is not `JobSummaryCreateArgs`")
        else:
            return v

        # validate data type: JobClassificationCreateArgs
        if not isinstance(v, JobClassificationCreateArgs):
            error_messages.append(f"Error! Input type `{type(v)}` is not `JobClassificationCreateArgs`")
        else:
            return v

        if error_messages:
            # no match
            raise ValueError("No match found when setting the actual_instance in JobCreateArgs with anyOf schemas: JobBuildFieldsDataCreateArgs, JobClassificationCreateArgs, JobPsvCreateArgs, JobSummaryCreateArgs, JobUpdateIssuesCreateArgs. Details: " + ", ".join(error_messages))
        else:
            return v

    @classmethod
    def from_dict(cls, obj: Dict[str, Any]) -> Self:
        return cls.from_json(json.dumps(obj))

    @classmethod
    def from_json(cls, json_str: str) -> Self:
        """Returns the object represented by the json string"""
        instance = cls.model_construct()
        error_messages = []
        # anyof_schema_1_validator: Optional[JobPsvCreateArgs] = None
        try:
            instance.actual_instance = JobPsvCreateArgs.from_json(json_str)
            return instance
        except (ValidationError, ValueError) as e:
             error_messages.append(str(e))
        # anyof_schema_2_validator: Optional[JobUpdateIssuesCreateArgs] = None
        try:
            instance.actual_instance = JobUpdateIssuesCreateArgs.from_json(json_str)
            return instance
        except (ValidationError, ValueError) as e:
             error_messages.append(str(e))
        # anyof_schema_3_validator: Optional[JobBuildFieldsDataCreateArgs] = None
        try:
            instance.actual_instance = JobBuildFieldsDataCreateArgs.from_json(json_str)
            return instance
        except (ValidationError, ValueError) as e:
             error_messages.append(str(e))
        # anyof_schema_4_validator: Optional[JobSummaryCreateArgs] = None
        try:
            instance.actual_instance = JobSummaryCreateArgs.from_json(json_str)
            return instance
        except (ValidationError, ValueError) as e:
             error_messages.append(str(e))
        # anyof_schema_5_validator: Optional[JobClassificationCreateArgs] = None
        try:
            instance.actual_instance = JobClassificationCreateArgs.from_json(json_str)
            return instance
        except (ValidationError, ValueError) as e:
             error_messages.append(str(e))

        if error_messages:
            # no match
            raise ValueError("No match found when deserializing the JSON string into JobCreateArgs with anyOf schemas: JobBuildFieldsDataCreateArgs, JobClassificationCreateArgs, JobPsvCreateArgs, JobSummaryCreateArgs, JobUpdateIssuesCreateArgs. Details: " + ", ".join(error_messages))
        else:
            return instance

    def to_json(self) -> str:
        """Returns the JSON representation of the actual instance"""
        if self.actual_instance is None:
            return "null"

        if hasattr(self.actual_instance, "to_json") and callable(self.actual_instance.to_json):
            return self.actual_instance.to_json()
        else:
            return json.dumps(self.actual_instance)

    def to_dict(self) -> Optional[Union[Dict[str, Any], JobBuildFieldsDataCreateArgs, JobClassificationCreateArgs, JobPsvCreateArgs, JobSummaryCreateArgs, JobUpdateIssuesCreateArgs]]:
        """Returns the dict representation of the actual instance"""
        if self.actual_instance is None:
            return None

        if hasattr(self.actual_instance, "to_dict") and callable(self.actual_instance.to_dict):
            return self.actual_instance.to_dict()
        else:
            return self.actual_instance

    def to_str(self) -> str:
        """Returns the string representation of the actual instance"""
        return pprint.pformat(self.model_dump())


