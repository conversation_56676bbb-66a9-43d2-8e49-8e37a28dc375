# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from prime_rat_logic_service_client.models.case_status import CaseStatus
from prime_rat_logic_service_client.models.external_issue_analysis_workroom import ExternalIssueAnalysisWorkroom
from prime_rat_logic_service_client.models.provider_field_info import ProviderFieldInfo
from typing import Optional, Set
from typing_extensions import Self

class ExternalCaseWorkroom(BaseModel):
    """
    ExternalCaseWorkroom
    """ # noqa: E501
    account_id: StrictStr
    source_id: StrictInt
    issue_id: StrictStr
    case_id: StrictInt
    status: CaseStatus
    issue_analysis: ExternalIssueAnalysisWorkroom
    write_back_recommendations: StrictBool
    title: StrictStr
    link: StrictStr
    labels: List[StrictStr]
    provider_fields: Dict[str, StrictStr]
    provider_fields_min_schema: Optional[Dict[str, ProviderFieldInfo]] = None
    parents: Optional[List[StrictStr]] = None
    progress_percentage: StrictInt
    __properties: ClassVar[List[str]] = ["account_id", "source_id", "issue_id", "case_id", "status", "issue_analysis", "write_back_recommendations", "title", "link", "labels", "provider_fields", "provider_fields_min_schema", "parents", "progress_percentage"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ExternalCaseWorkroom from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of issue_analysis
        if self.issue_analysis:
            _dict['issue_analysis'] = self.issue_analysis.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each value in provider_fields_min_schema (dict)
        _field_dict = {}
        if self.provider_fields_min_schema:
            for _key_provider_fields_min_schema in self.provider_fields_min_schema:
                if self.provider_fields_min_schema[_key_provider_fields_min_schema]:
                    _field_dict[_key_provider_fields_min_schema] = self.provider_fields_min_schema[_key_provider_fields_min_schema].to_dict()
            _dict['provider_fields_min_schema'] = _field_dict
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ExternalCaseWorkroom from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "account_id": obj.get("account_id"),
            "source_id": obj.get("source_id"),
            "issue_id": obj.get("issue_id"),
            "case_id": obj.get("case_id"),
            "status": obj.get("status"),
            "issue_analysis": ExternalIssueAnalysisWorkroom.from_dict(obj["issue_analysis"]) if obj.get("issue_analysis") is not None else None,
            "write_back_recommendations": obj.get("write_back_recommendations"),
            "title": obj.get("title"),
            "link": obj.get("link"),
            "labels": obj.get("labels"),
            "provider_fields": obj.get("provider_fields"),
            "provider_fields_min_schema": dict(
                (_k, ProviderFieldInfo.from_dict(_v))
                for _k, _v in obj["provider_fields_min_schema"].items()
            )
            if obj.get("provider_fields_min_schema") is not None
            else None,
            "parents": obj.get("parents"),
            "progress_percentage": obj.get("progress_percentage")
        })
        return _obj


