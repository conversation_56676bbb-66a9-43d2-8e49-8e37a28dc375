# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.risk_score_category import RiskScoreCategory

class TestRiskScoreCategory(unittest.TestCase):
    """RiskScoreCategory unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testRiskScoreCategory(self):
        """Test RiskScoreCategory"""
        # inst = RiskScoreCategory()

if __name__ == '__main__':
    unittest.main()
