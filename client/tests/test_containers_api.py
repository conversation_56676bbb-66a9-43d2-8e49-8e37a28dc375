# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.api.containers_api import ContainersApi


class TestContainersApi(unittest.IsolatedAsyncioTestCase):
    """ContainersApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = ContainersApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_get_container(self) -> None:
        """Test case for get_container

        Get Container
        """
        pass


if __name__ == '__main__':
    unittest.main()
