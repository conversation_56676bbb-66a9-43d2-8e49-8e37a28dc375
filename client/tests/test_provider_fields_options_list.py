# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.provider_fields_options_list import ProviderFieldsOptionsList

class TestProviderFieldsOptionsList(unittest.TestCase):
    """ProviderFieldsOptionsList unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ProviderFieldsOptionsList:
        """Test ProviderFieldsOptionsList
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ProviderFieldsOptionsList`
        """
        model = ProviderFieldsOptionsList()
        if include_optional:
            return ProviderFieldsOptionsList(
                fields = [
                    prime_rat_logic_service_client.models.provider_field_info_options.ProviderFieldInfoOptions(
                        type = 'string', 
                        id = '', 
                        name = '', 
                        options = [
                            ''
                            ], )
                    ],
                total = 56
            )
        else:
            return ProviderFieldsOptionsList(
                fields = [
                    prime_rat_logic_service_client.models.provider_field_info_options.ProviderFieldInfoOptions(
                        type = 'string', 
                        id = '', 
                        name = '', 
                        options = [
                            ''
                            ], )
                    ],
                total = 56,
        )
        """

    def testProviderFieldsOptionsList(self):
        """Test ProviderFieldsOptionsList"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
