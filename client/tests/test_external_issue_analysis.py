# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.external_issue_analysis import ExternalIssueAnalysis

class TestExternalIssueAnalysis(unittest.TestCase):
    """ExternalIssueAnalysis unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ExternalIssueAnalysis:
        """Test ExternalIssueAnalysis
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ExternalIssueAnalysis`
        """
        model = ExternalIssueAnalysis()
        if include_optional:
            return ExternalIssueAnalysis(
                account_id = '',
                source_id = 56,
                issue_id = '',
                risk_factors = prime_rat_logic_service_client.models.risk_factors.RiskFactors(
                    confidentiality = 0.0, 
                    confidentiality_explanation = '', 
                    integrity = 0.0, 
                    integrity_explanation = '', 
                    availability = 0.0, 
                    availability_explanation = '', 
                    third_party_management = 0.0, 
                    third_party_management_explanation = '', 
                    compliance = 0.0, 
                    compliance_explanation = '', 
                    severity = 1.0, 
                    severity_explanation = '', 
                    scope = 1.0, 
                    scope_explanation = '', 
                    confidentiality_level = 'low', 
                    integrity_level = 'low', 
                    availability_level = 'low', 
                    third_party_management_level = 'low', 
                    compliance_level = 'low', ),
                issue_hash = '',
                confidence = 0.0,
                is_automated = True,
                risk_score = 0.0,
                mitre_categories = [
                    ''
                    ],
                linddun_categories = [
                    ''
                    ],
                fire_summary = '',
                long_ai_summary_5w = prime_rat_logic_service_client.models.summary5_w.Summary5W(
                    what = prime_rat_logic_service_client.models._what._What(
                        summary = '', 
                        description = '', ), 
                    where = prime_rat_logic_service_client.models._where._Where(
                        environment = '', 
                        components = '', 
                        products = '', ), 
                    who = prime_rat_logic_service_client.models._who._Who(
                        stakeholders = '', 
                        affected = '', ), 
                    why = prime_rat_logic_service_client.models._why._Why(
                        purpose = '', 
                        impact = '', ), 
                    how = prime_rat_logic_service_client.models._how._How(
                        approach = '', 
                        acceptance = '', ), ),
                short_ai_summary = '',
                short_assessment = '',
                long_assessment = '',
                issue_links = [
                    prime_rat_logic_service_client.models.issue_links.IssueLinks(
                        url = '', 
                        link_type = 'confluence', )
                    ],
                keywords = [
                    ''
                    ],
                risk_score_category = 'intervene',
                confidence_level = 'low'
            )
        else:
            return ExternalIssueAnalysis(
                account_id = '',
                source_id = 56,
                issue_id = '',
                risk_factors = prime_rat_logic_service_client.models.risk_factors.RiskFactors(
                    confidentiality = 0.0, 
                    confidentiality_explanation = '', 
                    integrity = 0.0, 
                    integrity_explanation = '', 
                    availability = 0.0, 
                    availability_explanation = '', 
                    third_party_management = 0.0, 
                    third_party_management_explanation = '', 
                    compliance = 0.0, 
                    compliance_explanation = '', 
                    severity = 1.0, 
                    severity_explanation = '', 
                    scope = 1.0, 
                    scope_explanation = '', 
                    confidentiality_level = 'low', 
                    integrity_level = 'low', 
                    availability_level = 'low', 
                    third_party_management_level = 'low', 
                    compliance_level = 'low', ),
                issue_hash = '',
                is_automated = True,
                long_ai_summary_5w = prime_rat_logic_service_client.models.summary5_w.Summary5W(
                    what = prime_rat_logic_service_client.models._what._What(
                        summary = '', 
                        description = '', ), 
                    where = prime_rat_logic_service_client.models._where._Where(
                        environment = '', 
                        components = '', 
                        products = '', ), 
                    who = prime_rat_logic_service_client.models._who._Who(
                        stakeholders = '', 
                        affected = '', ), 
                    why = prime_rat_logic_service_client.models._why._Why(
                        purpose = '', 
                        impact = '', ), 
                    how = prime_rat_logic_service_client.models._how._How(
                        approach = '', 
                        acceptance = '', ), ),
                short_ai_summary = '',
                short_assessment = '',
                long_assessment = '',
                risk_score_category = 'intervene',
                confidence_level = 'low',
        )
        """

    def testExternalIssueAnalysis(self):
        """Test ExternalIssueAnalysis"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
