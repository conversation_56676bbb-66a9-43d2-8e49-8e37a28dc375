# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.search_response import SearchResponse

class TestSearchResponse(unittest.TestCase):
    """SearchResponse unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SearchResponse:
        """Test SearchResponse
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SearchResponse`
        """
        model = SearchResponse()
        if include_optional:
            return SearchResponse(
                id = 56,
                issue_id = '',
                title = '',
                issue_type = '',
                is_container = True,
                source_id = 56
            )
        else:
            return SearchResponse(
                id = 56,
                issue_id = '',
                title = '',
                issue_type = '',
                is_container = True,
                source_id = 56,
        )
        """

    def testSearchResponse(self):
        """Test SearchResponse"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
