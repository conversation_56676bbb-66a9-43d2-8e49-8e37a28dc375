# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.analysis_record import AnalysisRecord

class TestAnalysisRecord(unittest.TestCase):
    """AnalysisRecord unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> AnalysisRecord:
        """Test AnalysisRecord
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `AnalysisRecord`
        """
        model = AnalysisRecord()
        if include_optional:
            return AnalysisRecord(
                availability = 56,
                confidentiality = 56,
                integrity = 56,
                risk_score = 56,
                risk_score_category = 'intervene',
                classification = True,
                concerns = [
                    ''
                    ],
                is_automated = True,
                is_security_enhancement = True
            )
        else:
            return AnalysisRecord(
        )
        """

    def testAnalysisRecord(self):
        """Test AnalysisRecord"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
