# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.api.jira_fields_api import JiraFieldsApi


class TestJiraFieldsApi(unittest.IsolatedAsyncioTestCase):
    """JiraFieldsApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = JiraFieldsApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_get_all_provider_fields(self) -> None:
        """Test case for get_all_provider_fields

        Get All Provider Fields
        """
        pass

    async def test_get_selected_provider_fields(self) -> None:
        """Test case for get_selected_provider_fields

        Get Selected Provider Fields
        """
        pass

    async def test_get_workroom_fields(self) -> None:
        """Test case for get_workroom_fields

        Get Workroom Fields
        """
        pass


if __name__ == '__main__':
    unittest.main()
