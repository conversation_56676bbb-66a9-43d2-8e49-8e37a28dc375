# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.risk_factors import RiskFactors

class TestRiskFactors(unittest.TestCase):
    """RiskFactors unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> RiskFactors:
        """Test RiskFactors
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `RiskFactors`
        """
        model = RiskFactors()
        if include_optional:
            return RiskFactors(
                confidentiality = 0.0,
                confidentiality_explanation = '',
                integrity = 0.0,
                integrity_explanation = '',
                availability = 0.0,
                availability_explanation = '',
                third_party_management = 0.0,
                third_party_management_explanation = '',
                compliance = 0.0,
                compliance_explanation = '',
                severity = 1.0,
                severity_explanation = '',
                scope = 1.0,
                scope_explanation = '',
                confidentiality_level = 'low',
                integrity_level = 'low',
                availability_level = 'low',
                third_party_management_level = 'low',
                compliance_level = 'low'
            )
        else:
            return RiskFactors(
                confidentiality_level = 'low',
                integrity_level = 'low',
                availability_level = 'low',
                third_party_management_level = 'low',
                compliance_level = 'low',
        )
        """

    def testRiskFactors(self):
        """Test RiskFactors"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
