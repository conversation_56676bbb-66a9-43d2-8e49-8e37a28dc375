# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.job_type import JobType

class TestJobType(unittest.TestCase):
    """JobType unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testJobType(self):
        """Test JobType"""
        # inst = JobType()

if __name__ == '__main__':
    unittest.main()
