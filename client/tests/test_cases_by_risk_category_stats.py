# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.cases_by_risk_category_stats import CasesByRiskCategoryStats

class TestCasesByRiskCategoryStats(unittest.TestCase):
    """CasesByRiskCategoryStats unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> CasesByRiskCategoryStats:
        """Test CasesByRiskCategoryStats
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `CasesByRiskCategoryStats`
        """
        model = CasesByRiskCategoryStats()
        if include_optional:
            return CasesByRiskCategoryStats(
                start = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                end = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                query_name = '',
                intervene = [
                    prime_rat_logic_service_client.models.date_point.DatePoint(
                        x = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                        y = 56, )
                    ],
                analyze = [
                    prime_rat_logic_service_client.models.date_point.DatePoint(
                        x = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                        y = 56, )
                    ],
                monitor = [
                    prime_rat_logic_service_client.models.date_point.DatePoint(
                        x = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                        y = 56, )
                    ]
            )
        else:
            return CasesByRiskCategoryStats(
                start = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                end = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                query_name = '',
                intervene = [
                    prime_rat_logic_service_client.models.date_point.DatePoint(
                        x = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                        y = 56, )
                    ],
                analyze = [
                    prime_rat_logic_service_client.models.date_point.DatePoint(
                        x = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                        y = 56, )
                    ],
                monitor = [
                    prime_rat_logic_service_client.models.date_point.DatePoint(
                        x = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                        y = 56, )
                    ],
        )
        """

    def testCasesByRiskCategoryStats(self):
        """Test CasesByRiskCategoryStats"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
