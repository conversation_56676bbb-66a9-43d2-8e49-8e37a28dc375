# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.exported_record import ExportedRecord

class TestExportedRecord(unittest.TestCase):
    """ExportedRecord unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ExportedRecord:
        """Test ExportedRecord
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ExportedRecord`
        """
        model = ExportedRecord()
        if include_optional:
            return ExportedRecord(
                created_at = '',
                issue_id = '',
                parent_issue_id = '',
                status = '',
                analysis = prime_rat_logic_service_client.models.analysis_record.AnalysisRecord(
                    availability = 56, 
                    confidentiality = 56, 
                    integrity = 56, 
                    risk_score = 56, 
                    risk_score_category = 'intervene', 
                    classification = True, 
                    concerns = [
                        ''
                        ], 
                    is_automated = True, 
                    is_security_enhancement = True, ),
                summary = prime_rat_logic_service_client.models.exported_summary.ExportedSummary(
                    summary = 'No summary found', 
                    questions = prime_rat_logic_service_client.models.questions_output.QuestionsOutput(
                        what = prime_rat_logic_service_client.models.what_output.WhatOutput(
                            summary = prime_rat_logic_service_client.models.content_output.ContentOutput(
                                text = '', 
                                quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                                    quote = [
                                        prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                            quote_text = '', 
                                            source = '', )
                                        ], ), ), 
                            description = prime_rat_logic_service_client.models.content_output.ContentOutput(
                                text = '', ), ), 
                        who = prime_rat_logic_service_client.models.who_output.WhoOutput(
                            stakeholders = , 
                            affected = , ), 
                        where = prime_rat_logic_service_client.models.where_output.WhereOutput(
                            environment = , 
                            components = , 
                            products = , ), 
                        why = prime_rat_logic_service_client.models.why_output.WhyOutput(
                            purpose = , 
                            impact = , ), 
                        how = prime_rat_logic_service_client.models.how_output.HowOutput(
                            approach = , 
                            acceptance = , ), ), 
                    short = 'No short summary found', ),
                provider_fields = { }
            )
        else:
            return ExportedRecord(
                issue_id = '',
                parent_issue_id = '',
                status = '',
                analysis = prime_rat_logic_service_client.models.analysis_record.AnalysisRecord(
                    availability = 56, 
                    confidentiality = 56, 
                    integrity = 56, 
                    risk_score = 56, 
                    risk_score_category = 'intervene', 
                    classification = True, 
                    concerns = [
                        ''
                        ], 
                    is_automated = True, 
                    is_security_enhancement = True, ),
                summary = prime_rat_logic_service_client.models.exported_summary.ExportedSummary(
                    summary = 'No summary found', 
                    questions = prime_rat_logic_service_client.models.questions_output.QuestionsOutput(
                        what = prime_rat_logic_service_client.models.what_output.WhatOutput(
                            summary = prime_rat_logic_service_client.models.content_output.ContentOutput(
                                text = '', 
                                quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                                    quote = [
                                        prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                            quote_text = '', 
                                            source = '', )
                                        ], ), ), 
                            description = prime_rat_logic_service_client.models.content_output.ContentOutput(
                                text = '', ), ), 
                        who = prime_rat_logic_service_client.models.who_output.WhoOutput(
                            stakeholders = , 
                            affected = , ), 
                        where = prime_rat_logic_service_client.models.where_output.WhereOutput(
                            environment = , 
                            components = , 
                            products = , ), 
                        why = prime_rat_logic_service_client.models.why_output.WhyOutput(
                            purpose = , 
                            impact = , ), 
                        how = prime_rat_logic_service_client.models.how_output.HowOutput(
                            approach = , 
                            acceptance = , ), ), 
                    short = 'No short summary found', ),
                provider_fields = { },
        )
        """

    def testExportedRecord(self):
        """Test ExportedRecord"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
