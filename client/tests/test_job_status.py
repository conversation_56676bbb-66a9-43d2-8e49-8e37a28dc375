# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.job_status import JobStatus

class TestJobStatus(unittest.TestCase):
    """JobStatus unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testJobStatus(self):
        """Test JobStatus"""
        # inst = JobStatus()

if __name__ == '__main__':
    unittest.main()
