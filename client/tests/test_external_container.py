# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.external_container import ExternalContainer

class TestExternalContainer(unittest.TestCase):
    """ExternalContainer unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ExternalContainer:
        """Test ExternalContainer
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ExternalContainer`
        """
        model = ExternalContainer()
        if include_optional:
            return ExternalContainer(
                id = 56,
                source_id = 56,
                issue_id = '',
                risk_score = 56,
                title = '',
                provider_fields = {
                    'key' : ''
                    },
                issue_summary_short = '',
                issue_summary_5w = prime_rat_logic_service_client.models.summary5_w.Summary5W(
                    what = prime_rat_logic_service_client.models._what._What(
                        summary = '', 
                        description = '', ), 
                    where = prime_rat_logic_service_client.models._where._Where(
                        environment = '', 
                        components = '', 
                        products = '', ), 
                    who = prime_rat_logic_service_client.models._who._Who(
                        stakeholders = '', 
                        affected = '', ), 
                    why = prime_rat_logic_service_client.models._why._Why(
                        purpose = '', 
                        impact = '', ), 
                    how = prime_rat_logic_service_client.models._how._How(
                        approach = '', 
                        acceptance = '', ), ),
                risk = prime_rat_logic_service_client.models.external_container_risk.ExternalContainerRisk(
                    monitor = 56, 
                    analyze = 56, 
                    intervene = 56, ),
                concerns = [
                    prime_rat_logic_service_client.models.external_prime_concern.ExternalPrimeConcern(
                        id = 56, 
                        short_description = '', 
                        long_description = '', 
                        methodology = prime_rat_logic_service_client.models.issue_analysis_concern_methodology.IssueAnalysisConcernMethodology(
                            category = '', 
                            type = 'Generic', ), 
                        recommendations = [
                            prime_rat_logic_service_client.models.prime_recommendation.PrimeRecommendation(
                                id = '', 
                                name = '', 
                                description = '', 
                                implementations = [
                                    prime_rat_logic_service_client.models.external_implementation.ExternalImplementation(
                                        id = 56, 
                                        concern_id = 56, 
                                        recommendation = '', 
                                        status = 'unknown', 
                                        raci = [
                                            ''
                                            ], 
                                        code_snippets = {
                                            'key' : ''
                                            }, 
                                        control_id = '', 
                                        controls = {
                                            'key' : [
                                                ''
                                                ]
                                            }, )
                                    ], )
                            ], )
                    ]
            )
        else:
            return ExternalContainer(
                source_id = 56,
                issue_id = '',
                risk_score = 56,
                title = '',
                provider_fields = {
                    'key' : ''
                    },
                issue_summary_short = '',
                issue_summary_5w = prime_rat_logic_service_client.models.summary5_w.Summary5W(
                    what = prime_rat_logic_service_client.models._what._What(
                        summary = '', 
                        description = '', ), 
                    where = prime_rat_logic_service_client.models._where._Where(
                        environment = '', 
                        components = '', 
                        products = '', ), 
                    who = prime_rat_logic_service_client.models._who._Who(
                        stakeholders = '', 
                        affected = '', ), 
                    why = prime_rat_logic_service_client.models._why._Why(
                        purpose = '', 
                        impact = '', ), 
                    how = prime_rat_logic_service_client.models._how._How(
                        approach = '', 
                        acceptance = '', ), ),
                risk = prime_rat_logic_service_client.models.external_container_risk.ExternalContainerRisk(
                    monitor = 56, 
                    analyze = 56, 
                    intervene = 56, ),
                concerns = [
                    prime_rat_logic_service_client.models.external_prime_concern.ExternalPrimeConcern(
                        id = 56, 
                        short_description = '', 
                        long_description = '', 
                        methodology = prime_rat_logic_service_client.models.issue_analysis_concern_methodology.IssueAnalysisConcernMethodology(
                            category = '', 
                            type = 'Generic', ), 
                        recommendations = [
                            prime_rat_logic_service_client.models.prime_recommendation.PrimeRecommendation(
                                id = '', 
                                name = '', 
                                description = '', 
                                implementations = [
                                    prime_rat_logic_service_client.models.external_implementation.ExternalImplementation(
                                        id = 56, 
                                        concern_id = 56, 
                                        recommendation = '', 
                                        status = 'unknown', 
                                        raci = [
                                            ''
                                            ], 
                                        code_snippets = {
                                            'key' : ''
                                            }, 
                                        control_id = '', 
                                        controls = {
                                            'key' : [
                                                ''
                                                ]
                                            }, )
                                    ], )
                            ], )
                    ],
        )
        """

    def testExternalContainer(self):
        """Test ExternalContainer"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
