# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.api.psv_api import PsvApi


class TestPsvApi(unittest.IsolatedAsyncioTestCase):
    """PsvApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = PsvApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_bulk_update_psv_status(self) -> None:
        """Test case for bulk_update_psv_status

        Bulk Update Psv Status
        """
        pass

    async def test_delete_source_psv(self) -> None:
        """Test case for delete_source_psv

        Delete Source Psv
        """
        pass

    async def test_export_psv_for_account(self) -> None:
        """Test case for export_psv_for_account

        Export Psv For Account
        """
        pass

    async def test_get_psv(self) -> None:
        """Test case for get_psv

        Get Psv
        """
        pass

    async def test_update_psv_status(self) -> None:
        """Test case for update_psv_status

        Update Psv Status
        """
        pass


if __name__ == '__main__':
    unittest.main()
