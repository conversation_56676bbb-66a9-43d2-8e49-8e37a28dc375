# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.questions_output import QuestionsOutput

class TestQuestionsOutput(unittest.TestCase):
    """QuestionsOutput unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> QuestionsOutput:
        """Test QuestionsOutput
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `QuestionsOutput`
        """
        model = QuestionsOutput()
        if include_optional:
            return QuestionsOutput(
                what = prime_rat_logic_service_client.models.what_output.WhatOutput(
                    summary = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    description = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), ),
                who = prime_rat_logic_service_client.models.who_output.WhoOutput(
                    stakeholders = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    affected = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), ),
                where = prime_rat_logic_service_client.models.where_output.WhereOutput(
                    environment = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    components = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), 
                    products = , ),
                why = prime_rat_logic_service_client.models.why_output.WhyOutput(
                    purpose = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    impact = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), ),
                how = prime_rat_logic_service_client.models.how_output.HowOutput(
                    approach = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    acceptance = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), )
            )
        else:
            return QuestionsOutput(
                what = prime_rat_logic_service_client.models.what_output.WhatOutput(
                    summary = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    description = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), ),
                who = prime_rat_logic_service_client.models.who_output.WhoOutput(
                    stakeholders = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    affected = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), ),
                where = prime_rat_logic_service_client.models.where_output.WhereOutput(
                    environment = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    components = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), 
                    products = , ),
                why = prime_rat_logic_service_client.models.why_output.WhyOutput(
                    purpose = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    impact = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), ),
                how = prime_rat_logic_service_client.models.how_output.HowOutput(
                    approach = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', 
                        quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                            quote = [
                                prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                    quote_text = '', 
                                    source = '', )
                                ], ), ), 
                    acceptance = prime_rat_logic_service_client.models.content_output.ContentOutput(
                        text = '', ), ),
        )
        """

    def testQuestionsOutput(self):
        """Test QuestionsOutput"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
