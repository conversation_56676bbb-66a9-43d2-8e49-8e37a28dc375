# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.concern_type import ConcernType

class TestConcernType(unittest.TestCase):
    """ConcernType unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testConcernType(self):
        """Test ConcernType"""
        # inst = ConcernType()

if __name__ == '__main__':
    unittest.main()
