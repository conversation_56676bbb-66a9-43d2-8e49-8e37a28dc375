# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.summary5_w import Summary5W

class TestSummary5W(unittest.TestCase):
    """Summary5W unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> Summary5W:
        """Test Summary5W
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `Summary5W`
        """
        model = Summary5W()
        if include_optional:
            return Summary5W(
                what = prime_rat_logic_service_client.models._what._What(
                    summary = '', 
                    description = '', ),
                where = prime_rat_logic_service_client.models._where._Where(
                    environment = '', 
                    components = '', 
                    products = '', ),
                who = prime_rat_logic_service_client.models._who._Who(
                    stakeholders = '', 
                    affected = '', ),
                why = prime_rat_logic_service_client.models._why._Why(
                    purpose = '', 
                    impact = '', ),
                how = prime_rat_logic_service_client.models._how._How(
                    approach = '', 
                    acceptance = '', )
            )
        else:
            return Summary5W(
                what = prime_rat_logic_service_client.models._what._What(
                    summary = '', 
                    description = '', ),
                where = prime_rat_logic_service_client.models._where._Where(
                    environment = '', 
                    components = '', 
                    products = '', ),
                who = prime_rat_logic_service_client.models._who._Who(
                    stakeholders = '', 
                    affected = '', ),
                why = prime_rat_logic_service_client.models._why._Why(
                    purpose = '', 
                    impact = '', ),
                how = prime_rat_logic_service_client.models._how._How(
                    approach = '', 
                    acceptance = '', ),
        )
        """

    def testSummary5W(self):
        """Test Summary5W"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
