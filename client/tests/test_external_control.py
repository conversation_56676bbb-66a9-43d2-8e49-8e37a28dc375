# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.external_control import ExternalControl

class TestExternalControl(unittest.TestCase):
    """ExternalControl unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ExternalControl:
        """Test ExternalControl
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ExternalControl`
        """
        model = ExternalControl()
        if include_optional:
            return ExternalControl(
                id = '',
                name = '',
                description = '',
                control_names = [
                    ''
                    ],
                framework = 'NIST',
                implementations = [
                    prime_rat_logic_service_client.models.external_implementation.ExternalImplementation(
                        id = 56, 
                        concern_id = 56, 
                        recommendation = '', 
                        status = 'unknown', 
                        raci = [
                            ''
                            ], 
                        code_snippets = {
                            'key' : ''
                            }, 
                        control_id = '', 
                        controls = {
                            'key' : [
                                ''
                                ]
                            }, )
                    ]
            )
        else:
            return ExternalControl(
                id = '',
                name = '',
                description = '',
                control_names = [
                    ''
                    ],
                framework = 'NIST',
                implementations = [
                    prime_rat_logic_service_client.models.external_implementation.ExternalImplementation(
                        id = 56, 
                        concern_id = 56, 
                        recommendation = '', 
                        status = 'unknown', 
                        raci = [
                            ''
                            ], 
                        code_snippets = {
                            'key' : ''
                            }, 
                        control_id = '', 
                        controls = {
                            'key' : [
                                ''
                                ]
                            }, )
                    ],
        )
        """

    def testExternalControl(self):
        """Test ExternalControl"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
