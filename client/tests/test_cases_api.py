# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.api.cases_api import CasesApi


class TestCasesApi(unittest.IsolatedAsyncioTestCase):
    """CasesApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = CasesApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_add_comment(self) -> None:
        """Test case for add_comment

        Add Comment
        """
        pass

    async def test_add_watcher(self) -> None:
        """Test case for add_watcher

        Add Watcher
        """
        pass

    async def test_autocomplete(self) -> None:
        """Test case for autocomplete

        Autocomplete
        """
        pass

    async def test_autocomplete_search_global_cases(self) -> None:
        """Test case for autocomplete_search_global_cases

        Autocomplete Search Global Cases
        """
        pass

    async def test_bulk_update_cases(self) -> None:
        """Test case for bulk_update_cases

        Bulk Update Cases
        """
        pass

    async def test_delete_cases_for_source(self) -> None:
        """Test case for delete_cases_for_source

        Delete Cases For Source
        """
        pass

    async def test_export_for_account(self) -> None:
        """Test case for export_for_account

        Export For Account
        """
        pass

    async def test_generate_recommendations_for_concern_ids(self) -> None:
        """Test case for generate_recommendations_for_concern_ids

        Generate Recommendations For Concern Ids
        """
        pass

    async def test_get_case(self) -> None:
        """Test case for get_case

        Get Case
        """
        pass

    async def test_get_case_by_id(self) -> None:
        """Test case for get_case_by_id

        Get Case By Id
        """
        pass

    async def test_get_case_id(self) -> None:
        """Test case for get_case_id

        Get Case Id
        """
        pass

    async def test_get_cases_for_account(self) -> None:
        """Test case for get_cases_for_account

        Get Cases For Account
        """
        pass

    async def test_get_cases_for_account_and_source(self) -> None:
        """Test case for get_cases_for_account_and_source

        Get Cases For Account And Source
        """
        pass

    async def test_get_labels(self) -> None:
        """Test case for get_labels

        Get Labels
        """
        pass

    async def test_set_labels(self) -> None:
        """Test case for set_labels

        Set Labels
        """
        pass

    async def test_update_recommendations(self) -> None:
        """Test case for update_recommendations

        Update Recommendations
        """
        pass

    async def test_update_risk_score_category(self) -> None:
        """Test case for update_risk_score_category

        Update Risk Score Category
        """
        pass

    async def test_update_status(self) -> None:
        """Test case for update_status

        Update Status
        """
        pass

    async def test_write_back(self) -> None:
        """Test case for write_back

        Write Back
        """
        pass


if __name__ == '__main__':
    unittest.main()
