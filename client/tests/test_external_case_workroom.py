# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.external_case_workroom import ExternalCaseWorkroom

class TestExternalCaseWorkroom(unittest.TestCase):
    """ExternalCaseWorkroom unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ExternalCaseWorkroom:
        """Test ExternalCaseWorkroom
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ExternalCaseWorkroom`
        """
        model = ExternalCaseWorkroom()
        if include_optional:
            return ExternalCaseWorkroom(
                account_id = '',
                source_id = 56,
                issue_id = '',
                case_id = 56,
                status = 'open',
                issue_analysis = prime_rat_logic_service_client.models.external_issue_analysis_workroom.ExternalIssueAnalysisWorkroom(
                    account_id = '', 
                    source_id = 56, 
                    issue_id = '', 
                    risk_factors = prime_rat_logic_service_client.models.risk_factors.RiskFactors(
                        confidentiality = 0.0, 
                        confidentiality_explanation = '', 
                        integrity = 0.0, 
                        integrity_explanation = '', 
                        availability = 0.0, 
                        availability_explanation = '', 
                        third_party_management = 0.0, 
                        third_party_management_explanation = '', 
                        compliance = 0.0, 
                        compliance_explanation = '', 
                        severity = 1.0, 
                        severity_explanation = '', 
                        scope = 1.0, 
                        scope_explanation = '', 
                        confidentiality_level = 'low', 
                        integrity_level = 'low', 
                        availability_level = 'low', 
                        third_party_management_level = 'low', 
                        compliance_level = 'low', ), 
                    issue_hash = '', 
                    confidence = 0.0, 
                    is_automated = True, 
                    risk_score = 0.0, 
                    mitre_categories = [
                        ''
                        ], 
                    linddun_categories = [
                        ''
                        ], 
                    fire_summary = '', 
                    risk_score_category = 'intervene', 
                    confidence_level = 'low', ),
                write_back_recommendations = True,
                title = '',
                link = '',
                labels = [
                    ''
                    ],
                provider_fields = {
                    'key' : ''
                    },
                provider_fields_min_schema = {
                    'key' : prime_rat_logic_service_client.models.provider_field_info.ProviderFieldInfo(
                        type = 'string', 
                        id = '', 
                        name = '', )
                    },
                parents = [
                    ''
                    ],
                progress_percentage = 56
            )
        else:
            return ExternalCaseWorkroom(
                account_id = '',
                source_id = 56,
                issue_id = '',
                case_id = 56,
                status = 'open',
                issue_analysis = prime_rat_logic_service_client.models.external_issue_analysis_workroom.ExternalIssueAnalysisWorkroom(
                    account_id = '', 
                    source_id = 56, 
                    issue_id = '', 
                    risk_factors = prime_rat_logic_service_client.models.risk_factors.RiskFactors(
                        confidentiality = 0.0, 
                        confidentiality_explanation = '', 
                        integrity = 0.0, 
                        integrity_explanation = '', 
                        availability = 0.0, 
                        availability_explanation = '', 
                        third_party_management = 0.0, 
                        third_party_management_explanation = '', 
                        compliance = 0.0, 
                        compliance_explanation = '', 
                        severity = 1.0, 
                        severity_explanation = '', 
                        scope = 1.0, 
                        scope_explanation = '', 
                        confidentiality_level = 'low', 
                        integrity_level = 'low', 
                        availability_level = 'low', 
                        third_party_management_level = 'low', 
                        compliance_level = 'low', ), 
                    issue_hash = '', 
                    confidence = 0.0, 
                    is_automated = True, 
                    risk_score = 0.0, 
                    mitre_categories = [
                        ''
                        ], 
                    linddun_categories = [
                        ''
                        ], 
                    fire_summary = '', 
                    risk_score_category = 'intervene', 
                    confidence_level = 'low', ),
                write_back_recommendations = True,
                title = '',
                link = '',
                labels = [
                    ''
                    ],
                provider_fields = {
                    'key' : ''
                    },
                progress_percentage = 56,
        )
        """

    def testExternalCaseWorkroom(self):
        """Test ExternalCaseWorkroom"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
