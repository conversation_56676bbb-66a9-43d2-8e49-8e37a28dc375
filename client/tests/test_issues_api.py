# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.api.issues_api import IssuesApi


class TestIssuesApi(unittest.IsolatedAsyncioTestCase):
    """IssuesApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = IssuesApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_get_container_children_data(self) -> None:
        """Test case for get_container_children_data

        Get Container Children Data
        """
        pass

    async def test_get_issues_keys(self) -> None:
        """Test case for get_issues_keys

        Get-Issues-Keys
        """
        pass


if __name__ == '__main__':
    unittest.main()
