# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.external_case_history import ExternalCaseHistory

class TestExternalCaseHistory(unittest.TestCase):
    """ExternalCaseHistory unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ExternalCaseHistory:
        """Test ExternalCaseHistory
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ExternalCaseHistory`
        """
        model = ExternalCaseHistory()
        if include_optional:
            return ExternalCaseHistory(
                user = '',
                audit_action = 'create_case',
                audit_action_args = { },
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f')
            )
        else:
            return ExternalCaseHistory(
                user = '',
                audit_action = 'create_case',
                audit_action_args = { },
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
        )
        """

    def testExternalCaseHistory(self):
        """Test ExternalCaseHistory"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
