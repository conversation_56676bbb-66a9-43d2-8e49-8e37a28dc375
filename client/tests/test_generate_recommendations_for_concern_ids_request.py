# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.generate_recommendations_for_concern_ids_request import GenerateRecommendationsForConcernIdsRequest

class TestGenerateRecommendationsForConcernIdsRequest(unittest.TestCase):
    """GenerateRecommendationsForConcernIdsRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GenerateRecommendationsForConcernIdsRequest:
        """Test GenerateRecommendationsForConcernIdsRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GenerateRecommendationsForConcernIdsRequest`
        """
        model = GenerateRecommendationsForConcernIdsRequest()
        if include_optional:
            return GenerateRecommendationsForConcernIdsRequest(
                concern_ids = [
                    56
                    ]
            )
        else:
            return GenerateRecommendationsForConcernIdsRequest(
                concern_ids = [
                    56
                    ],
        )
        """

    def testGenerateRecommendationsForConcernIdsRequest(self):
        """Test GenerateRecommendationsForConcernIdsRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
