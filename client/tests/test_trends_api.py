# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.api.trends_api import TrendsApi


class TestTrendsApi(unittest.IsolatedAsyncioTestCase):
    """TrendsApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = TrendsApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_get_cases_by_risk_category(self) -> None:
        """Test case for get_cases_by_risk_category

        Get Cases By Risk Category
        """
        pass

    async def test_get_cases_by_status(self) -> None:
        """Test case for get_cases_by_status

        Get Cases By Status
        """
        pass

    async def test_get_count_psv(self) -> None:
        """Test case for get_count_psv

        Get Count Psv
        """
        pass

    async def test_get_linddun(self) -> None:
        """Test case for get_linddun

        Get Linddun
        """
        pass

    async def test_get_mitre(self) -> None:
        """Test case for get_mitre

        Get Mitre
        """
        pass


if __name__ == '__main__':
    unittest.main()
