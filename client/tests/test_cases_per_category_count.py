# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.cases_per_category_count import CasesPerCategoryCount

class TestCasesPerCategoryCount(unittest.TestCase):
    """CasesPerCategoryCount unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> CasesPerCategoryCount:
        """Test CasesPerCategoryCount
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `CasesPerCategoryCount`
        """
        model = CasesPerCategoryCount()
        if include_optional:
            return CasesPerCategoryCount(
                risk_scores = {
                    'key' : 56
                    }
            )
        else:
            return CasesPerCategoryCount(
                risk_scores = {
                    'key' : 56
                    },
        )
        """

    def testCasesPerCategoryCount(self):
        """Test CasesPerCategoryCount"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
