# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.api.jobs_api import JobsApi


class TestJobsApi(unittest.IsolatedAsyncioTestCase):
    """JobsApi unit test stubs"""

    async def asyncSetUp(self) -> None:
        self.api = JobsApi()

    async def asyncTearDown(self) -> None:
        await self.api.api_client.close()

    async def test_add_job(self) -> None:
        """Test case for add_job

        Add Job
        """
        pass

    async def test_get_job(self) -> None:
        """Test case for get_job

        Get Job
        """
        pass

    async def test_get_jobs(self) -> None:
        """Test case for get_jobs

        Get Jobs
        """
        pass


if __name__ == '__main__':
    unittest.main()
