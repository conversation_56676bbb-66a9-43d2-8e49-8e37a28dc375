# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.bulk_update_cases_request import BulkUpdateCasesRequest

class TestBulkUpdateCasesRequest(unittest.TestCase):
    """BulkUpdateCasesRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> BulkUpdateCasesRequest:
        """Test BulkUpdateCasesRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `BulkUpdateCasesRequest`
        """
        model = BulkUpdateCasesRequest()
        if include_optional:
            return BulkUpdateCasesRequest(
                status = 'open',
                dismissed_reason = '',
                labels = [
                    'k0'
                    ],
                risk_score_category = 'intervene',
                issues_ids = [
                    ''
                    ]
            )
        else:
            return BulkUpdateCasesRequest(
                issues_ids = [
                    ''
                    ],
        )
        """

    def testBulkUpdateCasesRequest(self):
        """Test BulkUpdateCasesRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
