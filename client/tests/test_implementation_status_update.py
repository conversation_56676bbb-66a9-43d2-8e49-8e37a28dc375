# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.implementation_status_update import ImplementationStatusUpdate

class TestImplementationStatusUpdate(unittest.TestCase):
    """ImplementationStatusUpdate unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ImplementationStatusUpdate:
        """Test ImplementationStatusUpdate
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ImplementationStatusUpdate`
        """
        model = ImplementationStatusUpdate()
        if include_optional:
            return ImplementationStatusUpdate(
                id = 56,
                status = 'approved',
                concern_id = 56,
                control_id = ''
            )
        else:
            return ImplementationStatusUpdate(
                id = 56,
                status = 'approved',
        )
        """

    def testImplementationStatusUpdate(self):
        """Test ImplementationStatusUpdate"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
