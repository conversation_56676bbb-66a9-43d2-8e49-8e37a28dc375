# coding: utf-8

"""
    Service API 

    Service API

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from prime_rat_logic_service_client.models.who_output import WhoOutput

class TestWhoOutput(unittest.TestCase):
    """WhoOutput unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> WhoOutput:
        """Test WhoOutput
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `WhoOutput`
        """
        model = WhoOutput()
        if include_optional:
            return WhoOutput(
                stakeholders = prime_rat_logic_service_client.models.content_output.ContentOutput(
                    text = '', 
                    quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                        quote = [
                            prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                quote_text = '', 
                                source = '', )
                            ], ), ),
                affected = prime_rat_logic_service_client.models.content_output.ContentOutput(
                    text = '', 
                    quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                        quote = [
                            prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                quote_text = '', 
                                source = '', )
                            ], ), )
            )
        else:
            return WhoOutput(
                stakeholders = prime_rat_logic_service_client.models.content_output.ContentOutput(
                    text = '', 
                    quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                        quote = [
                            prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                quote_text = '', 
                                source = '', )
                            ], ), ),
                affected = prime_rat_logic_service_client.models.content_output.ContentOutput(
                    text = '', 
                    quotes = prime_rat_logic_service_client.models.research_utils_models_context_quotes_output.ResearchUtilsModelsContextQuotesOutput(
                        quote = [
                            prime_rat_logic_service_client.models.research_utils_models_context_quote.ResearchUtilsModelsContextQuote(
                                quote_text = '', 
                                source = '', )
                            ], ), ),
        )
        """

    def testWhoOutput(self):
        """Test WhoOutput"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
