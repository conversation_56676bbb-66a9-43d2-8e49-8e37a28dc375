from datetime import UTC, datetime, timedelta

import pytest
from prime_config_service_client import NotificationsConfig, ScanCompletedNotificationsSettings
from prime_jobs import C<PERSON><PERSON>ob, JobStatus
from prime_jobs.errors import JobNotFoundError
from prime_notification_service_client import NotifyResponse
from prime_tests import MockResponse, service_mocker

from service.db import ServiceDAL
from service.job_type import JobType
from service.k8s_jobs.classification_job.notify_scan_done import NotifyScanDone
from service.k8s_jobs.job_spawners import RatJobSpawnerBase
from service.models import RISK_SCORE_RANGES, PsvStatus, RiskScoreCategory
from tests.case_test_utils import create_cases
from tests.mock_utils import get_issue_id
from tests.psv_utils import create_psv


class MockJobSpawner(RatJobSpawnerBase):
    async def spawn_job(self, job_id: int) -> str | None:
        return f"test-job-{job_id}"


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
async def test_notify_classification_done_notify(service_dal_fixture: ServiceDAL, account_id: str):
    levels = [
        RiskScoreCategory.INTERVENE,
        RiskScoreCategory.MONITOR,
        RiskScoreCategory.ANALYZE,
        RiskScoreCategory.INTERVENE,
        RiskScoreCategory.INTERVENE,
        RiskScoreCategory.MONITOR,
        RiskScoreCategory.ANALYZE,
        RiskScoreCategory.INTERVENE,
        RiskScoreCategory.INTERVENE,
        RiskScoreCategory.MONITOR,
    ]
    scores = [RISK_SCORE_RANGES[level].start for level in levels]
    start_at = datetime.now(UTC)
    await create_cases(service_dal_fixture, extra=[{"risk_score": score} for score in scores])
    job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
        MockJobSpawner, CreateJob(account_id=account_id, created_by="test", job_type="dummy")
    )
    psvs = [
        await create_psv(service_dal_fixture, get_issue_id(i + 1), description=f"test description {i}")
        for i in range(7)
    ]
    # create psvs that don't match the notify filters
    for psv in psvs[:2]:
        psv.created_at = psv.created_at.replace(year=psv.created_at.year - 1)
    psvs[-1].status = PsvStatus.DISMISSED
    psvs[-2].has_psv = False
    await service_dal_fixture.session.commit()

    with (
        service_mocker("notification-service") as notification_mocker,
        service_mocker("config-service") as config_service_mocker,
    ):
        notification_config = NotificationsConfig(
            scan_completed_notifications_settings=ScanCompletedNotificationsSettings(
                slack_enabled=True,
                slack_channel_ids=["test_channel"],
                mail_recipients=["<EMAIL>"],
                mail_enabled=True,
            )
        )
        config_service_mocker.get(f"notifications/{job.account_id}", MockResponse(notification_config.model_dump()))

        notification_response = NotifyResponse(sent=True)
        notification_mocker.post(f"/notify/mail/{account_id}", MockResponse(notification_response.to_json()))
        notification_mocker.post(f"/notify/slack/{account_id}", MockResponse(notification_response.to_json()))

        ncd = await NotifyScanDone.build(account_id, service_dal_fixture, start_at, job.id)
        assert (
            ncd.model_dump_json(exclude={"account_id", "user"})
            == '{"new_interventions":3,"new_cases":7,"intervene_amount":3,"analyze_amount":2,"monitor_amount":2,"total_sum":7,"new_open_psv_amount":3,"total_open_psv_amount":5,"mail_recipients":["<EMAIL>"],"slack_channel_ids":["test_channel"]}'
        )

        await ncd.notify()
        assert notification_mocker.requests["POST"][f"notify/slack/{account_id}"][0].args[3] == {
            "analyze_amount": 2,
            "intervene_amount": 3,
            "monitor_amount": 2,
            "new_cases": 7,
            "new_interventions": 3,
            "total_sum": 7,
            "new_open_psv_amount": 3,
            "notification_type": "scan_completed",
            "total_open_psv_amount": 5,
            "user": "test",
            "slack_channel_ids": ["test_channel"],
        }
        assert notification_mocker.requests["POST"][f"notify/mail/{account_id}"][0].args[3] == {
            "analyze_amount": 2,
            "intervene_amount": 3,
            "monitor_amount": 2,
            "new_cases": 7,
            "new_interventions": 3,
            "total_sum": 7,
            "new_open_psv_amount": 3,
            "notification_type": "scan_completed",
            "total_open_psv_amount": 5,
            "user": "test",
            "mail_recipients": ["<EMAIL>"],
        }


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
@pytest.mark.parametrize(
    "scan_completed_notifications_settings",
    [
        ScanCompletedNotificationsSettings(slack_enabled=True, slack_channel_ids=["test_channel"]),
        ScanCompletedNotificationsSettings(mail_enabled=True, mail_recipients=["<EMAIL>"]),
    ],
)
async def test_notify_classification_done_skip_notify(
    scan_completed_notifications_settings: ScanCompletedNotificationsSettings,
    service_dal_fixture: ServiceDAL,
    account_id: str,
):
    job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
        MockJobSpawner, CreateJob(account_id=account_id, created_by="test", job_type="dummy")
    )
    await service_dal_fixture.session.commit()

    with (
        service_mocker("notification-service") as notification_mocker,
        service_mocker("config-service") as config_service_mocker,
    ):
        notification_config = NotificationsConfig(
            scan_completed_notifications_settings=scan_completed_notifications_settings
        )
        config_service_mocker.get(f"notifications/{job.account_id}", MockResponse(notification_config.model_dump()))

        notification_response = NotifyResponse(sent=True)
        notification_mocker.post(f"/notify/mail/{account_id}", MockResponse(notification_response.to_json()))
        notification_mocker.post(f"/notify/slack/{account_id}", MockResponse(notification_response.to_json()))

        ncd = await NotifyScanDone.build(account_id, service_dal_fixture, datetime.now(UTC), job.id)

        await ncd.notify()
        if scan_completed_notifications_settings.slack_enabled:
            assert f"notify/slack/{account_id}" in notification_mocker.requests["POST"]
        else:
            assert f"notify/slack/{account_id}" not in notification_mocker.requests["POST"]
        if scan_completed_notifications_settings.mail_enabled:
            assert f"notify/mail/{account_id}" in notification_mocker.requests["POST"]
        else:
            assert f"notify/mail/{account_id}" not in notification_mocker.requests["POST"]


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
async def test_notify_classification_done_job_not_exist(service_dal_fixture: ServiceDAL, account_id: str):
    start_at = datetime.now(UTC)
    with service_mocker("config-service") as config_service_mocker, pytest.raises(JobNotFoundError):
        notification_config = NotificationsConfig(
            scan_completed_notifications_settings=ScanCompletedNotificationsSettings(
                slack_enabled=True, slack_channel_ids=["test_channel"]
            )
        )
        config_service_mocker.get(f"notifications/{account_id}", MockResponse(notification_config.model_dump()))
        await NotifyScanDone.build(account_id, service_dal_fixture, start_at, 1)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestNotifyGetStartTime:
    async def test_update_job_is_start_time(self, service_dal_fixture: ServiceDAL, account_id: str):
        update_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner,
            CreateJob(
                account_id=account_id,
                created_by="test",
                job_type=JobType.UPDATE_ISSUES,
                job_args={"update_fields_only": "false"},
            ),
        )
        update_job.status = JobStatus.COMPLETED
        other_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner, CreateJob(account_id=account_id, created_by="test", job_type="dummy")
        )
        await service_dal_fixture.session.commit()
        start_time = await NotifyScanDone._get_start_time(service_dal_fixture, account_id, other_job.created_at)
        assert update_job.created_at == start_time

    async def test_no_matching_update_job(self, service_dal_fixture: ServiceDAL, account_id: str):
        update_job1 = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner,
            CreateJob(
                account_id=account_id,
                created_by="test",
                job_type=JobType.UPDATE_ISSUES,
                job_args={"update_fields_only": "true"},
            ),
        )
        update_job1.status = JobStatus.COMPLETED
        _ = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner,
            CreateJob(
                account_id=account_id,
                created_by="test",
                job_type=JobType.UPDATE_ISSUES,
                job_args={"update_fields_only": "false"},
            ),
        )
        other_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner, CreateJob(account_id=account_id, created_by="test", job_type="dummy")
        )
        await service_dal_fixture.session.commit()
        start_time = await NotifyScanDone._get_start_time(service_dal_fixture, account_id, other_job.created_at)
        assert other_job.created_at == start_time

    async def test_update_job_started_24h_ago(self, service_dal_fixture: ServiceDAL, account_id: str):
        update_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner,
            CreateJob(
                account_id=account_id,
                created_by="test",
                job_type=JobType.UPDATE_ISSUES,
                job_args={"update_fields_only": "false"},
            ),
        )
        update_job.status = JobStatus.COMPLETED
        update_job.created_at = update_job.created_at - timedelta(hours=26)
        other_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner, CreateJob(account_id=account_id, created_by="test", job_type="dummy")
        )
        await service_dal_fixture.session.commit()
        start_time = await NotifyScanDone._get_start_time(service_dal_fixture, account_id, other_job.created_at)
        assert other_job.created_at - timedelta(days=1) == start_time

    async def test_update_job_started_after_current_job(self, service_dal_fixture: ServiceDAL, account_id: str):
        other_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner, CreateJob(account_id=account_id, created_by="test", job_type="dummy")
        )
        update_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            MockJobSpawner,
            CreateJob(
                account_id=account_id,
                created_by="test",
                job_type=JobType.UPDATE_ISSUES,
                job_args={"update_fields_only": "false"},
            ),
        )
        update_job.status = JobStatus.COMPLETED
        await service_dal_fixture.session.commit()
        start_time = await NotifyScanDone._get_start_time(service_dal_fixture, account_id, other_job.created_at)
        assert other_job.created_at - timedelta(days=1) == start_time
