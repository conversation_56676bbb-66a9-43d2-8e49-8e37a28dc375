import copy
from unittest import mock
from unittest.mock import <PERSON><PERSON>ock

import pytest
from jira import <PERSON>IR<PERSON><PERSON><PERSON><PERSON>
from prime_jira_client import PrimeJira<PERSON>lient

from service.db import ServiceDAL
from service.errors import WriteBackError
from service.logic.write_back import <PERSON>ra<PERSON>rite<PERSON>ackHand<PERSON>, _build_concerns_recommendations_map, _get_comment_template
from service.models import ImplementationStatus
from service.models.jira_comment_data import Concern, Control, JiraCommentData, Recommendation
from tests.case_test_utils import TEST_RECOMMENDATIONS, build_external_case_for_testing, create_case
from tests.mock_utils import FAKE_PROVIDER_FIELDS_DATA, SOURCE_ID, TESTS_ISSUES, _basic_mocks, resources_dir


class MockJiraComment:
    def __init__(self, comment_id: str):
        self.id = comment_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestJiraWriteBackHandler:
    WRITE_BACK_LABEL_TEXT = "write-to-jira"

    def test_get_comment_template(self):
        template_data = JiraCommentData(
            data={
                1: Concern(
                    short_description="short description concern 1",
                    controls={
                        "1": Control(
                            name="control name 1",
                            description="control description 1",
                            recommendations=[
                                Recommendation(id=1, text="text recommendation 1"),
                                Recommendation(id=455, text="text recommendation 2"),
                                Recommendation(id=456, text="text recommendation 3"),
                            ],
                        ),
                    },
                ),
                2: Concern(
                    short_description="short description concern 2",
                    controls={
                        "2": Control(
                            name="control name 2",
                            description="control description 2",
                            recommendations=[
                                Recommendation(id=1, text="text recommendation 4"),
                                Recommendation(id=455, text="text recommendation 5"),
                                Recommendation(id=456, text="text recommendation 6"),
                            ],
                        ),
                    },
                ),
            }
        )

        result = _get_comment_template(template_data)
        expected_template = (resources_dir / "expected_jira_comment.txt").read_text()
        assert result.strip() == expected_template.strip()

    async def test_jira_write_back_handler_without_recommendation(
        self, service_dal_fixture: ServiceDAL, account_id: str
    ):
        with _basic_mocks():
            test_issue = TESTS_ISSUES[0]
            case = await create_case(
                service_dal_fixture,
                test_issue,
                provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
            )
            case, external_case = await build_external_case_for_testing(case, service_dal_fixture, test_issue)

            assert case.write_back_ref_id is None
            write_back_handler = JiraWriteBackHandler(account_id, SOURCE_ID, test_issue, service_dal_fixture)
            write_back_ref_id = await write_back_handler.write_back()
            assert write_back_ref_id is None

    async def test_jira_write_back_handler_with_unapproved_recommendation(
        self, service_dal_fixture: ServiceDAL, account_id: str
    ):
        recommendations = copy.deepcopy(TEST_RECOMMENDATIONS)
        recommendations[0].status = ImplementationStatus.DISMISSED
        recommendations[1].status = ImplementationStatus.UNKNOWN
        test_issue = TESTS_ISSUES[0]
        case = await create_case(
            service_dal_fixture,
            test_issue,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        _, _ = await build_external_case_for_testing(case, service_dal_fixture, test_issue, recommendations)

        with _basic_mocks():
            write_back_handler = JiraWriteBackHandler(account_id, SOURCE_ID, test_issue, service_dal_fixture)
            write_back_ref_id = await write_back_handler.write_back()
            assert write_back_ref_id is None

    async def test_jira_write_back_add_jira_labels_multiple(self, service_dal_fixture: ServiceDAL, account_id: str):
        recommendations = copy.deepcopy(TEST_RECOMMENDATIONS)
        recommendations[0].status = ImplementationStatus.APPROVED
        recommendations[1].status = ImplementationStatus.APPROVED
        test_issue = TESTS_ISSUES[0]
        case = await create_case(
            service_dal_fixture,
            test_issue,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        case, external_case = await build_external_case_for_testing(
            case, service_dal_fixture, test_issue, recommendations
        )

        with (
            _basic_mocks(),
            mock.patch.object(PrimeJiraClient, "add_comment") as jira_client_mocked,
        ):
            write_back_handler = JiraWriteBackHandler(account_id, SOURCE_ID, test_issue, service_dal_fixture)
            jira_client_mocked.return_value = MockJiraComment("123")

            await write_back_handler.write_back()

            await write_back_handler.write_back()

        await service_dal_fixture.session.refresh(case)
        assert case.labels == [self.WRITE_BACK_LABEL_TEXT]

        account_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        labels = [account_label.name for account_label in account_labels]
        assert len(labels) == 1
        assert self.WRITE_BACK_LABEL_TEXT in labels

    async def test_jira_write_back_add_jira_labels(self, service_dal_fixture: ServiceDAL, account_id: str):
        recommendations = copy.deepcopy(TEST_RECOMMENDATIONS)
        recommendations[0].status = ImplementationStatus.APPROVED
        recommendations[1].status = ImplementationStatus.APPROVED
        test_issue = TESTS_ISSUES[0]
        case = await create_case(
            service_dal_fixture,
            test_issue,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        case, external_case = await build_external_case_for_testing(
            case, service_dal_fixture, test_issue, recommendations
        )

        with (
            _basic_mocks(),
            mock.patch.object(PrimeJiraClient, "add_comment") as jira_client_mocked,
        ):
            write_back_handler = JiraWriteBackHandler(account_id, SOURCE_ID, test_issue, service_dal_fixture)
            jira_client_mocked.return_value = MockJiraComment("123")

            await write_back_handler.write_back()

        await service_dal_fixture.session.refresh(case)
        assert case.labels == [self.WRITE_BACK_LABEL_TEXT]

        account_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        labels = [account_label.name for account_label in account_labels]
        assert len(labels) == 1
        assert self.WRITE_BACK_LABEL_TEXT in labels

    async def test_jira_write_back_handler_with_selected_recommendation(
        self, service_dal_fixture: ServiceDAL, account_id: str
    ):
        recommendations = copy.deepcopy(TEST_RECOMMENDATIONS)
        recommendations[0].status = ImplementationStatus.APPROVED
        recommendations[1].status = ImplementationStatus.APPROVED
        test_issue = TESTS_ISSUES[0]
        case = await create_case(
            service_dal_fixture,
            test_issue,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        case, external_case = await build_external_case_for_testing(
            case, service_dal_fixture, test_issue, recommendations
        )

        concerns_recommendations_map = await _build_concerns_recommendations_map(external_case)
        write_back_text = _get_comment_template(concerns_recommendations_map)

        with (
            _basic_mocks(),
            mock.patch.object(PrimeJiraClient, "add_comment") as jira_client_mocked,
        ):
            write_back_handler = JiraWriteBackHandler(account_id, SOURCE_ID, test_issue, service_dal_fixture)
            jira_client_mocked.return_value = MockJiraComment("123")

            await write_back_handler.write_back()
            jira_client_mocked.assert_called_once()
            jira_client_mocked.assert_called_once_with(test_issue, write_back_text)

        await service_dal_fixture.session.refresh(case)
        assert case.write_back_ref_id == "123"

    async def test_jira_write_back_handler_with_error(self, service_dal_fixture: ServiceDAL, account_id: str):
        mock_comment = MagicMock()
        mock_comment.id = "123"

        recommendations = copy.deepcopy(TEST_RECOMMENDATIONS)
        recommendations[0].status = ImplementationStatus.APPROVED
        recommendations[1].status = ImplementationStatus.APPROVED
        test_issue = TESTS_ISSUES[0]
        case = await create_case(
            service_dal_fixture,
            test_issue,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        case, external_case = await build_external_case_for_testing(
            case, service_dal_fixture, test_issue, recommendations
        )
        case.write_back_ref_id = "100"
        await service_dal_fixture.session.commit()

        write_back_handler = JiraWriteBackHandler(account_id, SOURCE_ID, test_issue, service_dal_fixture)
        with (
            _basic_mocks(),
            mock.patch.object(PrimeJiraClient, "add_comment") as jira_client_add_comment,
            mock.patch.object(PrimeJiraClient, "comment") as jira_client_comment,
        ):
            jira_client_add_comment.return_value = mock_comment
            jira_client_comment.side_effect = JIRAError(status_code=404)
            write_back_result = await write_back_handler.write_back()
            assert write_back_result == "123"

            jira_client_comment.side_effect = JIRAError(status_code=400)
            with pytest.raises(WriteBackError):
                assert await write_back_handler.write_back()
