import pytest

from service.logic.filters_and_sort import CaseFilters
from service.models import ProviderFieldType
from service.models.cases import CaseStatus
from service.models.filters_and_sort import Filter, Operator

DEFAULT_CASE_FILTER = Filter(field="status", value="open")


def _assert_query(q, expected_query: str):
    assert str(q.compile(compile_kwargs={"literal_binds": True})).split("FROM", maxsplit=1)[1] == expected_query


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestCaseFilters:
    def test_create_case_filter(self):
        expected_query = (
            " cases JOIN issues_analysis ON cases.issue_analysis_id = issues_analysis.id "
            "AND issues_analysis.classification = true AND issues_analysis.is_automated = false "
            "AND issues_analysis.is_security_enhancement = false \n"
            "WHERE cases.status = 'OPEN' "
            "AND NOT (EXISTS (SELECT * \nFROM cases AS cases_1 \nWHERE cases_1.account_id = cases.account_id AND cases_1.source_id = cases.source_id AND cases_1.parent_issue_id = cases.issue_id))"
        )

        f = CaseFilters([Filter(field="classification", value="true", op=Operator.NE)])
        assert f.filters_list == [
            Filter(field="classification", value="true", op=Operator.NE),
        ]

        f = CaseFilters.default_workroom_filter()
        assert f.filters_list == [
            Filter(field="status", value="open"),
            Filter(field="classification", value="true", op=Operator.EQ),
            Filter(field="is_automated", value="false", op=Operator.EQ),
            Filter(field="is_security_enhancement", value="false", op=Operator.EQ),
            Filter(field="container", value="false"),
        ]
        assert f.allowed_filters.sort() == ["status", "classification", "source_id", "account_id"].sort()
        assert f.status == CaseStatus.OPEN
        query = f._generate_query()
        _assert_query(query, expected_query)

    def test_create_case_filter_override(self):
        expected_query = (
            " cases JOIN issues_analysis ON cases.issue_analysis_id = issues_analysis.id "
            "AND issues_analysis.classification = true AND issues_analysis.is_automated = false "
            "AND issues_analysis.is_security_enhancement = false \n"
            "WHERE cases.status = 'DONE' "
            "AND NOT (EXISTS (SELECT * \nFROM cases AS cases_1 \n"
            "WHERE cases_1.account_id = cases.account_id AND cases_1.source_id = cases.source_id AND cases_1.parent_issue_id = cases.issue_id)) "
            "AND cases.status = 'DONE'"
        )

        f = CaseFilters.default_workroom_filter([Filter(field="status", value="DONE")])
        assert f.filters_list == [
            Filter(field="status", value="open"),
            Filter(field="classification", value="true", op=Operator.EQ),
            Filter(field="is_automated", value="false", op=Operator.EQ),
            Filter(field="is_security_enhancement", value="false", op=Operator.EQ),
            Filter(field="container", value="false"),
            Filter(field="status", value="DONE"),
        ]
        assert f.allowed_filters.sort() == ["status", "classification", "source_id", "account_id"].sort()
        assert f.status == CaseStatus.DONE
        query = f._generate_query()
        _assert_query(query, expected_query)

    def test_generate_filters_query(self):
        expected = (
            " cases JOIN issues_analysis ON cases.issue_analysis_id = issues_analysis.id "
            "AND issues_analysis.classification = true AND "
            "issues_analysis.is_automated = false "
            "AND issues_analysis.is_security_enhancement = false \n"
            "WHERE cases.status = 'OPEN' "
            "AND NOT (EXISTS (SELECT * \nFROM cases AS cases_1 \nWHERE cases_1.account_id = cases.account_id AND cases_1.source_id = cases.source_id AND cases_1.parent_issue_id = cases.issue_id)) "
            "AND (provider_fields->>'momo')::text ILIKE ANY(ARRAY['%OPEN%']) "
            "AND (provider_fields->>'momo1')::date < '2024-07-02 11:12:50.092078' "
            "AND (provider_fields->>'momo2')::boolean = ANY(ARRAY[True]) "
            "AND (provider_fields->>'momo3')::float = ANY(ARRAY[4]) "
            "AND (provider_fields->>'momo4')::float = ANY(ARRAY[4.5])"
        )

        f = CaseFilters.default_workroom_filter(
            [
                Filter(
                    field="provider_fields.momo",
                    value="OPEN",
                    op=Operator.EQ,
                    inner_field_type=ProviderFieldType.STRING,
                ),
                Filter(
                    field="provider_fields.momo1",
                    value="2024-07-02 11:12:50.092078",
                    op=Operator.LT,
                    inner_field_type=ProviderFieldType.DATE,
                ),
                Filter(
                    field="provider_fields.momo2",
                    value="True",
                    op=Operator.EQ,
                    inner_field_type=ProviderFieldType.BOOLEAN,
                ),
                Filter(
                    field="provider_fields.momo3",
                    value="4",
                    op=Operator.EQ,
                    inner_field_type=ProviderFieldType.NUMBER,
                ),
                Filter(
                    field="provider_fields.momo4",
                    value="4.5",
                    op=Operator.EQ,
                    inner_field_type=ProviderFieldType.NUMBER,
                ),
                Filter(
                    field="field_not_exists.momo4",
                    value="4.5",
                    op=Operator.EQ,
                    inner_field_type=ProviderFieldType.NUMBER,
                ),
            ]
        )
        query = f._generate_query()
        _assert_query(query, expected)

    def test_labels_filter(self):
        expected_query = (
            " cases \n" "WHERE cases.labels && ARRAY['momo', 'momo2'] " "AND NOT (cases.labels && ARRAY['mom3'])"
        )
        f = CaseFilters(
            [
                Filter(field="labels", value=["momo", "momo2"], op=Operator.EQ),
                Filter(field="labels", value=["mom3"], op=Operator.NE),
                Filter(field="labels", value=["momo", "momo2"], op=Operator.GT),
            ]
        )
        query = f._generate_query()
        _assert_query(query, expected_query)
