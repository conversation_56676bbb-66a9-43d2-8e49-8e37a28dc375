import random
from datetime import UTC, datetime, timedelta

import pytest

from service.db import ServiceDAL
from service.db.dal_psv import get_psv_filter
from service.k8s_jobs.security_violation_job.models import PsvJobLogicResult
from service.logic.filters_and_sort import PSVFilters
from service.models.filters_and_sort import Filter, Operator
from service.models.psv import PsvStatus, SinglePsvUpdateRequest
from tests.mock_utils import SOURCE_ID, get_issue_id
from tests.psv_utils import create_psv, create_psvs


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestPSV:
    async def test_add_psv(self, service_dal_fixture: ServiceDAL, account_id: str):
        new_psv = await create_psv(service_dal_fixture, get_issue_id(1), description="test description")
        assert new_psv.id is not None
        new_id = new_psv.id
        psv_result = PsvJobLogicResult(
            account_id=account_id,
            source_id=SOURCE_ID,
            issue_id=get_issue_id(1),
            description="test description",
            violation_type="type",
            research_package_version="1.0",
            has_psv=True,
            issue_hash="hash",
        )
        newer_psv = await service_dal_fixture.psv_dal.add_psv(psv_result)
        assert newer_psv.id is not None
        assert newer_psv.id == new_id + 1
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(psvs) == 1
        assert psvs[0].id == newer_psv.id

    async def test_get_all_open_psv(self, service_dal_fixture: ServiceDAL, account_id: str):
        total_psvs = random.randint(1, 5)
        for i in range(total_psvs):
            await create_psv(service_dal_fixture, get_issue_id(i + 1), description=f"test description {i}")
        psv_filters = PSVFilters([Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN)])
        open_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filters)
        assert len(open_psvs) == total_psvs

    async def test_update_status(self, service_dal_fixture: ServiceDAL, account_id: str):
        total_psvs = random.randint(3, 8)
        ids: list[int] = []
        for i in range(total_psvs):
            psv = await create_psv(service_dal_fixture, get_issue_id(i + 1), description=f"test description {i}")
            ids.append(psv.id)

        await service_dal_fixture.psv_dal.update_psv_status(account_id, ids[0], PsvStatus.DONE)
        await service_dal_fixture.psv_dal.update_psv_status(account_id, ids[1], PsvStatus.DISMISSED, "test reason")

        psv_filters = PSVFilters([Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN)])
        open_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filters)
        assert len(open_psvs) == total_psvs - 2

        psv_filters = PSVFilters([Filter(field="status", op=Operator.EQ, value=PsvStatus.DONE)])
        done_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filters)
        assert len(done_psvs) == 1

        default_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(default_psvs) == total_psvs

        psv_filters = PSVFilters([Filter(field="status", op=Operator.EQ, value=PsvStatus.DISMISSED)])
        dissmissed_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filters)
        assert len(dissmissed_psvs) == 1
        assert dissmissed_psvs[0].dismissed_reason == "test reason"

    async def test_delete_psv(self, service_dal_fixture: ServiceDAL, account_id: str):
        new_psv = await create_psv(service_dal_fixture, get_issue_id(1), description="test description")
        assert new_psv.id is not None
        psv = await service_dal_fixture.psv_dal.get_psv_by_issue_id(account_id, SOURCE_ID, get_issue_id(1))
        assert psv is not None
        deleted = await service_dal_fixture.psv_dal.delete_psv(account_id, SOURCE_ID, get_issue_id(1))
        assert deleted
        psv = await service_dal_fixture.psv_dal.get_psv_by_issue_id(account_id, SOURCE_ID, get_issue_id(1))
        assert psv is None

    async def test_delete_all_psvs_for_source(self, service_dal_fixture: ServiceDAL, account_id: str):
        total_psvs = random.randint(3, 8)
        for i in range(total_psvs):
            await create_psv(service_dal_fixture, get_issue_id(i + 1), description=f"test description {i}")
        await create_psv(service_dal_fixture, get_issue_id(1), description="test description", source_id=SOURCE_ID + 1)
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(psvs) == total_psvs + 1
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, source_id=SOURCE_ID)
        assert len(psvs) == total_psvs
        await service_dal_fixture.psv_dal.delete_psv_for_source(account_id, SOURCE_ID)
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(psvs) == 1
        assert psvs[0].source_id == SOURCE_ID + 1
        await service_dal_fixture.psv_dal.delete_psv_for_source(account_id, SOURCE_ID)
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(psvs) == 1

    async def test_delete_all_psvs_without_case(self, service_dal_fixture: ServiceDAL, account_id: str):
        total_psvs = random.randint(3, 8)
        for i in range(total_psvs):
            psv_result = PsvJobLogicResult(
                account_id=account_id,
                source_id=SOURCE_ID,
                issue_id=get_issue_id(i + 1),
                description=f"test description {i}",
                violation_type="psv_type",
                research_package_version="1.0",
                has_psv=False,
                issue_hash="hash",
            )
            await service_dal_fixture.psv_dal.add_psv(psv_result)
        psv_filter = get_psv_filter()
        psv_filter.join_default_tables = True
        assert not await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filter)
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(psvs) == total_psvs
        assert await service_dal_fixture.psv_dal.delete_psv(account_id, SOURCE_ID, get_issue_id(1))
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(psvs) == total_psvs - 1
        await service_dal_fixture.psv_dal.delete_psv_for_source(account_id, SOURCE_ID)
        psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(psvs) == 0

    async def test_bulk_update_psv_status(self, service_dal_fixture: ServiceDAL, account_id: str):
        total_psvs = random.randint(3, 8)
        psv_list = []
        for i in range(total_psvs):
            psv = await create_psv(service_dal_fixture, get_issue_id(i), description=f"test description {i}")
            psv_list.append(psv)

        bulk_update_request = {
            psv_list[0].id: SinglePsvUpdateRequest(new_status=PsvStatus.DONE),
            psv_list[1].id: SinglePsvUpdateRequest(new_status=PsvStatus.DISMISSED, dismissed_reason="test reason"),
            psv_list[2].id: SinglePsvUpdateRequest(new_status=PsvStatus.OPEN),
        }

        updated_psvs = await service_dal_fixture.psv_dal.update_psv_status_bulk(
            account_id=account_id, psvs_to_update=bulk_update_request
        )

        assert len(updated_psvs) == 3

        psv_filters = PSVFilters([Filter(field="status", op=Operator.EQ, value=PsvStatus.DONE)])
        done_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filters)
        assert len(done_psvs) == 1

        psv_filters = PSVFilters([Filter(field="status", op=Operator.EQ, value=PsvStatus.DISMISSED)])
        dismissed_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filters)
        assert len(dismissed_psvs) == 1
        assert dismissed_psvs[0].dismissed_reason == "test reason"

        psv_filters = PSVFilters([Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN)])
        open_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id, psv_filters=psv_filters)
        assert len(open_psvs) == total_psvs - 2

        default_psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
        assert len(default_psvs) == total_psvs

    async def test_get_count_psv(self, service_dal_fixture: ServiceDAL, account_id: str):
        now = datetime.now(UTC)
        type1 = "psv_type1"
        type2 = "psv_type2"
        psvs1 = await create_psvs(service_dal_fixture, psv_type=type1, count=5)
        psvs2 = [
            await create_psv(service_dal_fixture, get_issue_id(len(psvs1) + i + 1), psv_type=type2) for i in range(4)
        ]
        open_psvs = await service_dal_fixture.psv_dal.get_psv_count_by_types(account_id)
        assert open_psvs[type1] == len(psvs1)
        assert open_psvs[type2] == len(psvs2)
        for psv in psvs1[:2]:
            psv.created_at = psv.created_at.replace(year=psv.created_at.year - 1)
        psvs1[4].created_at = psvs1[4].created_at.replace(year=psvs1[4].created_at.year + 1)
        created_before = psvs1[4].created_at - timedelta(days=1)
        await service_dal_fixture.session.commit()
        psv_filters = PSVFilters(
            [Filter(field="created_at", op=Operator.BETWEEN, value=[str(now), str(created_before)])]
        )
        open_psvs_with_date = await service_dal_fixture.psv_dal.get_psv_count_by_types(
            account_id, psv_filters=psv_filters
        )
        assert open_psvs_with_date[type1] == 2
        await service_dal_fixture.psv_dal.update_psv_status(account_id, psvs1[2].id, PsvStatus.DONE)
        await service_dal_fixture.session.commit()
        psv_filters.add_filter(Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN))
        open_psvs_with_date = await service_dal_fixture.psv_dal.get_psv_count_by_types(
            account_id, psv_filters=psv_filters
        )
        assert open_psvs_with_date[type1] == 1
