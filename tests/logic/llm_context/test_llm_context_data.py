from service.db import ServiceDA<PERSON>
from service.logic.llm_context import DataExporter
from service.models.levels.risk_score import RiskScoreCategory
from tests.case_test_utils import create_case

PROVIDER_FIELDS = {"summary": "Test summary", "parent_id": "PARENT-123", "custom_field": "custom_value"}


class TestLLMContextData:
    async def test_prepare_record(self, service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(service_dal_fixture, issue_id="TEST-123", provider_fields=PROVIDER_FIELDS)
        exporter = DataExporter(
            account_id=account_id,
            service_dal=service_dal_fixture,
            by_container_id=0,
        )

        result = exporter._prepare_record(case)

        assert result.issue_id == case.issue_id
        assert result.parent_issue_id == case.parent_issue_id
        assert result.status == case.status
        assert result.created_at == case.created_at
        assert result.status == case.status

    async def test_extract_summary(self, service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(
            service_dal_fixture, issue_id="TEST-123", provider_fields=PROVIDER_FIELDS, with_summary=True
        )
        service_dal_fixture.session.expunge_all()
        exporter = DataExporter(
            account_id=account_id,
            service_dal=service_dal_fixture,
            by_container_id=0,
        )
        result = exporter._get_summary(case)
        assert result.summary == case.partial.summary

    async def test_extract_summary_empty(self, service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(service_dal_fixture, issue_id="TEST-123", provider_fields=PROVIDER_FIELDS)
        case.final = None
        case.partial = None
        exporter = DataExporter(
            account_id=account_id,
            service_dal=service_dal_fixture,
            by_container_id=0,
        )

        result = exporter._get_summary(case)
        assert result.summary == "No summary found"

    async def test_extract_issue_analysis_with_data(self, service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(service_dal_fixture, issue_id="TEST-123", provider_fields=PROVIDER_FIELDS)
        exporter = DataExporter(
            account_id=account_id,
            service_dal=service_dal_fixture,
            by_container_id=0,
        )

        result = exporter._extract_issue_analysis(case)

        assert result.availability == case.issue_analysis.availability
        assert result.confidentiality == case.issue_analysis.confidentiality
        assert result.integrity == case.issue_analysis.integrity
        assert result.risk_score == case.issue_analysis.risk_score
        assert result.risk_score_category == RiskScoreCategory.ANALYZE
        assert result.classification == case.issue_analysis.classification
        assert result.concerns == [c.long_description for c in case.issue_analysis.concerns]
        assert result.is_automated == case.issue_analysis.is_automated
        assert result.is_security_enhancement == case.issue_analysis.is_security_enhancement

    async def test_extract_issue_analysis_without_data(self, service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(service_dal_fixture, issue_id="TEST-123", provider_fields=PROVIDER_FIELDS)
        case.issue_analysis = None
        exporter = DataExporter(
            account_id=account_id,
            service_dal=service_dal_fixture,
            by_container_id=0,
        )

        result = exporter._extract_issue_analysis(case)

        assert result.availability is None
        assert result.confidentiality is None
        assert result.integrity is None
        assert result.risk_score is None
        assert result.classification is False
        assert result.concerns is None
