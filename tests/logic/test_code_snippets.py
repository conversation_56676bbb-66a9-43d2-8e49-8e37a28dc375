from pathlib import Path

import pytest

from service.k8s_jobs.classification_job.task_helpers import _handle_code_snippet
from tests.mock_utils import resources_dir


@pytest.mark.parametrize("file_name", ["1_input.txt", "2_input.txt", "3_input.txt", "4_input.txt", "5_input.txt"])
def test_handle_code_snippets(file_name):
    snippets_dir = Path(resources_dir / "code_snippets")
    file_path = snippets_dir / file_name
    if file_path.name.endswith("input.txt"):
        input_snippet = file_path.read_text()
        expected_snippet = Path(snippets_dir / file_name.replace("input", "result")).read_text()
        assert _handle_code_snippet(input_snippet) == expected_snippet, f"failed to check {file_name}"


def test_handle_code_snippet_exception():
    # Test that the function handles exceptions gracefully
    # by returning the input unchanged
    input_snippet = None
    assert _handle_code_snippet(input_snippet) == input_snippet
