from datetime import UTC, datetime
from typing import Any

import pytest
from dateutil.tz import tzoffset
from prime_redis_utils import AsyncPrefixRedisClient

from service.logic.issues.issues import BasePrimeIssue, PrimeIssueAttributes
from service.logic.issues.utils import get_jira_fields_selected, get_jira_fields_stored
from service.logic.jira_manager import JiraPrimeIssue
from service.logic.jira_manager.jira_fields import JiraFieldInfo, JiraFieldSchema
from tests.mock_utils import REAL_JIRA_FIELDS_SCHEMA, SOURCE_ID, _basic_mocks, get_prime_issue

now = datetime.now()
today = datetime.today().replace(hour=0, minute=0, second=0, microsecond=0)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGenericPrimeIssue:
    def test_prime_issue_attributes_creation(self):
        attrs = PrimeIssueAttributes(
            id="TEST-123",
            summary="Test issue",
            created=datetime(2024, 1, 1),
            issuetype="Task",
            creator="<PERSON> Do<PERSON>",
        )

        assert attrs.id_ == "TEST-123"
        assert attrs.key == "TEST-123"
        assert attrs.summary == "Test issue"
        assert attrs.issuetype == "Task"
        assert attrs.creator == "John Doe"

    def test_created_serialization(self):
        attrs = PrimeIssueAttributes(
            id="TEST-123", summary="Test issue", created=datetime(2024, 1, 1, 12, 0), issuetype="Task"
        )

        assert attrs.serialize_created(attrs.created, None) == "2024-01-01T12:00:00"


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestBasePrimeIssueHelpers:
    def test_get_nested_value(self):
        data = {"level1": {"level2": {"level3": "value"}}}

        assert BasePrimeIssue.get_nested_value(data, "level1.level2.level3") == "value"
        assert BasePrimeIssue.get_nested_value(data, "nonexistent") is None
        assert BasePrimeIssue.get_nested_value(data, "level1.nonexistent") is None

    def test_get_nested_value_with_invalid_data(self):
        assert BasePrimeIssue.get_nested_value({}, "any.path") is None
        assert BasePrimeIssue.get_nested_value({"key": None}, "key.subkey") is None


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestJiraIssue:
    @pytest.mark.parametrize(
        "field_type, value, expected",
        [
            # ("string", "string_value", "string_value"),
            # ("boolean", True, True),
            # ("number", 10, 10),
            # ("datetime", now, now),
            # ("date", date.today(), today),
            # ("array", ["1", "2", "3"], ["1", "2", "3"]),
            ("array", [1, 2, 3], ["1", "2", "3"]),
            # ("array", [{"name": "1"}, {"name": "2"}, {"name": "3"}], ["1", "2", "3"]),
            # ("number", None, None),
            # ("date", "", None),
            # ("date", "32443", None),
        ],
    )
    async def test_fields_parsing(self, field_type: str, value: Any, expected: Any):
        schema = JiraFieldSchema(type=field_type)
        field_name = f"{field_type}_field"
        field = JiraFieldInfo(
            id=field_name,
            name=field_name,
            schema=schema,
            orderable=True,
            navigable=True,
            searchable=True,
            key=field_name,
        )
        issue_data = {
            "fields": {
                field_name: value,
                "summary": "summary",
                "description": "description",
                "issuetype": {"name": "type"},
                "created": datetime.now(UTC),
                "status": {"name": "status"},
                "creator": {"displayName": "creator"},
                "project": {"key": "TEST"},
            }
        }
        issue_data.update(
            {
                "key": "id",
                "self": "self",
            }
        )
        issue = JiraPrimeIssue.from_dict(issue_data)
        result_value = issue.get_field(field)
        assert result_value.value == expected

    async def test_get_fields(self, account_redis_client: AsyncPrefixRedisClient, account_id: str):
        selected_jira_fields_ids = ["field_1", "field_2", "labels"]
        with _basic_mocks(selected_jira_fields=selected_jira_fields_ids):
            selected_jira_fields = await get_jira_fields_selected(account_id, SOURCE_ID, account_redis_client)
            stored_jira_fields = await get_jira_fields_stored(account_id, SOURCE_ID, account_redis_client)
        assert {f.id for f in selected_jira_fields} == set(selected_jira_fields_ids)
        hardcoded_fields = ["status", "summary", "id", "self", "creator", "issuetype", "created", "sprint", "project"]
        assert {f.id for f in stored_jira_fields} == {*selected_jira_fields_ids, *hardcoded_fields}

    async def test_get_fields_data_sprint_with_custom_sprint_selected(
        self, account_redis_client: AsyncPrefixRedisClient, account_id: str
    ):
        expected_fields = {
            "customfield_99999": False,
            "customfield_99998": "moshe reubinoff",
            "customfield_10031": ["Sprint 34"],
            "id": "10001",
            "summary": "Issue 1 - lets go",
            "created": datetime(2024, 7, 22, 11, 24, 33, 91271, tzinfo=tzoffset(None, 7200)),
            "issuetype": "Epic",
            "project": "PORJ",
            "creator": "John Doe",
            "self": "https://prime-test.atlassian.net/rest/api/2/issue/10001",
            "sprint": [
                {
                    "boardId": 2,
                    "endDate": "2024-05-30T04:19:25.000Z",
                    "name": "Sprint 34",
                    "startDate": "2024-05-19T14:30:27.042Z",
                    "state": "closed",
                },
            ],
            "status": "Open",
            "assignee": "Roy Mezan",
            "reporter": "Matan Markovics",
        }

        with _basic_mocks(selected_jira_fields=["customfield_10031"], jira_fields_override=REAL_JIRA_FIELDS_SCHEMA):
            jira_fields = await get_jira_fields_stored(account_id, SOURCE_ID, account_redis_client)
        issue = get_prime_issue(1)
        actual_data = issue.get_fields_data(jira_fields)
        assert actual_data == expected_fields

    async def test_get_fields_data_sprint(self, account_redis_client: AsyncPrefixRedisClient, account_id: str):
        expected_fields = {
            "customfield_99999": False,
            "customfield_99998": "moshe reubinoff",
            "id": "10001",
            "summary": "Issue 1 - lets go",
            "created": datetime(2024, 7, 22, 11, 24, 33, 91271, tzinfo=tzoffset(None, 7200)),
            "issuetype": "Epic",
            "project": "PORJ",
            "creator": "John Doe",
            "self": "https://prime-test.atlassian.net/rest/api/2/issue/10001",
            "sprint": [
                {
                    "boardId": 2,
                    "endDate": "2024-05-30T04:19:25.000Z",
                    "name": "Sprint 34",
                    "startDate": "2024-05-19T14:30:27.042Z",
                    "state": "closed",
                },
            ],
            "status": "Open",
            "assignee": "Roy Mezan",
            "reporter": "Matan Markovics",
        }

        with _basic_mocks(jira_fields_override=REAL_JIRA_FIELDS_SCHEMA):
            jira_fields = await get_jira_fields_stored(account_id, SOURCE_ID, account_redis_client)
        issue = get_prime_issue(1)
        actual_data = issue.get_fields_data(jira_fields)
        assert actual_data == expected_fields

    async def test_get_fields_data(self, account_redis_client: AsyncPrefixRedisClient, account_id: str):
        expected_fields = [
            {
                "assignee": "Roy Mezan",
                "created": datetime(2024, 7, 22, 11, 24, 33, 91271, tzinfo=tzoffset(None, 7200)),
                "creator": "John Doe",
                "customfield_10031": [
                    "Sprint 34",
                ],
                "customfield_99998": "moshe reubinoff",
                "customfield_99999": False,
                "id": "10001",
                "issuetype": "Epic",
                "project": "PORJ",
                "reporter": "Matan Markovics",
                "self": "https://prime-test.atlassian.net/rest/api/2/issue/10001",
                "sprint": [
                    {
                        "boardId": 2,
                        "endDate": "2024-05-30T04:19:25.000Z",
                        "name": "Sprint 34",
                        "startDate": "2024-05-19T14:30:27.042Z",
                        "state": "closed",
                    },
                ],
                "status": "Open",
                "summary": "Issue 1 - lets go",
            },
            {
                "assignee": "Roy Mezan",
                "created": datetime(2024, 7, 27, 11, 24, 33, 91271, tzinfo=tzoffset(None, 7200)),
                "creator": "Jane Doe the 2nd",
                "customfield_99998": "moshe reubinoff",
                "customfield_99999": False,
                "id": "10002",
                "issuetype": "Task",
                "project": "PORJ",
                "reporter": "Matan Markovics",
                "self": "https://prime-test.atlassian.net/rest/api/2/issue/10002",
                "status": "Closed",
                "summary": "Issue 2 is here",
            },
            {
                "assignee": "Matan Markovics",
                "created": datetime(2024, 7, 29, 11, 24, 33, 91271, tzinfo=tzoffset(None, 7200)),
                "creator": "John Smith the 3rd",
                "customfield_99998": "moshe reubinoff",
                "customfield_99999": True,
                "id": "10003",
                "issuetype": "Subtask",
                "project": "PORJ",
                "reporter": "Orna Khait-Marelly",
                "self": "https://prime-test.atlassian.net/rest/api/2/issue/10003",
                "status": "In Progress",
                "summary": "Issue 3 is here",
            },
        ]
        with _basic_mocks(selected_jira_fields=["customfield_10031"], jira_fields_override=REAL_JIRA_FIELDS_SCHEMA):
            jira_fields = await get_jira_fields_stored(account_id, SOURCE_ID, account_redis_client)
        for i in range(3):
            issue = get_prime_issue(i + 1)
            actual_data = issue.get_fields_data(jira_fields)
            assert actual_data == expected_fields[i]
