import pytest
from prime_config_service_client import SecurityFramework

from service.logic.recommendation_builder import get_controls_dict
from service.models import SecurityControl


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
def test_get_security_controls_dict():
    result = get_controls_dict(SecurityFramework.NIST)
    assert "AC-01" in result
    assert "SC-36" in result
    assert "SR-05(02)" in result
    assert result["SR-05(02)"] == SecurityControl(
        id="SR-05(02)",
        name="ASSESSMENTS PRIOR TO SELECTION, ACCEPTANCE, MODIFICATION, OR UPDATE",
        description="",
        control_names=["SR-05(02)"],
        framework=SecurityFramework.NIST,
    )
