import pytest

from service.models import ProviderFieldInfo, ProviderFieldType
from service.models.filters_and_sort import SortField


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
def test_sort_validator_inner_field_type_not_found_defaults_to_string():
    # field is not in the context
    test_field = ProviderFieldInfo(type=ProviderFieldType.STRING, name="field 3", id="field_3")
    field = SortField.model_validate(
        {"field": "provider_fields.field_2", "direction": "desc"},
        context={"field_3": test_field},
    )
    assert field.inner_field_type == ProviderFieldType.STRING

    # field is not in the context and type is None
    field = SortField.model_validate(
        {"field": "provider_fields.field_2", "direction": "desc", "inner_field_type": None},
        context={"field_3": test_field},
    )
    assert field.inner_field_type == ProviderFieldType.STRING

    # field is not in the context and type is not in CustomProviderFieldType
    field = SortField.model_validate(
        {"field": "provider_fields.field_2", "direction": "desc", "inner_field_type": "momo"},
        context={"field_3": test_field},
    )
    assert field.inner_field_type == ProviderFieldType.STRING
