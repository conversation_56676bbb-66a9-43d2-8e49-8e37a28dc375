from unittest.mock import Mock

import pytest

MOCK_ISSUE_KEY = "ISSUE-1"


@pytest.fixture
def mock_issue() -> Mock:
    issue = Mock()
    issue.attributes.key = MOCK_ISSUE_KEY
    return issue


@pytest.fixture
def mock_issue_node() -> str:
    return MOCK_ISSUE_KEY


class TestIssueManager:
    # TODO: create a test
    async def test_get_issue(self):
        pass

    # TODO: create a test
    async def test_get_full_issue(
        self,
    ):
        pass
