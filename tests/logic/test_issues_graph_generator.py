from datetime import UTC, datetime
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest
from prime_redis_utils import AsyncPrefixRedisClient

from service.db import ServiceDAL
from service.db.tables.cases import CaseTable
from service.logic.issues_graph import GraphGenerator, GraphItem
from service.logic.jira_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from tests.case_test_utils import _basic_mocks, create_case, create_cases
from tests.db.test_cases_dal import ISSUE_ID
from tests.mock_utils import JIRA_STRUCTURE, SOURCE_ID, mock_get_issues_files


@pytest.fixture
async def case(service_dal_fixture: ServiceDAL, account_id: str) -> CaseTable:
    return await create_case(service_dal_fixture, account_id, SOURCE_ID, ISSUE_ID, with_summary=False)


EXPECTED_TREE_ITEMS = {GraphItem(x[0], x[1]) for x in JIRA_STRUCTURE}


class TestGraphGenerator:
    async def test_graph_generator_load_and_dump(
        self,
        service_dal_fixture: ServiceDAL,
        issue_manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ana<PERSON>,
        account_redis_client: AsyncPrefixRedisClient,
        account_id: str,
    ):
        await account_redis_client.flushall()
        await create_cases(service_dal_fixture)
        generator = GraphGenerator(service_dal_fixture, account_id, SOURCE_ID)
        graph_from_db = await generator.load_from_db()
        assert set(graph_from_db.to_items()) == EXPECTED_TREE_ITEMS
        await generator.write_to_to_redis(graph_from_db, account_redis_client)
        graph_from_redis = await generator.load_from_redis(account_redis_client)
        assert set(graph_from_redis.to_items()) == set(graph_from_db.to_items())
        assert set(graph_from_redis.to_items()) == EXPECTED_TREE_ITEMS

    @patch("service.logic.issues_graph.generator.get_jira_client", new_callable=AsyncMock)
    async def test_graph_generator_build(
        self, jira_fetcher_mock: AsyncMock, service_dal_fixture: ServiceDAL, account_id: str
    ):
        mock_issues = []
        for x in JIRA_STRUCTURE:
            issue = Mock(key=x[0])
            issue.fields.parent.key = x[1]
            issue.fields.issuetype.name = "Task"
            mock_issues.append(issue)

        time = datetime.now(UTC)
        jira_fetcher_mock.return_value = Mock()
        jira_fetcher_mock.return_value.get_issues_generator.return_value = mock_issues

        with _basic_mocks() as (file_manager_mocker, _, _):
            mock_get_issues_files(file_manager_mocker, account_id, time)
            generator = GraphGenerator(service_dal_fixture, account_id, SOURCE_ID)
            result = await generator.update_since(None)
        assert set(result.to_items()) == EXPECTED_TREE_ITEMS
