import pytest
from prime_tests import <PERSON><PERSON><PERSON><PERSON>po<PERSON>, service_mocker

from service.errors import IssueIdNotFoundInStorageError
from service.logic.jira_manager import Jira<PERSON><PERSON>uesManager, JiraPrimeIssue
from tests.mock_utils import (
    SOURCE_ID,
    TOTAL_TESTS_ISSUES,
    generate_test_tree,
    get_issue_data,
    get_zip_data,
)


class TestJiraIssuesManager:
    async def test_get_issue(self, issue_manager: JiraIssuesManager):
        await self._validate_get_issue(issue_manager)

    async def test_get_issue_not_found(self, issue_manager: JiraIssuesManager, account_id: str):
        issue_id = "not_existing_issue"
        graph = generate_test_tree()
        with service_mocker("file-manager-service") as file_manager_mocker:
            download_file_url = f"files/{account_id}/data/source/{SOURCE_ID}/origin_id/{issue_id}.json"
            download_files_url = f"files/{account_id}/data/source/{SOURCE_ID}"
            file_manager_mocker.get(download_file_url, MockResponse("", status_code=404, reason="Not Found"))
            file_manager_mocker.put(download_files_url, MockResponse(content=get_zip_data({})))
            with pytest.raises(IssueIdNotFoundInStorageError):
                await issue_manager.get_issue(issue_id, issues_graph=graph)

    @classmethod
    async def _validate_get_issue(cls, issue_manager: JiraIssuesManager):
        for i in range(TOTAL_TESTS_ISSUES):
            expected_issue = JiraPrimeIssue.from_dict(get_issue_data(i + 1))
            if i == 3:
                expected_issue.parent = JiraPrimeIssue.from_dict(get_issue_data(2))
            graph = generate_test_tree()
            actual_issue_data = await issue_manager.get_issue(expected_issue.attributes.key, issues_graph=graph)
            assert actual_issue_data, expected_issue


class TestIssue:
    def test_from_dict(self):
        data = get_issue_data(2)
        issue = JiraPrimeIssue.from_dict(data)
        assert issue.attributes.id_ == issue.attributes.key == data["key"]
        assert issue.attributes.summary == data["fields"]["summary"]
        assert issue.description == data["fields"]["description"]
        assert issue.attributes.issuetype == data["fields"]["issuetype"]["name"]

    def test_from_dict_sprint(self):
        data = get_issue_data(1)
        issue = JiraPrimeIssue.from_dict(data)
        assert issue.attributes.id_ == issue.attributes.key == data["key"]
        assert issue.attributes.summary == data["fields"]["summary"]
        assert issue.description == data["fields"]["description"]
        assert issue.attributes.issuetype == data["fields"]["issuetype"]["name"]
        assert issue.attributes.sprint == [
            {
                "boardId": 2,
                "completeDate": "2024-06-18T11:40:22.959Z",
                "endDate": "2024-05-30T04:19:25.000Z",
                "goal": "",
                "id": 120,
                "name": "Sprint 34",
                "startDate": "2024-05-19T14:30:27.042Z",
                "state": "closed",
            }
        ]
