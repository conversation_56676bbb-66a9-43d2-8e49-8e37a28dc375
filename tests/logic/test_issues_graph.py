import pytest

from service.logic.issues_graph import GraphItem, IssuesGraph

tree1 = [
    GraphItem(key="A1", parent=None),
    GraphItem(key="B1", parent="A1"),
    GraphItem(key="B2", parent="A1"),
    GraphItem(key="C1", parent="B1"),
    GraphItem(key="C2", parent="B1"),
    GraphItem(key="C3", parent="B2"),
    GraphItem(key="C4", parent="B2"),
    GraphItem(key="C5", parent="B2"),
    GraphItem(key="D1", parent="C1"),
    GraphItem(key="D2", parent="C1"),
    GraphItem(key="D3", parent="C3"),
    GraphItem(key="D4", parent="C3"),
]

tree2 = [
    GraphItem(key="X1", parent=None),
    GraphItem(key="Y1", parent="X1"),
    GraphItem(key="Y2", parent="X1"),
    GraphItem(key="Y3", parent="X1"),
    GraphItem(key="Z1", parent="Y1"),
    GraphItem(key="Z2", parent="Y2"),
    GraphItem(key="Z3", parent="Y2"),
    GraphItem(key="Z4", parent="Y3"),
    GraphItem(key="W1", parent="Z1"),
    GraphItem(key="W2", parent="Z3"),
    GraphItem(key="V1", parent="W2"),
    GraphItem(key="U1", parent="V1"),
]
all_items = tree1 + tree2


class TestIssuesGraph:
    def test_empty_graph(self):
        graph = IssuesGraph.build([])
        assert graph.size() == 0
        assert graph.is_empty()
        assert list(graph.iterator_from_top()) == []
        assert list(graph.iterator_from_bottom()) == []

    def test_get_node_nonexistent(self):
        tree = IssuesGraph.build([])
        assert not tree.exists("nonexistent")
        with pytest.raises(ValueError):
            tree.get_parent("nonexistent")
        with pytest.raises(ValueError):
            tree.get_children("nonexistent")

    def test_graph_size(self):
        graph = IssuesGraph.build(all_items)
        assert graph.size() == len(all_items)

    def test_parent(self):
        graph = IssuesGraph.build(all_items)
        assert graph.get_parent("B1") == "A1"
        assert graph.get_parent("C1") == "B1"
        assert graph.get_parent("D1") == "C1"
        assert graph.get_parent("Y1") == "X1"
        assert graph.get_parent("Z1") == "Y1"
        assert graph.get_parent("W1") == "Z1"

    def test_get_children(self):
        graph = IssuesGraph.build(all_items)
        assert set(graph.get_children("A1")) == {"B1", "B2"}
        assert set(graph.get_children("B1")) == {"C1", "C2"}
        assert set(graph.get_children("B2")) == {"C3", "C4", "C5"}
        assert set(graph.get_children("X1")) == {"Y1", "Y2", "Y3"}
        assert set(graph.get_children("Y2")) == {"Z2", "Z3"}
        assert set(graph.get_children("Z3")) == {"W2"}

    def test_get_all_descendants(self):
        graph = IssuesGraph.build(all_items)
        expected = {
            "B1",
            "B2",
            "C1",
            "C2",
            "C3",
            "C4",
            "C5",
            "D1",
            "D2",
            "D3",
            "D4",
        }
        assert set(graph.get_all_descendants("A1")) == expected
        assert set(graph.get_all_descendants("B1")) == {"C1", "C2", "D1", "D2"}
        expected = {
            "Y1",
            "Y2",
            "Y3",
            "Z1",
            "Z2",
            "Z3",
            "Z4",
            "W1",
            "W2",
            "V1",
            "U1",
        }
        assert set(graph.get_all_descendants("X1")) == expected

    def test_get_ancestors(self):
        graph = IssuesGraph.build(all_items)
        assert graph.get_ancestors("D1") == ["C1", "B1", "A1"]
        assert graph.get_ancestors("C3") == ["B2", "A1"]
        assert graph.get_ancestors("U1") == ["V1", "W2", "Z3", "Y2", "X1"]

    def test_iterator_from_top(self):
        graph = IssuesGraph.build(all_items)
        expected = [
            ["A1", "X1"],
            ["B2", "B1", "Y2", "Y3", "Y1"],
            ["C5", "C4", "C3", "C1", "C2", "Z3", "Z2", "Z4", "Z1"],
            ["D4", "D3", "D1", "D2", "W2", "W1"],
            ["V1"],
            ["U1"],
        ]
        actual = list(graph.iterator_from_top())
        for sub_array_actual, sub_array_expected in zip(actual, expected, strict=False):
            assert set(sub_array_actual) == set(sub_array_expected)

    def test_iterator_from_bottom(self):
        graph = IssuesGraph.build(all_items)
        expected = [
            ["U1", "D3", "D4", "D2", "D1", "W1", "C4", "C5", "C2", "Z4", "Z2"],
            ["V1", "C3", "C1", "Z1", "Y3"],
            ["W2", "B2", "B1", "Y1"],
            ["Z3", "A1"],
            ["Y2"],
            ["X1"],
        ]
        actual = list(graph.iterator_from_bottom())
        for sub_array_actual, sub_array_expected in zip(actual, expected, strict=False):
            assert set(sub_array_actual) == set(sub_array_expected)

    def test_issues_graph_subgraph(self):
        graph = IssuesGraph.build(all_items)
        subgraph = graph.create_subgraph("Z3")
        assert set(subgraph.iterator()) == {"W2", "V1", "Z3", "U1"}
