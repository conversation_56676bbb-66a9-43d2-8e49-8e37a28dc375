from pathlib import Path
from unittest import mock

import pytest

from service.errors.errors import JiraFieldNotFoundError
from service.logic.jira_manager import <PERSON><PERSON><PERSON>ieldsManager
from tests.mock_utils import SOURCE_ID, _basic_mocks, mock_jira_client

resources_dir = Path(Path(__file__).parent).parent / "_resources"


@pytest.fixture
async def fields_manager(fake_redis_fixture, account_id: str):
    with mock_jira_client():
        yield await JiraFieldsManager.build(account_id, SOURCE_ID, fake_redis_fixture)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestJiraCustomField:
    async def test_get_provider_field_id(self, fields_manager: JiraFieldsManager) -> None:
        field_to_test = "field_1"
        return_val = fields_manager.get_field_by_id(field_to_test)
        assert return_val.id == field_to_test
        assert return_val.name == "field 1"

    async def test_get_field_id_no_cache(self, account_id: str) -> None:
        with mock_jira_client():
            field_to_test = "field_1"
            manager = await JiraFieldsManager.build(account_id, SOURCE_ID)
            return_val = manager.get_field_by_id(field_to_test)
            assert return_val.id == field_to_test
            assert return_val.name == "field 1"
            return_val = manager.get_field_by_id(field_to_test)
            assert return_val.id == field_to_test

    async def test_get_field_id_not_fount(self, fields_manager: JiraFieldsManager) -> None:
        field_to_test = "oops"
        with pytest.raises(JiraFieldNotFoundError):
            _ = await fields_manager.get_field_by_id(field_to_test)

    async def test_custom_field_mapping(self, account_id: str) -> None:
        with _basic_mocks():
            manager = await JiraFieldsManager.build(account_id, SOURCE_ID)
            assert manager.prime_attr_to_jira_field == {"sprint": "customfield_10020"}

    async def test_download_get_jira_client_exception(self, account_id: str) -> None:
        with (
            mock.patch(
                "service.logic.jira_manager.jira_fields_manager.get_jira_client",
                side_effect=Exception("Jira client error"),
            ),
            _basic_mocks(),
        ):
            field_to_test = "field_1"
            manager = await JiraFieldsManager.build(account_id, SOURCE_ID)
            return_val = manager.get_field_by_id(field_to_test)
            assert return_val.id == field_to_test
            assert return_val.name == "field 1"
            return_val = manager.get_field_by_id(field_to_test)
            assert return_val.id == field_to_test
