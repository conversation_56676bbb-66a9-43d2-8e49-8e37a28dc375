from datetime import datetime

from service.db import ServiceDAL
from service.logic.filters_and_sort import CaseFilters
from service.logic.statistics import SecurityLinddunMethodologyStats
from service.models import RiskScoreCategory
from service.models.filters_and_sort import Filter, Operator

from ..case_test_utils import modify_concern_category
from .helpers import created_expected, prepare_test_cases


class TestLinddunDashboard:
    async def test_linuddun(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        await modify_concern_category(service_dal_fixture.session, [case.issue_analysis_id for case in cases[0:6]])
        handler = SecurityLinddunMethodologyStats(service_dal_fixture, account_id, datetime.min, datetime.max)
        stats = await handler.get_stats()
        expected_categories = {
            "Detectability": (0, 1, 1),
            "Disclosure of information": (26, 26, 27),
            "Identifiability": (27, 25, 28),
            "Linkability": (27, 25, 27),
            "Non-compliance": (27, 25, 27),
            "Non-repudiation": (0, 1, 2),
            "Unawareness": (1, 1, 0),
        }
        assert stats.categories == created_expected(expected_categories)

    async def test_linddun_stats_project_filter(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        await modify_concern_category(service_dal_fixture.session, [case.issue_analysis_id for case in cases[0:6]])

        handler = SecurityLinddunMethodologyStats(service_dal_fixture, account_id, datetime.min, datetime.max)
        _filter = [Filter(field="provider_fields.project", value=["test_project_id1"], op=Operator.EQ)]
        stats = await handler.get_stats(case_filters=CaseFilters(filters=_filter))
        expected_categories = {
            "Disclosure of information": (3, 3, 2),
            "Identifiability": (3, 3, 2),
            "Linkability": (3, 3, 2),
            "Non-compliance": (3, 3, 2),
        }
        assert stats.categories == created_expected(expected_categories)

    async def test_linddun_stats_risk_score_filter(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        await modify_concern_category(service_dal_fixture.session, [case.issue_analysis_id for case in cases[0:6]])
        handler = SecurityLinddunMethodologyStats(service_dal_fixture, account_id, datetime.min, datetime.max)
        _filter = [Filter(field="risk_score_category", value=RiskScoreCategory.ANALYZE, op=Operator.EQ)]
        stats = await handler.get_stats(case_filters=CaseFilters(filters=_filter))
        expected_categories = {
            "Detectability": (0, 1, 0),
            "Disclosure of information": (0, 28, 0),
            "Identifiability": (0, 26, 0),
            "Linkability": (0, 27, 0),
            "Non-compliance": (0, 27, 0),
            "Non-repudiation": (0, 1, 0),
            "Unawareness": (0, 2, 0),
        }
        assert stats.categories == created_expected(expected_categories)
