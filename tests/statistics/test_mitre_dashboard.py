from datetime import datetime

from service.db import ServiceDAL
from service.logic.filters_and_sort import CaseFilters
from service.logic.statistics import SecurityMitreMethodologyStats
from service.models import RiskScoreCategory
from service.models.filters_and_sort import Filter, Operator

from ..case_test_utils import modify_concern_category
from .helpers import created_expected, prepare_test_cases


class TestMitreDashboard:
    async def test_mitre_sql_query(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        await modify_concern_category(service_dal_fixture.session, [case.issue_analysis_id for case in cases[0:6]])
        handler = SecurityMitreMethodologyStats(service_dal_fixture, account_id, datetime.min, datetime.max)
        stats = await handler.get_stats()
        expected_categories = {
            "Initial Access": (0, 0, 2),
            "Execution": (0, 1, 0),
            "Persistence": (0, 0, 1),
            "Privilege Escalation": (0, 0, 1),
            "Defense Evasion": (26, 26, 26),
            "Credential Access": (0, 1, 0),
            "Discovery": (0, 1, 0),
            "Lateral Movement": (0, 0, 2),
            "Collection": (27, 25, 26),
            "Exfiltration": (26, 25, 27),
            "Command and Control": (1, 0, 0),
            "Impact": (0, 0, 1),
            "Resource Development": (1, 0, 0),
            "Reconnaissance": (27, 25, 26),
        }

        assert stats.categories == created_expected(expected_categories)

    async def test_mitre_stats_project_filter(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        await modify_concern_category(service_dal_fixture.session, [case.issue_analysis_id for case in cases[0:6]])

        handler = SecurityMitreMethodologyStats(service_dal_fixture, account_id, datetime.min, datetime.max)
        _filter = [Filter(field="provider_fields.project", value=["test_project_id1"], op=Operator.EQ)]
        stats = await handler.get_stats(case_filters=CaseFilters(filters=_filter))
        expected_categories = {
            "Defense Evasion": (3, 3, 2),
            "Collection": (3, 3, 2),
            "Exfiltration": (3, 3, 2),
            "Reconnaissance": (3, 3, 2),
        }
        assert stats.categories == created_expected(expected_categories)

    async def test_mitre_stats_risk_score_filter(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        await modify_concern_category(service_dal_fixture.session, [case.issue_analysis_id for case in cases[0:6]])
        handler = SecurityMitreMethodologyStats(service_dal_fixture, account_id, datetime.min, datetime.max)
        _filter = [Filter(field="risk_score_category", value=RiskScoreCategory.ANALYZE, op=Operator.EQ)]
        stats = await handler.get_stats(case_filters=CaseFilters(filters=_filter))
        expected_categories = {
            "Execution": (0, 2, 0),
            "Defense Evasion": (0, 27, 0),
            "Credential Access": (0, 1, 0),
            "Discovery": (0, 2, 0),
            "Collection": (0, 26, 0),
            "Exfiltration": (0, 27, 0),
            "Impact": (0, 1, 0),
            "Reconnaissance": (0, 26, 0),
        }
        assert stats.categories == created_expected(expected_categories)
