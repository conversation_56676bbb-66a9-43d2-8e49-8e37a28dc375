from datetime import UTC, datetime, timedelta
from functools import lru_cache

import freezegun

from service.db import ServiceDAL
from service.models import RISK_SCORE_RANGES, CasesPerCategoryCount, RiskScoreCategory

from ..case_test_utils import create_case
from ..mock_utils import FAKE_PROVIDER_FIELDS_DATA, get_issue_id

TEST_PERIOD_DAYS = 14
TODAY = datetime.now(UTC)
START_DAY_14_DAYS_AGO = TODAY - timedelta(days=TEST_PERIOD_DAYS)
CASES_PER_DAY = 6
EXPECTED_RANGE = TEST_PERIOD_DAYS + 1
TOTAL_CASES = TEST_PERIOD_DAYS * CASES_PER_DAY


def check_values(items, expected: list[int]):
    print("actual", [item.y for item in items])
    print("expected", expected)
    assert [item.y for item in items] == expected


@lru_cache
def _get_date_back(delta: int) -> datetime:
    return TODAY - timedelta(days=delta) + timedelta(minutes=1)


async def prepare_test_cases(service_dal_fixture: ServiceDAL):
    cases = []
    for i in range(TEST_PERIOD_DAYS):
        days_back = TEST_PERIOD_DAYS - i
        for j in range(CASES_PER_DAY):
            pos = i * CASES_PER_DAY + j
            provider_field = FAKE_PROVIDER_FIELDS_DATA[pos % len(FAKE_PROVIDER_FIELDS_DATA)]
            risk_score = list(RISK_SCORE_RANGES.items())[j % 3 + 1][1].start
            with freezegun.freeze_time(_get_date_back(days_back)):
                case = await create_case(
                    service_dal_fixture, get_issue_id(pos + 1), provider_fields=provider_field, risk_score=risk_score
                )
                cases.append(case)
    return cases


def created_expected(expected_categories: dict[str, tuple[int, int, int]]) -> dict[str, CasesPerCategoryCount]:
    expected = {}
    for key, value in expected_categories.items():
        expected[key] = CasesPerCategoryCount(
            risk_scores={
                RiskScoreCategory.NONE: 0,
                RiskScoreCategory.MONITOR: value[0],
                RiskScoreCategory.ANALYZE: value[1],
                RiskScoreCategory.INTERVENE: value[2],
            }
        )
    return expected
