from datetime import UTC, datetime, timedelta

import freezegun
import pytest
from prime_rat_logic_service_client import Case<PERSON>tatus

from service.db import ServiceDA<PERSON>
from service.logic.filters_and_sort import CaseFilters
from service.logic.statistics import CaseByStatus
from service.models import RiskScoreCategory
from service.models.filters_and_sort import Filter, Operator
from tests.case_test_utils import create_cases
from tests.mock_utils import SOURCE_ID

from .helpers import (
    CASES_PER_DAY,
    EXPECTED_RANGE,
    START_DAY_14_DAYS_AGO,
    TEST_PERIOD_DAYS,
    TODAY,
    check_values,
    prepare_test_cases,
)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestCaseByStatus:
    async def test_case_by_status(self, service_dal_fixture: ServiceDAL, account_id: str):
        """Test case by status."""
        await prepare_test_cases(service_dal_fixture)
        # make sure there is another account to test for separation
        await create_cases(service_dal_fixture, f"{account_id}2", count=5)

        cases_info = CaseByStatus(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)

        cases_info_results = await cases_info.get_stats()
        assert cases_info_results.start == START_DAY_14_DAYS_AGO
        assert cases_info_results.end == TODAY

        assert len(cases_info_results.scanned) == EXPECTED_RANGE
        expected_values = [CASES_PER_DAY * i for i in range(EXPECTED_RANGE)]
        check_values(cases_info_results.scanned, expected_values)

        assert len(cases_info_results.identified) == EXPECTED_RANGE
        expected_values = [CASES_PER_DAY * i for i in range(EXPECTED_RANGE)]
        check_values(cases_info_results.identified, expected_values)

        assert len(cases_info_results.close) == EXPECTED_RANGE
        check_values(cases_info_results.close, [0] * EXPECTED_RANGE)

    async def test_case_by_status_with_filter(self, service_dal_fixture: ServiceDAL, account_id: str):
        await prepare_test_cases(service_dal_fixture)
        cases_info = CaseByStatus(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        filters = CaseFilters([Filter(field="risk_score_category", value=RiskScoreCategory.INTERVENE, op=Operator.EQ)])
        cases_info_results = await cases_info.get_stats(filters)
        expected_values = [CASES_PER_DAY * i / 3 for i in range(EXPECTED_RANGE)]
        check_values(cases_info_results.identified, expected_values)

    async def test_case_by_status_total(self, service_dal_fixture: ServiceDAL, account_id: str):
        await prepare_test_cases(service_dal_fixture)
        cases_info = CaseByStatus(service_dal_fixture, account_id, datetime.today(), datetime.today())
        cases_info_results = await cases_info.get_stats()
        assert cases_info_results.identified[-1].y == CASES_PER_DAY * TEST_PERIOD_DAYS
        assert cases_info_results.scanned[-1].y == CASES_PER_DAY * TEST_PERIOD_DAYS
        assert cases_info_results.close[-1].y == 0

    async def test_case_by_status_deleted(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        cases[-1].deleted_at = datetime.now(UTC)
        cases[0].deleted_at = datetime.now(UTC)
        await service_dal_fixture.session.commit()

        daily_stats = CaseByStatus(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        daily_results = await daily_stats.get_stats()

        expected_values = [CASES_PER_DAY * i for i in range(EXPECTED_RANGE)]
        for i in range(1, len(expected_values)):
            expected_values[i] -= 1
        expected_values[-1] -= 1
        check_values(daily_results.scanned, expected_values)
        check_values(daily_results.identified, expected_values)
        check_values(daily_results.close, [0] * EXPECTED_RANGE)

    async def test_case_by_status_changed(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        with freezegun.freeze_time(datetime.now(UTC) - timedelta(days=1)):
            await service_dal_fixture.cases_dal.update_case_status(
                account_id, SOURCE_ID, cases[-1].issue_id, CaseStatus.DISMISSED
            )
            await service_dal_fixture.cases_dal.update_case_status(
                account_id, SOURCE_ID, cases[-2].issue_id, CaseStatus.DONE
            )
        with freezegun.freeze_time(datetime.now(UTC) - timedelta(days=2)):
            await service_dal_fixture.cases_dal.update_case_status(
                account_id, SOURCE_ID, cases[-3].issue_id, CaseStatus.DONE
            )
        with freezegun.freeze_time(datetime.now(UTC) - timedelta(days=3)):
            await service_dal_fixture.cases_dal.update_case_status(
                account_id, SOURCE_ID, cases[0].issue_id, CaseStatus.DISMISSED
            )
        await service_dal_fixture.session.commit()

        daily_stats = CaseByStatus(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        daily_results = await daily_stats.get_stats()
        expected_values = [CASES_PER_DAY * i for i in range(EXPECTED_RANGE)]
        check_values(daily_results.scanned, expected_values)
        check_values(daily_results.identified, expected_values)
        expected_values = [0] * EXPECTED_RANGE
        expected_values[-3:] = [1, 2, 4]
        check_values(daily_results.close, expected_values)
