from __future__ import annotations

from datetime import date, datetime

import pytest
from prime_shared.common_types import AccountIdType

from service.db import ServiceDAL
from service.logic.statistics import BaseQuery
from service.models.statistics import BaseCustomerTrends


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
def test_start_and_end_date_same_type(account_id: str):
    class FakeCustomerStats(BaseCustomerTrends):
        pass

    class FakeQuery(BaseQuery[FakeCustomerStats]):
        __queryname__ = "fake_query"

        @classmethod
        def build(
            cls,
            service_dal: ServiceDAL,
            account_id: AccountIdType,
            start_date: datetime | date,
            end_date: datetime | date,
        ) -> FakeQuery:
            return cls(service_dal, account_id, start_date, end_date)

        async def get_stats(self) -> FakeCustomerStats:
            raise NotImplementedError

    with pytest.raises(TypeError):
        FakeQuery.build(None, account_id, date.today(), datetime.today())

    with pytest.raises(TypeError):
        FakeQuery(None, account_id, datetime.today(), datetime.today())

    with pytest.raises(TypeError):
        FakeQuery.build(None, account_id, datetime.today(), date.today())

    with pytest.raises(TypeError):
        FakeQuery(None, account_id, date.today(), datetime.today())
