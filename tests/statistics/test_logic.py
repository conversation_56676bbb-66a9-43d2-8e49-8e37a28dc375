from datetime import datetime, timedelta

import pytest

from service.db import ServiceDAL
from service.logic.statistics import CaseByStatus, Granularity


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
def test_calculate_chart_size(service_dal_fixture: ServiceDAL, account_id: str):
    today = datetime.today()

    handler = CaseByStatus(service_dal_fixture, account_id, today - timedelta(days=7), today, Granularity.hour)
    assert handler._calculate_chart_size() == 168
    assert handler._get_datetime_buckets() == [handler._start + timedelta(hours=i) for i in range(168)] + [handler._end]

    handler = CaseByStatus(service_dal_fixture, account_id, today - timedelta(days=14), today, Granularity.day)
    assert handler._calculate_chart_size() == 14
    assert handler._get_datetime_buckets() == [handler._start + timedelta(days=i) for i in range(14)] + [handler._end]

    handler = CaseByStatus(service_dal_fixture, account_id, today - timedelta(days=31), today, Granularity.week)
    assert handler._calculate_chart_size() == 4
    assert handler._get_datetime_buckets() == [handler._start + timedelta(weeks=i) for i in range(4)] + [handler._end]

    handler = CaseByStatus(service_dal_fixture, account_id, today - timedelta(days=31), today, Granularity.month)
    assert handler._calculate_chart_size() == 1
    assert handler._get_datetime_buckets() == [handler._start + timedelta(days=i * 30) for i in range(1)] + [
        handler._end
    ]

    handler = CaseByStatus(service_dal_fixture, account_id, today, today, Granularity.day)
    assert handler._calculate_chart_size() == 0
    assert handler._get_datetime_buckets() == [today]
