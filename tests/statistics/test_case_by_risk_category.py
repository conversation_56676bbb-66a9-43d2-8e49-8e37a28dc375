from datetime import UTC, datetime

import pytest

from service.db import <PERSON><PERSON><PERSON>
from service.logic.filters_and_sort import CaseFilters
from service.logic.statistics import CasesByRiskCategory
from service.models import CaseStatus, ProviderFieldType, RiskScoreCategory
from service.models.filters_and_sort import Filter, Operator
from tests.mock_utils import SOURCE_ID

from .helpers import (
    CASES_PER_DAY,
    EXPECTED_RANGE,
    START_DAY_14_DAYS_AGO,
    TODAY,
    check_values,
    prepare_test_cases,
)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestCaseByRiskCategory:
    async def test_case_by_risk_category(self, service_dal_fixture: ServiceDAL, account_id: str):
        await prepare_test_cases(service_dal_fixture)
        cases_stats = CasesByRiskCategory(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        cases_results = await cases_stats.get_stats()
        check_values(cases_results.analyze, [0, 1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26])
        check_values(cases_results.intervene, [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28])
        check_values(cases_results.monitor, [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27])

    async def test_case_by_risk_category_with_filter_risk(self, service_dal_fixture: ServiceDAL, account_id: str):
        await prepare_test_cases(service_dal_fixture)
        cases_stats = CasesByRiskCategory(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        filters = CaseFilters([Filter(field="risk_score_category", value=RiskScoreCategory.INTERVENE, op=Operator.EQ)])
        cases_results = await cases_stats.get_stats(filters)
        check_values(cases_results.monitor, [0] * EXPECTED_RANGE)
        check_values(cases_results.analyze, [0] * EXPECTED_RANGE)
        check_values(cases_results.intervene, [CASES_PER_DAY // 3 * i for i in range(EXPECTED_RANGE)])

    async def test_case_by_risk_category_with_filter_project(self, service_dal_fixture: ServiceDAL, account_id: str):
        await prepare_test_cases(service_dal_fixture)
        cases_stats = CasesByRiskCategory(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        project_name = "test_project_id2"
        filters = CaseFilters(
            [
                Filter(
                    field="provider_fields",
                    value=project_name,
                    op=Operator.EQ,
                    inner_field="project",
                    inner_field_type=ProviderFieldType.ENUM,
                )
            ]
        )
        cases_results = await cases_stats.get_stats(filters)
        check_values(cases_results.intervene, [0, 0, 1, 2, 3, 4, 4, 5, 6, 7, 8, 8, 9, 10, 11])
        check_values(cases_results.analyze, [0, 0, 1, 2, 3, 4, 4, 5, 6, 7, 8, 8, 9, 10, 11])
        check_values(cases_results.monitor, [0, 1, 2, 2, 3, 4, 5, 6, 6, 7, 8, 9, 10, 10, 11])

    async def test_case_by_risk_category_total(self, service_dal_fixture: ServiceDAL, account_id: str):
        await prepare_test_cases(service_dal_fixture)
        cases_stats = CasesByRiskCategory(service_dal_fixture, account_id, datetime.today(), datetime.today())
        cases_results = await cases_stats.get_stats()
        assert cases_results.analyze[-1].y == 26
        assert cases_results.intervene[-1].y == 28
        assert cases_results.monitor[-1].y == 27

    async def test_case_by_risk_category_status_modified(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        await service_dal_fixture.cases_dal.update_case_status(
            account_id,
            SOURCE_ID,
            cases[-1].issue_id,  # intervene
            CaseStatus.DISMISSED,
        )
        await service_dal_fixture.cases_dal.update_case_status(
            account_id,
            SOURCE_ID,
            cases[0].issue_id,  # monitor
            CaseStatus.DONE,
        )
        await service_dal_fixture.session.commit()

        cases_stats = CasesByRiskCategory(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        cases_results = await cases_stats.get_stats()
        intervene_expected_values = [CASES_PER_DAY // 3 * i for i in range(EXPECTED_RANGE)]
        intervene_expected_values[-1] -= 1
        check_values(cases_results.intervene, [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 27])
        check_values(cases_results.monitor, [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27])
        check_values(cases_results.analyze, [0, 1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26])

    async def test_case_by_risk_category_deleted(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await prepare_test_cases(service_dal_fixture)
        cases[-1].deleted_at = datetime.now(UTC)  # intervene
        cases[0].deleted_at = datetime.now(UTC)  # monitor
        await service_dal_fixture.session.commit()

        cases_stats = CasesByRiskCategory(service_dal_fixture, account_id, START_DAY_14_DAYS_AGO, TODAY)
        cases_results = await cases_stats.get_stats()
        check_values(cases_results.intervene, [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 27])
        check_values(cases_results.monitor, [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27])
        check_values(cases_results.analyze, [0, 1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26])
