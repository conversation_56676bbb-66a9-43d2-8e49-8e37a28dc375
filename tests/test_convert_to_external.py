# from datetime import date, datetime
#
# import pytest
# from prime_utils import get_all_subclasses
# from pydantic import ValidationError
#
# from service.models.statistics import (
#     CasesByRiskCategoryStats,
#     CasesByStatusStats,
#     CountCasesByRiskCategoryCustomerStats,
#     CountCasesByStatusCustomerStats,
#     DatePoint,
# )
# from service.models.statistics_external import (
#     BaseStsExternal,
#     CasesByRiskCategoryExternal,
#     CasesByStatusExternal,
#     T,
#     convert_to_external,
# )
#
# START_DATE = END_DATE = datetime.today()
#
#
# def generate_params(external_sts_model_cls: type[T], sts_model_cls, **rat_model_cls_kwargs):
#     return external_sts_model_cls, sts_model_cls(
#         start=START_DATE,
#         end=END_DATE,
#         **rat_model_cls_kwargs,
#     )
#
#
# test_params = [
#     (
#         CasesByStatusExternal,
#         CasesByStatusStats,
#         {
#             "query_name": "example_query",
#             "scanned_cases": [DatePoint(x=datetime.today(), y=1)],
#             "identified_cases": [DatePoint(x=datetime.today(), y=2)],
#             "close_cases": [DatePoint(x=datetime.today(), y=3)],
#         },
#     ),
#     (
#         CasesByRiskCategoryExternal,
#         CasesByRiskCategoryStats,
#         {
#             "query_name": "example_query",
#             "intervene_cases": [DatePoint(x=datetime.today(), y=1)],
#             "analyze_cases": [DatePoint(x=datetime.today(), y=2)],
#             "monitor_cases": [DatePoint(x=datetime.today(), y=3)],
#         },
#     ),
# ]
#
#
# @pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
# @pytest.mark.parametrize(
#     "external_sts_model_cls, sts_model_cls", [generate_params(a, b, **c) for a, b, c in test_params]
# )
# async def test_convert_to_external(external_sts_model_cls: type[T], sts_model_cls):
#     x = convert_to_external(external_sts_model_cls, sts_model_cls)
#     assert x.start_date == START_DATE.date()
#     assert x.end_date == END_DATE.date()
#
#
# def test_all_external_sts_models():
#     assert len(get_all_subclasses(BaseStsExternal)) == 4
#
#
# def test_base_sts_external_model():
#     x = BaseStsExternal(start_date=date.today(), end_date=date.today())
#     assert x.start_date == date.today()
#     assert x.end_date == date.today()
#     x = BaseStsExternal(start_date=datetime.today(), end_date=datetime.today())
#     assert x.start_date == date.today()
#     assert x.end_date == date.today()
#     with pytest.raises(ValidationError):
#         BaseStsExternal.model_validate({"start_date": "2000-1-1", "end_date": "2024-09-24 10:52:58.047529"})
