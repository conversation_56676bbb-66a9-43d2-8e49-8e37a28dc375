import contextlib
import functools
import json
import random
import zipfile
from collections.abc import Generator
from datetime import UTC, datetime
from functools import cache
from io import BytesIO
from pathlib import Path
from typing import Any
from unittest import mock
from urllib.parse import parse_qs, urlparse

from dateutil import parser as datetime_parser
from jira import JIR<PERSON>
from mock.mock import MagicMock
from orjson import orjson
from prime_chatbot_service_client.models import ConversationOutput
from prime_config_service_client.models import AccountConfig
from prime_file_manager_service_client import ChildLink, DocumentType, FileInfo
from prime_gen_ai_service_client import (
    AIPipelinesOutput,
    AssessmentResultFlowOutput,
    PersonalRecommendationsFlowOutput,
    RiskScorePromptOutput,
)
from prime_jira_client import PrimeJiraClient
from prime_service_kit.fastapi_utils.pagination import PaginationResponse
from prime_source_service_client import JiraConnectionDetails
from prime_source_service_client.models import SourceModel, SourceType
from prime_tests import <PERSON><PERSON><PERSON><PERSON>ponse, ServiceMocked, service_mocker

from service.logic.issues_graph import IssuesGraph
from service.logic.jira_manager import JiraFieldInfo, JiraFieldSchema, JiraPrimeIssue
from service.models import ProviderFieldInfo, ProviderFieldType
from service.services_clients import FILE_INFO_BATCH_SIZE

resources_dir = Path(__file__).parent / "_resources"


class RefString:
    def __init__(self, value: str):
        self.value = value

    def get(self) -> str:
        return self.value

    def set(self, value: str) -> None:
        self.value = value


ACCOUNT_ID_CONTEXT = RefString("")


def reset_account_id():
    ACCOUNT_ID_CONTEXT.set(f"test_account_id_{random.randint(1, 100000)}")


SOURCE_ID = 10
GOOGLE_SOURCE_ID = 11
EPIC_TYPE = "Epic"
TASK_TYPE = "Task"
STORY_TYPE = "Story"
JIRA_HOST = "https://prime-test.atlassian.net"
TOTAL_TESTS_ISSUES = 10  # TODO: make dynamic
JOB_ID = 1
JIRA_STRUCTURE = [
    ("ISSUE-1", None),
    ("ISSUE-2", "ISSUE-1"),
    ("ISSUE-3", "ISSUE-2"),
    ("ISSUE-4", None),
    ("ISSUE-5", "ISSUE-1"),
    ("ISSUE-6", "ISSUE-1"),
    ("ISSUE-7", None),
    ("ISSUE-8", None),
    ("ISSUE-9", "ISSUE-8"),
    ("ISSUE-10", "ISSUE-8"),
]

JIRA_CONTAINERS_ISSUES = {parent for _, parent in JIRA_STRUCTURE if parent is not None}
JIRA_LEAF_ISSUES = {issue for issue, _ in JIRA_STRUCTURE} - JIRA_CONTAINERS_ISSUES
POLICY_DATA = b"policy_data"
DESIGN_REVIEW_PROJECT_KEY = "SECURITY_REVIEW_PROJECT_KEY"


def generate_test_tree() -> IssuesGraph:
    return IssuesGraph.build_from_tuples(JIRA_STRUCTURE)


example_security_config = AccountConfig.model_validate_json((resources_dir / "config.json").read_text())


def mock_download_config(
    config_mocker: ServiceMocked, account_id: str, extra_jira_fields: list[str] | None = None, framework: str = None
) -> None:
    config_data = example_security_config.model_copy(deep=True)
    if extra_jira_fields:
        config_data.providers_attributes["jira"] += extra_jira_fields
    if framework:
        config_data.security_framework = framework
    config_mocker.get(f"config/{account_id}", MockResponse(config_data.model_dump(by_alias=True)))
    for view in config_data.query_cases_views + config_data.query_containers_views:
        config_mocker.get(f"query-view/{account_id}/view/{view.name}", MockResponse(view.model_dump()))


def get_issue_data(issue: int | str) -> dict[str, Any]:
    issue_number = TESTS_ISSUES.index(issue) if isinstance(issue, str) else issue
    return json.loads((resources_dir / "jira" / f"jira_issue{issue_number}.json").read_bytes())


def get_prime_issue(issue_id: str | int) -> JiraPrimeIssue:
    issue_number = int(issue_id.split("-")[-1]) if isinstance(issue_id, str) else issue_id
    issue = JiraPrimeIssue.from_dict(get_issue_data(issue_number))
    if parent_id := issue.fields.get("parent", {}).get("key"):
        issue.parent = get_prime_issue(parent_id)
    return issue


def get_issue_id(i: int) -> str:
    return f"ISSUE-{i}"


FAKE_PROVIDER_FIELDS_INFO = {
    "self": ProviderFieldInfo(type=ProviderFieldType.STRING, name="self", id="self"),
    "id": ProviderFieldInfo(type=ProviderFieldType.STRING, name="id", id="id"),
    "summary": ProviderFieldInfo(type=ProviderFieldType.STRING, name="summary", id="summary"),
    "created": ProviderFieldInfo(type=ProviderFieldType.DATE, name="created at", id="created"),
    "status": ProviderFieldInfo(type=ProviderFieldType.ENUM, name="status", id="status"),
    "creator": ProviderFieldInfo(type=ProviderFieldType.ENUM, name="creator", id="creator"),
    "project": ProviderFieldInfo(type=ProviderFieldType.STRING, name="project", id="project"),
    "field_1": ProviderFieldInfo(type=ProviderFieldType.STRING, name="field 1", id="field_1"),
    "field_2": ProviderFieldInfo(type=ProviderFieldType.NUMBER, name="field 2", id="field_2"),
    "field_3": ProviderFieldInfo(type=ProviderFieldType.NUMBER, name="field 3", id="field_3"),
    "field_4": ProviderFieldInfo(type=ProviderFieldType.BOOLEAN, name="field 4", id="field_4"),
    "field_5": ProviderFieldInfo(type=ProviderFieldType.DATE, name="field 5", id="field_5"),
    "field_6": ProviderFieldInfo(type=ProviderFieldType.ENUM, name="field 6", id="field_6"),
    "field_optional": ProviderFieldInfo(type=ProviderFieldType.ENUM, name="field optional", id="field_optional"),
    "field_array_optional": ProviderFieldInfo(
        type=ProviderFieldType.ARRAY, name="field array optional", id="field_array_optional"
    ),
    "labels": ProviderFieldInfo(type=ProviderFieldType.ARRAY, name="labels", id="labels"),
    "issuetype": ProviderFieldInfo(type=ProviderFieldType.ENUM, name="issue type", id="issuetype"),
    "customfield_10020": ProviderFieldInfo(type=ProviderFieldType.ARRAY, name="Sprint", id="customfield_10020"),
}

JIRA_TYPES = {
    "self": "string",
    "id": "string",
    "summary": "string",
    "created": "datetime",
    "status": "status",
    "creator": "user",
    "project": "string",
    "field_1": "string",
    "field_2": "number",
    "field_3": "number",
    "field_4": "boolean",
    "field_5": "date",
    "field_6": "option",
    "field_optional": "option",
    "field_array_optional": "array",
    "labels": "array",
    "issuetype": "option",
    "customfield_10020": "array",
}

FAKE_JIRA_FIELDS_SCHEMA = [
    JiraFieldInfo(
        id=FAKE_PROVIDER_FIELDS_INFO[field_id].id,
        key=FAKE_PROVIDER_FIELDS_INFO[field_id].id,
        name=FAKE_PROVIDER_FIELDS_INFO[field_id].name,
        schema=JiraFieldSchema(type=JIRA_TYPES[field_id]),
    )
    for field_id in FAKE_PROVIDER_FIELDS_INFO
]

FAKE_PROVIDER_FIELDS_DATA = json.loads((resources_dir / "fake_fields_data.json").read_text())
TESTS_ISSUES = [fake_provide_id["id"] for fake_provide_id in FAKE_PROVIDER_FIELDS_DATA]

REAL_JIRA_FIELDS_SCHEMA = json.loads((resources_dir / "jira" / "jira_fields.json").read_text())
LINK_RESPONSES = [
    ChildLink(link="http://stam_link1", link_type=None, id=1),
    ChildLink(link="http://stam_link2", link_type=DocumentType.JIRA, id=2),
    ChildLink(link="http://stam_link3", link_type=DocumentType.GDRIVE, id=2),
    ChildLink(link="http://stam_link4", link_type=DocumentType.CONFLUENCE, id=None),
]


def mock_get_issues_files(
    file_manager_mocker: ServiceMocked,
    account_id: str | None = None,
    timestamp: datetime | dict[str, datetime] = datetime.now(UTC),
    document_type: DocumentType = DocumentType.JIRA,
):
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    if document_type == DocumentType.JIRA:
        issues_data = [(key, json.dumps(get_issue_data(i + 1))) for i, key in enumerate(TESTS_ISSUES)]
    else:
        issues_data = []
    _set_files_mock(issues_data, file_manager_mocker, account_id, timestamp, document_type)


def mock_get_descendants_empty(file_manager_mocker: ServiceMocked, account_id: str):
    file_manager_mocker.get(f"/relationship/{account_id}/source/*/origin_id/*/descendants", MockResponse([]))


def mock_get_descendants_links(file_manager_mocker: ServiceMocked, account_id: str) -> list[ChildLink]:
    file_manager_mocker.get(
        f"/relationship/{account_id}/source/*/origin_id/*/descendants-links",
        MockResponse(json.dumps([x.model_dump() for x in LINK_RESPONSES])),
    )
    return LINK_RESPONSES


def get_zip_data(files: dict[str, bytes]) -> bytes:
    zip_buffer = BytesIO()
    zip_file = zipfile.ZipFile(zip_buffer, "a")
    for file_name, issue_data in files.items():
        zip_file.writestr(file_name, issue_data)
    zip_file.close()
    zip_buffer.seek(0)
    return zip_buffer.read()


def _set_files_mock(
    all_files: list[tuple[str, bytes]],
    file_manager_mocker: ServiceMocked,
    account_id: str,
    timestamp: datetime | dict[str, datetime] = datetime.now(UTC),
    document_type: DocumentType = DocumentType.JIRA,
) -> None:
    file_infos = []
    for file_id, (file_name, issue_data) in enumerate(all_files):
        origin_id = f"{file_name}.json"
        download_file_url = f"files/{account_id}/data/{file_id}"
        file_manager_mocker.get(download_file_url, MockResponse(issue_data))
        download_origin_url = f"files/{account_id}/data/source/{SOURCE_ID}/origin_id/{origin_id}"
        file_manager_mocker.get(download_origin_url, MockResponse(issue_data))
        file_timestamp = timestamp.get(origin_id, datetime.now(UTC)) if isinstance(timestamp, dict) else timestamp
        file_infos.append(
            FileInfo(
                id=file_id,
                origin_id=origin_id,
                domain="primesec.ai",
                timestamp=file_timestamp,
                downloadable_link=f"https://primesec.ai/{origin_id}",
                document_type=document_type,
            )
        )

    def _data_response(*args, **kwargs) -> MockResponse:
        file_names = args[3].get("file_names")
        _files = {}
        for _file_name, _issue_data in all_files:
            origin_id = f"{_file_name}.json"
            if file_names is None or origin_id in file_names:
                _files[origin_id] = _issue_data
        return MockResponse(get_zip_data(_files))

    file_manager_mocker.put(f"files/{account_id}/data/source/*", _data_response)
    file_manager_mocker.put(f"files/{account_id}/data", _data_response)

    def _info_response(*args, **kwargs) -> MockResponse:
        _file_infos = kwargs.get("file_infos")
        if since := parse_qs(urlparse(args[1]).query).get("since"):
            since = datetime_parser.parse(since[0])
        since_file_infos = [info for info in _file_infos if since is None or info.timestamp > since]
        info_response = PaginationResponse(
            results=[x.model_dump(by_alias=True) for x in since_file_infos],
            size=len(since_file_infos),
            limit=FILE_INFO_BATCH_SIZE,
            start=0,
            total=len(since_file_infos),
            has_next=False,
        )
        return MockResponse(info_response.model_dump_json())

    file_manager_mocker.put(f"files/{account_id}/info*", functools.partial(_info_response, file_infos=file_infos))


@contextlib.contextmanager
def _agent_api_mocks(
    account_id: str | None = None,
    conv_id: int | None = None,
) -> Generator[ServiceMocked]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    with service_mocker("chatbot-service") as chatbot_mocker:
        if conv_id:
            now = datetime.now(UTC)
            chatbot_mocker.get(
                f"/agent/{account_id}/conversation/{conv_id}",
                MockResponse(
                    ConversationOutput(conversation_id=conv_id, created_at=now, updated_at=now).model_dump_json()
                ),
            )

        yield chatbot_mocker


@contextlib.contextmanager
def _basic_mocks(
    *,
    selected_jira_fields: list[str] | None = None,
    jira_fields_override: list[dict[str, Any]] | None = None,
    framework: str | None = None,
    account_id: str | None = None,
) -> Generator[tuple[ServiceMocked, ServiceMocked, ServiceMocked]]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    with (
        service_mocker("file-manager-service") as file_manager_mocker,
        service_mocker("config-service") as config_mocker,
    ):
        mock_download_config(config_mocker, account_id, extra_jira_fields=selected_jira_fields, framework=framework)
        with mock_jira_client(jira_fields_override, account_id=account_id) as (
            _,
            _,
            source_mocker,
        ):
            yield file_manager_mocker, source_mocker, config_mocker


@contextlib.contextmanager
def mock_jira_client(
    jira_fields: list[dict[str, Any]] | None = None,
    account_id: str | None = None,
    get_jira_target: str = "service.logic.jira_manager.jira_fields_manager.get_jira_client",
) -> Generator[tuple[MagicMock, MagicMock, ServiceMocked]]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    with (
        mock.patch.object(JIRA, "server_info") as jira_server_info,
        mock.patch(get_jira_target, new_callable=mock.AsyncMock) as mock_get_jira_client,
        mock_source_service(account_id) as source_mocker,
    ):
        jira_server_info.return_value = {"versionNumbers": ("8", "1", "0"), "deploymentType": "Cloud"}
        client = PrimeJiraClient(JIRA_HOST, email="<EMAIL>", token="123456")
        client.server_info = mock.Mock(return_value=jira_server_info)
        mocked_fields = jira_fields or [item.model_dump(by_alias=True) for item in FAKE_JIRA_FIELDS_SCHEMA]
        client.fields = mock.Mock(return_value=mocked_fields)
        mock_get_jira_client.return_value = client
        yield jira_server_info, mock_get_jira_client, source_mocker


@contextlib.contextmanager
def mock_policy_service(account_id: str | None = None):
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    policy_zip_data = get_zip_data({"policy1": POLICY_DATA})
    with service_mocker("policy-service") as policy_mocker:
        policy_mocker.get(f"/{account_id}/policies/policy1/data", MockResponse(POLICY_DATA))
        policy_mocker.get(f"/{account_id}/policies/download_all", MockResponse(policy_zip_data))
        yield


@contextlib.contextmanager
def mock_source_service(account_id: str | None = None) -> Generator[ServiceMocked]:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    now = datetime.now(UTC)
    with service_mocker("source-service") as source_mocker:
        # JIRA
        jira_source = SourceModel(id=SOURCE_ID, source_type=SourceType.JIRA, created_at=now)
        source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}", MockResponse(jira_source.model_dump()))
        connection = JiraConnectionDetails(
            jira_url=JIRA_HOST,
            email="<EMAIL>",
            api_token="123456",
            design_review_projects=[DESIGN_REVIEW_PROJECT_KEY, "not_exists_project"],
        )
        conn_mocked_resp = MockResponse(connection.model_dump())
        source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}/connection_details/", conn_mocked_resp)

        # POLICY
        policy_source = SourceModel(id=-1, source_type=SourceType.POLICY, created_at=now)
        source_mocker.get(f"/sources/{account_id}/-1", MockResponse(policy_source.model_dump()))

        # GOOGLE
        google_source = SourceModel(id=GOOGLE_SOURCE_ID, source_type=SourceType.GOOGLE, created_at=now)
        source_mocker.get(f"/sources/{account_id}/{GOOGLE_SOURCE_ID}", MockResponse(google_source.model_dump()))

        def _sources_response(*args, **kwargs) -> MockResponse:
            _source_type = parse_qs(urlparse(args[1]).query).get("source_type")
            resp = []
            if SourceType.JIRA in _source_type:
                resp.append(jira_source.model_dump())
            if SourceType.POLICY in _source_type:
                resp.append(policy_source.model_dump())
            if SourceType.GOOGLE in _source_type:
                resp.append(google_source.model_dump())
            return MockResponse(orjson.dumps(resp))

        source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}/domain", MockResponse("https://test.com"))
        source_mocker.get(f"/sources/{account_id}?*", _sources_response)

        sources_list_mocked_resp = MockResponse(orjson.dumps([jira_source.model_dump()]))
        source_mocker.get(f"/sources/{account_id}", sources_list_mocked_resp)
        yield source_mocker


@cache
def get_assessment_result_output(is_dev: bool) -> AssessmentResultFlowOutput | None:
    return get_classification_output(is_security=is_dev).assessment_result


@cache
def get_risk_score_output(is_dev: bool) -> RiskScorePromptOutput | None:
    return get_classification_output(is_security=is_dev).risk_score


@cache
def get_classification_output(
    *,
    is_security: bool = True,
    with_recommendations: bool = True,
) -> AIPipelinesOutput:
    file_name = "MKT-1800_classification.json" if is_security else "CM-13270_output.json"
    model = AIPipelinesOutput.model_validate_json((resources_dir / "gen-ai-outputs" / file_name).read_text())
    if not with_recommendations:
        model.personal_recommendations = PersonalRecommendationsFlowOutput(personal_recommendations=[])
    return model
