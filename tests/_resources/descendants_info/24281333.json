{"id": "24281333", "status": "current", "title": "Project Kickoff: New Customer Portal", "space_id": "24281090", "parent_id": "24281306", "parent_type": "page", "position": 954, "author_id": "712020:cc3ab326-a7f4-48e1-a271-0bdcbd2b2ee0", "owner_id": "712020:cc3ab326-a7f4-48e1-a271-0bdcbd2b2ee0", "last_owner_id": null, "created_at": "2024-10-07T13:36:08.899000Z", "version": {"created_at": "2024-10-07T13:36:33.525000Z", "message": "", "number": 1, "minor_edit": false, "author_id": "712020:cc3ab326-a7f4-48e1-a271-0bdcbd2b2ee0"}, "body": {"storage": {"representation": "storage", "value": "<h2>Project Overview</h2><p>This page provides key information about the New Customer Portal project, including objectives, timeline, team members, and important resources.</p><h3>Objectives</h3><ul><li><p>Develop a user-friendly customer portal to improve client engagement</p></li><li><p>Streamline customer support processes</p></li><li><p>Increase customer satisfaction by 20% within 6 months of launch</p></li></ul><h2>Timeline</h2><ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\" ac:macro-id=\"181ba644-f38d-4939-a81b-6425ee91e3f2\"><ac:plain-text-body><![CDATA[MilestoneDateRequirements GatheringMarch 1-15, 2024Design PhaseMarch 16-31, 2024Development Sprint 1April 1-14, 2024Development Sprint 2April 15-28, 2024QA and TestingMay 1-15, 2024User Acceptance TestingMay 16-31, 2024LaunchJune 1, 2024]]></ac:plain-text-body></ac:structured-macro><h2>Team Members</h2><ul><li><p>Project Manager: <PERSON></p></li><li><p>Lead Developer: John Smith</p></li><li><p>UX Designer: Alice Johnson</p></li><li><p>QA Lead: Bob Williams</p></li><li><p>Customer Success Rep: <PERSON></p></li></ul><h2>Key Resources</h2><ul><li><p><a href=\"https://example.com/project-charter\">Project Charter</a></p></li><li><p><a href=\"https://example.com/wireframes\">Wireframes</a></p></li><li><p><a href=\"https://example.com/tech-specs\">Technical Specifications</a></p></li><li><p><a href=\"https://example.com/survey-results\">Customer Feedback Survey Results</a></p></li></ul><h2>Risks and Mitigation Strategies</h2><ol start=\"1\"><li><p><strong>Risk</strong>: Tight timeline may lead to quality issues <strong>Mitigation</strong>: Implement agile methodology with frequent check-ins and adjustments</p></li><li><p><strong>Risk</strong>: Integration with existing systems may be complex <strong>Mitigation</strong>: Conduct thorough system analysis in the initial phase and allocate extra time for integration testing</p></li></ol><h2>Next Steps</h2><ul><li><p>Schedule kickoff meeting with all team members</p></li><li><p>Finalize project charter and get stakeholder sign-off</p></li><li><p>Set up project management tool and add all team members</p></li><li><p>Begin requirements gathering phase</p></li></ul><h2>Questions or Concerns?</h2><p>Please reach out to Jane Doe (<a href=\"mailto:<EMAIL>\"><EMAIL></a>) with any questions or concerns about this project.</p><hr /><p>Last updated: [Current Date]</p>"}, "atlas_doc_format": null}, "links": {"webui": "/spaces/~712020cc3ab326a7f448e1a2710bdcbd2b2ee0/pages/24281333/Project+Kickoff+New+Customer+Portal", "editui": "/pages/resumedraft.action?draftId=24281333", "tinyui": "/x/9YByAQ"}}