def create_new_user(email, phone, ssn, date_of_birth):
    """
    Creates a new user account, ensuring the provided email, phone, and SSN are unique.

    Args:
        email (str): The user's email address.
        phone (str): The user's phone number.
        ssn (str): The user's Social Security Number.
        date_of_birth (date): The user's date of birth.

    Returns:
        User: The newly created user object.
    """
    # Validate that the email, phone, and SSN are unique
    if not validate_unique_user_fields(email, phone, ssn):
        raise ValueError("Email, phone, or SSN already exists in the system.")

    # Create the new user
    user = User.objects.create(email=email, phone=phone, ssn=ssn, date_of_birth=date_of_birth)
    return user