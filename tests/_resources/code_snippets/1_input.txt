
"""
Implement a data deduplication algorithm to check for existing accounts with the same SSN, DoB, email, and phone number during the registration process.
"""
def check_for_duplicate_accounts(ssn, dob, email, phone):
    """
    Check if an account with the given SSN, DoB, email, and phone number already exists.
    
    Args:
        ssn (str): The social security number to check.
        dob (str): The date of birth to check.
        email (str): The email address to check.
        phone (str): The phone number to check.
    
    Returns:
        bool: True if a duplicate account is found, False otherwise.
    """
    # Connect to the database and query for existing accounts
    existing_accounts = db.query(Account).filter(
        Account.ssn == ssn,
        Account.dob == dob,
        Account.email == email,
        Account.phone == phone
    ).all()
    
    return len(existing_accounts) > 0
