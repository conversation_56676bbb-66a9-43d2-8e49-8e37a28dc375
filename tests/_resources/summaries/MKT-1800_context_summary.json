{"context_summary": {"questions": {"what": {"summary": {"text": "Replace the json viewer library used in the KG Query Builder.", "quotes": {"quote": [{"quote_text": "We're using {{react-json-view}} library to display object &amp; array typed fields. And it doesn't support custom value renders there. We should be able to put filtering controls there.", "source": "main_jira_ticket.description"}]}}, "description": {"text": "The current library being used, `react-json-view`, does not support custom value renders. The goal is to replace this library with one that can support filtering controls.", "quotes": {"quote": [{"quote_text": "We're using {{react-json-view}} library to display object &amp; array typed fields. And it doesn't support custom value renders there. We should be able to put filtering controls there.", "source": "main_jira_ticket.description"}]}}}, "who": {"stakeholders": {"text": null, "quotes": null}, "affected": {"text": "KG Query Builder", "quotes": {"quote": [{"quote_text": "[KG Query Builder] Replace json viewer library", "source": "main_jira_ticket.summary"}]}}}, "where": {"environment": {"text": null, "quotes": null}, "components": {"text": "KG Query Builder", "quotes": {"quote": [{"quote_text": "[KG Query Builder] Replace json viewer library", "source": "main_jira_ticket.summary"}]}}, "products": {"text": "Knowledge Graph", "quotes": {"quote": [{"quote_text": "[KG Query Builder] Replace json viewer library", "source": "main_jira_ticket.summary"}]}}}, "why": {"purpose": {"text": "To replace the current json viewer library with one that supports custom value renders and filtering controls.", "quotes": {"quote": [{"quote_text": "We're using {{react-json-view}} library to display object &amp; array typed fields. And it doesn't support custom value renders there. We should be able to put filtering controls there.", "source": "main_jira_ticket.description"}]}}, "impact": {"text": "The new library will allow for more customization and functionality in the KG Query Builder.", "quotes": {"quote": [{"quote_text": "We're using {{react-json-view}} library to display object &amp; array typed fields. And it doesn't support custom value renders there. We should be able to put filtering controls there.", "source": "main_jira_ticket.description"}]}}}, "how": {"approach": {"text": null, "quotes": null}, "acceptance": {"text": null, "quotes": null}}}, "questions_summary": "The main purpose of this ticket is to replace the current JSON viewer library used in the KG Query Builder with a new library that supports custom value renders and filtering controls. The current library, `react-json-view`, does not provide this functionality. The ticket indicates that the new library should allow for more customization and improved functionality in the KG Query Builder.\n\nHowever, the ticket does not provide any information about the specific approach to be taken or the acceptance criteria for the replacement. Additionally, there are no details about the stakeholders or the environment where this change will be implemented.", "short_summary": "The task is to replace the json viewer library used in the KG Query Builder. The current library does not support custom value renders, and the goal is to find a new library that can support filtering controls."}, "error": null}