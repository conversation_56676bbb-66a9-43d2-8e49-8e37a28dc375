{"summary_output": {"questions": {"what": {"li_components": ["summary", "description"], "summary": {"text": "The summary indicates this is a test issue related to the Fetcher component, but no further details are provided.", "quotes": []}, "description": {"text": "The description field is None, so no details about the development task are available.", "quotes": []}}, "who": {"li_components": ["stakeholders", "affected"], "stakeholders": {"text": "None", "quotes": []}, "affected": {"text": "None", "quotes": []}}, "where": {"li_components": ["environment", "components", "products"], "environment": {"text": "None", "quotes": []}, "components": {"text": "None", "quotes": []}, "products": {"text": "None", "quotes": []}}, "why": {"li_components": ["purpose", "impact"], "purpose": {"text": "None", "quotes": []}, "impact": {"text": "None", "quotes": []}}, "how": {"li_components": ["approach", "acceptance"], "approach": {"text": "None", "quotes": []}, "acceptance": {"text": "None", "quotes": []}}, "html_format": "<b>What</b>\\n<li>summary: The summary indicates this is a test issue related to the <PERSON>tcher component, but no further details are provided.</li>\\n<li>description: The description field is None, so no details about the development task are available.</li>\\n<br>\\n<b>Who</b>\\n<li>stakeholders: None</li>\\n<li>affected: None</li>\\n<br>\\n<b>Where</b>\\n<li>environment: None</li>\\n<li>components: None</li>\\n<li>products: None</li>\\n<br>\\n<b>Why</b>\\n<li>purpose: None</li>\\n<li>impact: None</li>\\n<br>\\n<b>How</b>\\n<li>approach: None</li>\\n<li>acceptance: None</li>\\n<br>\\n"}, "questions_summary": "THIS IS A TEST SUMMARY", "issue_id": "ISSUE-1"}, "error": "None"}