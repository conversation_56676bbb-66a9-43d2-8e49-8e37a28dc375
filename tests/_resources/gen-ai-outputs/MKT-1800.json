{"jira_ticket": {"id": "MKT-1800", "summary": "App >> Lat/Lon optimisation", "description": "*Context:*\n\nAs per [https://eproject.atlassian.net/browse/MKT-1780|https://eproject.atlassian.net/browse/MKT-1780|smart-link] we are now sending lat/lon values upstream to bidders by performing a maxmind DB lookup. When we do not have GDPR consent, we seem to be still sending lat/lon but partially obfuscated to 2 digits. \n\n*Deliverables:*\n\n* Check if we can perform the maxmind lookup post obfuscation logic\n** This will mean we zero out the last octet of the IP address before the lookup happens so that the resulting lat/lon value is in line with GDPR requirements\n* If we cannot do the above, then do not send any lat/lon when we do not have GDPR consent", "type": "Task", "parent": {"id": "MKT-1779", "summary": "App Phase 1 - Monetisation", "description": "Our initial results from programmatic revenue has shown that there is currently limited revenue from the Ad Partners we work with in the current state.\n\nThis epic is to collect further strategies we can do to increase the monetisation of The Sun App.\n\nInitial reporting on revenue [here |https://theeproject.cloud.looker.com/looks/299]and copied below, this is from the last 7 days\n\n!image-20230830-134603.png|width=2271,height=304!\n\nPotential ideas for increasing revenue:\n\n* Pass lat/long in the bid request (feedback from Xandr).\n* Pass AdPartner ID in the bid request (feedback from Xandr, need to find out where we would pass that).\n* Instead of sending ifa as all 0000’s send as blank, this should increase bid rate (Wojtek feedback from OpenX).\n* Generate a MAID when we don’t have one by hashing the e ID and passing up in ifa parameter. (<PERSON>)\n** Add MAID  to it’s own ID graph.\n** Add MAID as a lookup on other ID graph.\n* Repackage the app request as a web request. Lots of business and technical considerations here.\n** Drop the app object, send a site object. Pass up buyeruid.\n** Ensure the Imp beacon is from a mobile web destination otherwise could be blocked by DSP/SSP.\n\nActions:\n\n* What part of the process zeros out the IFA. Matt to Rup.\n* Comparison of bid rate vs other partners. Matt\n* bid rate, win rate of MAID and w/o MAID. Scott\n* add did_hash and storing it in bidstream. <PERSON>\n* Rubicon seat spk to Ben. <PERSON>", "type": "Epic", "parent": {"id": "IN-52", "summary": "App", "description": "In Sept last year we did a POC to support video and display for APP. The outcome of that POC lives [here|https://eproject.atlassian.net/browse/PUBS-631]\n\nThis work is to productionise and monetise app inventory for at least 1 of our publishers.\n\nCurrent opportunity is with NewsUK. Other publisher details live [here|https://docs.google.com/presentation/d/1fb7LnCkH6Kykpz-4WLJWr72fY_rX1MEd8y7NRy6Ey2c/edit#slide=id.gefb68957b4_1_0]\n\nCurrent publisher CPMs are < £1 CPM mainly monetised via ADX. We need to think through which upstream platforms can monetise APP inventory outside our managed service.\n\n[Link |https://docs.google.com/spreadsheets/d/1yT472h7aRVUxWXH2h16cCONtXb5GMeakL6CCh1IeaX8/edit#gid=755037581]to Business case.\n\nMVP - [https://docs.google.com/document/d/1-oKyME6bAS3jJiq_DVgMil7dkFaqnIr-eqKR6VtNA7M/edit#heading=h.4rootrxgiibm|https://docs.google.com/document/d/1-oKyME6bAS3jJiq_DVgMil7dkFaqnIr-eqKR6VtNA7M/edit#heading=h.4rootrxgiibm|smart-link]", "type": "Initiative", "parent": null, "parent_summary": null}, "parent_summary": null}, "parent_summary": null}}