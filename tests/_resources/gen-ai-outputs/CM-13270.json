{"jira_ticket": {"id": "CM-13270", "summary": "[KG Query Builder] Replace json viewer library", "description": "We’re using {{react-json-view}} library to display object & array typed fields. And it doesn’t support custom value renders there. We should be able to put filtering controls there.\n\n\n", "type": "Task", "parent": {"id": "CM-11716", "summary": "[UI] Graph explorer - phase 3", "description": "Small tasks and bugs related to the graph view", "type": "Epic", "parent": null, "parent_summary": null}, "parent_summary": null}}