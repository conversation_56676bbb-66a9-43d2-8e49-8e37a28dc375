[{"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-1", "id": "ISSUE-1", "summary": "Issue 1 - lets go", "created": "2024-07-22 11:24:33.091271", "updated": "2024-07-22 11:10:00.000000", "status": "Open", "creator": "<PERSON>", "reporter": "<PERSON><PERSON>", "field_1": "never gonna", "field_2": 10, "field_3": 0.9894843984610379, "field_4": true, "project": "test_project_id", "field_5": "2024-01-01 11:24:33.091271", "labels": ["label1", "label2"], "field_6": "option1", "field_optional": "optional1", "field_array_optional": ["optional1", "optional2"], "issuetype": "Epic", "sprint": [{"id": 120, "name": "Sprint 34", "state": "active", "boardId": 2, "goal": "", "startDate": "2024-05-19T14:30:27.042Z", "endDate": "2024-05-30T04:19:25.000Z", "completeDate": "2024-06-18T11:40:22.959Z"}]}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-2", "id": "ISSUE-2", "summary": "Issue 2 is here", "created": "2024-07-27 11:24:33.091271", "updated": "2024-07-22 11:09:00.000000", "status": "Closed", "creator": "<PERSON> the 2nd", "reporter": "<PERSON><PERSON>", "field_1": "give you up", "field_2": 20, "field_3": 0.9577759778400627, "field_4": true, "field_5": "2023-01-01 11:24:33.091271", "project": "test_project_id", "labels": ["label2", "label3"], "sprint": [{"id": 120, "name": "Sprint 34", "state": "closed", "boardId": 2, "goal": "", "startDate": "2024-05-19T14:30:27.042Z", "endDate": "2024-05-30T04:19:25.000Z", "completeDate": "2024-06-18T11:40:22.959Z"}], "field_6": "option2", "field_optional": "", "field_array_optional": [], "parent_id": "ISSUE-1", "issuetype": "Task"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-3", "id": "ISSUE-3", "summary": "Issue 3 is here", "created": "2024-07-29 11:24:33.091271", "updated": "2024-07-22 11:08:00.000000", "status": "In Progress", "creator": "<PERSON> the 3rd", "reporter": "<PERSON><PERSON>", "field_1": "never gonna", "field_2": 30, "field_3": 0.4856025635426533, "field_4": false, "field_5": "2022-01-01 11:24:33.091271", "field_6": "option2", "project": "test_project_id", "labels": [], "field_optional": null, "field_array_optional": null, "parent_id": "ISSUE-2", "issuetype": "Subtask"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-4", "id": "ISSUE-4", "summary": "Issue 4 Title Lets do Issue 4 & and more", "created": "2024-08-07 11:24:33.091271", "updated": "2024-07-22 11:07:00.000000", "status": "Open", "creator": "<PERSON> the 4th", "reporter": "<PERSON><PERSON>", "field_1": "let you down", "field_2": 40, "field_3": 0.9289822064491423, "field_4": false, "field_5": "2021-01-01 11:24:33.091271", "labels": ["label7", "label10"], "field_6": "option4", "project": "test_project_id2", "issuetype": "Task"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-2", "id": "ISSUE-5", "summary": "Issue 5 is here", "created": "2025-02-22 11:24:33.091271", "updated": "2024-07-22 11:06:00.000000", "status": "Closed", "creator": "<PERSON> the 2nd", "reporter": "<PERSON><PERSON>", "field_1": "give you up", "field_2": 20, "field_3": 0.9577759778400627, "field_4": null, "field_5": "2023-01-01 11:24:33.091271", "project": "test_project_id", "labels": ["label3"], "field_6": "option2", "field_optional": "", "field_array_optional": [], "parent_id": "ISSUE-1", "issuetype": "Task"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-2", "id": "ISSUE-6", "summary": "Issue 6 is here", "created": "2025-02-22 11:24:33.091271", "updated": "2024-07-22 11:05:00.000000", "status": "Closed", "creator": "<PERSON> the 2nd", "reporter": "<PERSON><PERSON>", "field_1": "give you up", "field_2": 20, "field_3": 0.9577759778400627, "field_4": true, "field_5": "2023-01-01 11:24:33.091271", "project": "prim", "labels": ["label3"], "field_6": "option2", "field_optional": "", "field_array_optional": [], "parent_id": "ISSUE-1", "issuetype": "Task"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-2", "id": "ISSUE-7", "summary": "Issue 7 is here", "created": "2025-02-22 11:24:33.091271", "updated": "2024-07-22 11:04:00.000000", "status": "Closed", "creator": "<PERSON> the 2nd", "reporter": "<PERSON><PERSON>", "field_1": "give you up", "field_2": 20, "field_3": 0.9577759778400627, "field_4": true, "field_5": "2023-01-01 11:24:33.091271", "project": "test_project_id1", "labels": ["label3"], "field_6": "option2", "field_optional": "", "field_array_optional": [], "issuetype": "Epic"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-2", "id": "ISSUE-8", "summary": "Issue 8 is here", "created": "2025-02-22 11:24:33.091271", "updated": "2024-07-22 11:03:00.000000", "status": "Closed", "creator": "<PERSON> the 2nd", "reporter": "<PERSON><PERSON>", "field_1": "give you up", "field_2": 20, "field_3": 0.9577759778400627, "field_4": true, "field_5": "2023-01-01 11:24:33.091271", "project": "test_project_id2", "labels": ["label3"], "field_6": "option2", "field_optional": "", "field_array_optional": [], "issuetype": "Epic"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-2", "id": "ISSUE-9", "summary": "Issue 9 is here", "created": "2025-02-22 11:24:33.091271", "updated": "2024-07-22 11:02:00.000000", "status": "In Progress", "creator": "<PERSON> the 55nd", "reporter": "<PERSON><PERSON>", "field_1": "give you up", "field_2": 20, "field_3": 0.9577759778400627, "field_4": true, "field_5": "2023-01-01 11:24:33.091271", "project": "test_project_id2", "parent_id": "ISSUE-8", "labels": ["label3"], "field_6": "option2", "field_optional": "", "field_array_optional": [], "issuetype": "Epic"}, {"self": "https://prime-test.atlassian.net/rest/api/2/issue/ISSUE-2", "id": "ISSUE-10", "summary": "Issue 10 is here", "created": "2025-02-22 11:24:33.091271", "updated": "2024-07-22 11:01:00.000000", "status": "In Progress", "creator": "<PERSON> the 2nd", "reporter": "<PERSON><PERSON>", "field_2": 20, "field_3": 0.9577759778400627, "field_4": true, "field_5": "2023-01-01 11:24:33.091271", "project": "test_project_id2", "parent_id": "ISSUE-8", "labels": ["label3"], "field_6": "option2", "field_optional": "optional2", "field_array_optional": [], "issuetype": "Task"}]