import contextlib
import json
from pathlib import Path
from unittest.mock import patch

from prime_gen_ai_service_client import (
    PersonalRecommendationsTaskOutput,
)
from prime_rat_logic_service_client import GenerateRecommendationsForConcernIdsRequest
from prime_redis_utils import AsyncPrefixRedisClient

from client.prime_rat_logic_service_client.api.cases_api import CasesApi
from service.db import ServiceDAL
from service.logic.recommendation_on_demand import _hash_request_parameters, generate
from tests.mock_utils import SOURCE_ID, _basic_mocks, mock_get_issues_files

from ..case_test_utils import create_cases

resources_dir = Path(__file__).parent.parent / "_resources"

RECOMMENDATION_ON_DEMAND_ONE_CONCERN = json.loads(
    (resources_dir / "recommendation_on_demand_ai_result_one_concern.json").read_text()
)
RECOMMENDATION_ON_DEMAND_MULTIPLE_CONCERNS = json.loads(
    (resources_dir / "recommendation_on_demand_ai_result_multiple_concerns.json").read_text()
)


def get_recommendation_output(is_multiple_concerns: bool) -> PersonalRecommendationsTaskOutput:
    if is_multiple_concerns:
        return PersonalRecommendationsTaskOutput.model_validate(RECOMMENDATION_ON_DEMAND_MULTIPLE_CONCERNS)
    return PersonalRecommendationsTaskOutput.model_validate(RECOMMENDATION_ON_DEMAND_ONE_CONCERN)


@contextlib.contextmanager
def mock_wait_for_recommendation_on_demand_result(is_multiple_concerns: bool):
    with patch(
        "service.k8s_jobs.base_job_logic.gen_ai_celery.celery_manager.CeleryManager.send_and_wait_for_result",
        return_value=get_recommendation_output(is_multiple_concerns).model_dump(),
    ) as wait_patch:
        yield wait_patch


class TestRecommendationOnDemand:
    async def test_generate_recommendation_on_demand_api_called(self, cases_api: CasesApi, account_id: str):
        with patch("service.logic.recommendation_on_demand.generate") as mock_generate:
            mock_generate.return_value = None

            request = GenerateRecommendationsForConcernIdsRequest(concern_ids=[1, 2, 3])
            response = await cases_api.generate_recommendations_for_concern_ids(
                account_id, SOURCE_ID, "AI-27", "test_user", request
            )

            assert response
            mock_generate.assert_called_once()

    async def test_generate_recommendation_on_demand_already_running(self, cases_api: CasesApi, account_id: str):
        with (
            patch("service.logic.recommendation_on_demand.generate") as mock_generate,
            patch(
                "service.logic.recommendation_on_demand._is_ai_already_running_for_issue_id"
            ) as mock_is_ai_already_running_for_issue_id,
        ):
            mock_generate.return_value = None
            mock_is_ai_already_running_for_issue_id.return_value = True

            request = GenerateRecommendationsForConcernIdsRequest(concern_ids=[1, 2, 3])
            response = await cases_api.generate_recommendations_for_concern_ids(
                account_id, SOURCE_ID, "AI-27", "test_user", request
            )

            assert response is False
            mock_generate.assert_not_called()

    async def test_hash_parameters(self):
        hashed_parameter = _hash_request_parameters("account_id1", 2, "issue_id1")
        assert hashed_parameter == "account_id1-2-issue_id1"

    async def test_generate_one_concern(
        self,
        service_dal_fixture: ServiceDAL,
        cases_api: CasesApi,
        account_id: str,
        account_redis_client: AsyncPrefixRedisClient,
    ):
        cases = await create_cases(service_dal_fixture)
        case_to_test = cases[0]

        # Assert no recommendations or controls for the issue
        issue_analysis_to_test = await service_dal_fixture.issues_analysis_dal.get_issue_analysis_by_id(
            case_to_test.issue_analysis_id
        )
        assert not case_to_test.recommendations
        assert not issue_analysis_to_test.controls

        # Call generate with one concern
        request = GenerateRecommendationsForConcernIdsRequest(concern_ids=[1])
        hashed_parameter = _hash_request_parameters(account_id, SOURCE_ID, case_to_test.issue_id)
        with _basic_mocks() as (file_manager_mocker, _, _), mock_wait_for_recommendation_on_demand_result(False):
            mock_get_issues_files(file_manager_mocker)
            await generate(
                service_dal_fixture,
                account_id,
                SOURCE_ID,
                case_to_test.issue_id,
                set(request.concern_ids),
                "test_user",
                hashed_parameter,
                account_redis_client,
            )

        # Assert that the recommendations and controls were generated
        res_case = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case_to_test.id)
        assert len(res_case.recommendations) == 710
        assert all(rec.concern_id == 1 for rec in res_case.recommendations)
        res_issue_analysis = await service_dal_fixture.issues_analysis_dal.get_issue_analysis_by_id(
            res_case.issue_analysis_id
        )
        assert len(res_issue_analysis.controls) == 6

        # Generate again and assert result the same, with override the old results
        with _basic_mocks() as (file_manager_mocker, _, _), mock_wait_for_recommendation_on_demand_result(False):
            mock_get_issues_files(file_manager_mocker)
            await generate(
                service_dal_fixture,
                account_id,
                SOURCE_ID,
                case_to_test.issue_id,
                set(request.concern_ids),
                "test_user",
                hashed_parameter,
                account_redis_client,
            )

        res_case = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case_to_test.id)
        await self._assert_recommendations(account_id, service_dal_fixture, case_to_test.id, 710)

        assert all(rec.concern_id == 1 for rec in res_case.recommendations)

        await self._assert_controls(service_dal_fixture, res_case.issue_analysis_id, 6)

    async def test_generate_multiple_concerns_with_data_already_in_db_for_some_concerns(
        self,
        service_dal_fixture: ServiceDAL,
        cases_api: CasesApi,
        account_id: str,
        account_redis_client: AsyncPrefixRedisClient,
    ):
        cases = await create_cases(service_dal_fixture)
        case_to_test = cases[0]

        # Assert no recommendations or controls for the issue
        issue_analysis_to_test = await service_dal_fixture.issues_analysis_dal.get_issue_analysis_by_id(
            case_to_test.issue_analysis_id
        )
        assert not case_to_test.recommendations
        assert not issue_analysis_to_test.controls

        request = GenerateRecommendationsForConcernIdsRequest(concern_ids=[1])
        hashed_parameter = _hash_request_parameters(account_id, SOURCE_ID, case_to_test.issue_id)
        with _basic_mocks() as (file_manager_mocker, _, _), mock_wait_for_recommendation_on_demand_result(False):
            mock_get_issues_files(file_manager_mocker)
            await generate(
                service_dal_fixture,
                account_id,
                SOURCE_ID,
                case_to_test.issue_id,
                set(request.concern_ids),
                "test_user",
                hashed_parameter,
                account_redis_client,
            )

        res_case = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case_to_test.id)

        assert len(res_case.recommendations) == 710

        assert all(rec.concern_id == 1 for rec in res_case.recommendations)

        await self._assert_controls(service_dal_fixture, res_case.issue_analysis_id, 6)

        # Generate again for multiple concerns and assert new result with old didn't override
        request = GenerateRecommendationsForConcernIdsRequest(concern_ids=[2, 3, 4])

        with _basic_mocks() as (file_manager_mocker, _, _), mock_wait_for_recommendation_on_demand_result(True):
            mock_get_issues_files(file_manager_mocker)
            await generate(
                service_dal_fixture,
                account_id,
                SOURCE_ID,
                case_to_test.issue_id,
                set(request.concern_ids),
                "test_user",
                hashed_parameter,
                account_redis_client,
            )

        await self._assert_recommendations(account_id, service_dal_fixture, case_to_test.id, 2477)

        assert any(rec.concern_id == 1 for rec in res_case.recommendations)
        assert any(rec.concern_id == 2 for rec in res_case.recommendations)
        assert any(rec.concern_id == 3 for rec in res_case.recommendations)
        assert any(rec.concern_id == 4 for rec in res_case.recommendations)

        await self._assert_controls(service_dal_fixture, res_case.issue_analysis_id, 21)

    async def _assert_controls(
        self, service_dal_fixture: ServiceDAL, issue_analysis_id: int, expected_controls_number: int
    ):
        issue_analysis = await service_dal_fixture.issues_analysis_dal.get_issue_analysis_by_id(issue_analysis_id)
        assert len(issue_analysis.controls) == expected_controls_number

        assert [control.id for control in issue_analysis.controls] == [
            str(i) for i in range(1, len(issue_analysis.controls) + 1)
        ]

    async def _assert_recommendations(
        self, account_id: str, service_dal_fixture: ServiceDAL, case_id: int, expected_recommendations_number: int
    ):
        case = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case_id)
        assert len(case.recommendations) == expected_recommendations_number
        assert [rec.id for rec in case.recommendations] == list(range(1, len(case.recommendations) + 1))
