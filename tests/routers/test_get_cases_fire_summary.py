import pytest
from prime_rat_logic_service_client import Cases<PERSON>pi

from service.db import ServiceDAL
from service.models.filters_and_sort import Filter, Operator
from tests.case_test_utils import create_cases, modify_fire_summary
from tests.mock_utils import TOTAL_TESTS_ISSUES, _basic_mocks


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCasesFireSummary:
    async def test_filter_equal_operation_fire_summary(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture)
            # Currently all the tickets contains the same fire summary - modify the fire summary for one of the cases
            issue_analysis_id = cases[0].issue_analysis_id
            await modify_fire_summary(service_dal_fixture.session, issue_analysis_id, "Some summary")

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="fire_summary", value="Optimize lat/lon in app bid for GDPR", op=Operator.EQ
                    ).as_json_str()
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES - 1

    async def test_filter_equal_operation_fire_summary_empty(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)
            account_cases = await cases_api.get_cases_for_account(
                account_id, f=[Filter(field="fire_summary", value="", op=Operator.EQ).as_json_str()]
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES

    async def test_filter_equal_operation_fire_summary_value_not_exists(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)
            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field="fire_summary", value="Some not exists value", op=Operator.EQ).as_json_str()],
            )
            assert len(account_cases.results) == 0

    async def test_filter_not_equal_operation_fire_summary(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="fire_summary", value="Optimize lat/lon in app bid for GDPR", op=Operator.NE
                    ).as_json_str()
                ],
            )
            assert len(account_cases.results) == 0

    async def test_filter_not_equal_operation_fire_summary_empty(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)
            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field="fire_summary", value="", op=Operator.NE).as_json_str()],
            )
            assert len(account_cases.results) == 0

    async def test_filter_not_equal_operation_fire_summary_value_not_exists(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)
            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field="fire_summary", value="Some not exists value", op=Operator.NE).as_json_str()],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES

    async def test_filter_equal_operation_fire_summary_list(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture)
            # Currently all the tickets contains the same fire summary - modify the fire summary for one of the cases
            issue_analysis_id = cases[0].issue_analysis_id
            await modify_fire_summary(service_dal_fixture.session, issue_analysis_id, "Some summary")

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="fire_summary", value=["Optimize lat/lon in app bid for GDPR"], op=Operator.EQ
                    ).as_json_str()
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES - 1

    async def test_filter_equal_operation_fire_summary_case_sensitive(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture)
            # Currently all the tickets contains the same fire summary - modify the fire summary for one of the cases
            issue_analysis_id = cases[0].issue_analysis_id
            await modify_fire_summary(service_dal_fixture.session, issue_analysis_id, "Some summary")

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="fire_summary", value="OPTIMIZE LAT/LON IN APP BID FOR GDPR", op=Operator.EQ
                    ).as_json_str()
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES - 1
