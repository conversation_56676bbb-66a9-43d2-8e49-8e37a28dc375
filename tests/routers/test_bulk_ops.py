from unittest.mock import patch

import pytest
from prime_rat_logic_service_client import <PERSON><PERSON>tat<PERSON>, RiskScoreCategory
from prime_rat_logic_service_client.api.cases_api import BulkUpdateCasesRequest, CasesApi
from sqlmodel import select

from service.db import CaseHistoryTable, ServiceDAL
from service.models import risk_category_to_score, risk_score_to_category
from tests.case_test_utils import create_case, create_cases
from tests.mock_utils import SOURCE_ID, TOTAL_TESTS_ISSUES, get_issue_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestBulkOps:
    async def test_bulk_update_failure(self, cases_api: CasesApi, service_dal_fixture: ServiceDAL, account_id: str):
        bulk_update_data = {
            "status": CaseStatus.DONE,
            "labels": ["urgent", "security"],
            "risk_score_category": RiskScoreCategory.ANALYZE,
        }
        created_cases = await create_cases(
            service_dal_fixture,
            account_id,
            SOURCE_ID,
            extra=[{"risk_score": risk_category_to_score(RiskScoreCategory.INTERVENE).start}] * 10,
        )
        [await service_dal_fixture.set_labels(account_id, SOURCE_ID, c.issue_id, ["high"]) for c in created_cases]
        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[c.issue_id for c in created_cases])
        with patch("sqlmodel.ext.asyncio.session.AsyncSession.commit", side_effect=Exception("TEST")):
            with pytest.raises(Exception) as e:
                await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)
            assert e.value.args[0] == "TEST"
        db_cases = await service_dal_fixture.cases_dal.get_cases_by(
            account_id, SOURCE_ID, issues=[c.issue_id for c in created_cases]
        )
        assert created_cases == db_cases
        assert (await cases_api.get_labels(account_id)) == ["high"]
        assert (await service_dal_fixture.session.exec(select(CaseHistoryTable))).all() == []

    async def test_bulk_update_status_and_labels_and_risk_score(
        self, cases_api: CasesApi, service_dal_fixture: ServiceDAL, account_id: str
    ):
        bulk_update_data = {
            "status": CaseStatus.DISMISSED,
            "dismissed_reason": "test_reason",
            "labels": ["urgent", "security"],
            "risk_score_category": RiskScoreCategory.ANALYZE,
        }
        created_cases = await create_cases(
            service_dal_fixture,
            account_id,
            SOURCE_ID,
            extra=[{"risk_score": risk_category_to_score(RiskScoreCategory.INTERVENE).start}] * 10,
        )
        [await service_dal_fixture.set_labels(account_id, SOURCE_ID, c.issue_id, ["high"]) for c in created_cases]

        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[c.issue_id for c in created_cases])
        await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)
        [await service_dal_fixture.session.refresh(c) for c in created_cases]

        # Assert updated fields
        for case in created_cases:
            assert case.status == CaseStatus.DISMISSED
            assert case.dismissed_reason == "test_reason"
            assert set(case.labels) == {"high", "urgent", "security"}
            assert risk_score_to_category(case.original_risk_score) == RiskScoreCategory.INTERVENE
            issue_analysis = await service_dal_fixture.issues_analysis_dal.get_issue_analysis_by_id(
                case.issue_analysis_id
            )
            assert risk_score_to_category(issue_analysis.risk_score) == RiskScoreCategory.ANALYZE

        # Check history and label tables
        history_records = (await service_dal_fixture.session.exec(select(CaseHistoryTable))).all()
        assert (
            len(history_records) == TOTAL_TESTS_ISSUES * 2
        )  # 1 entry per case for the status change and 1 for risk_score change
        assert len([r.audit_action for r in history_records if r.audit_action == "update_status"]) == TOTAL_TESTS_ISSUES
        assert (
            len([r.audit_action for r in history_records if r.audit_action == "override_risk_category"])
            == TOTAL_TESTS_ISSUES
        )
        account_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert {label.name for label in account_labels} == {"high", "urgent", "security"}

    async def test_bulk_update_status(self, cases_api: CasesApi, service_dal_fixture: ServiceDAL, account_id: str):
        bulk_update_data = {"status": CaseStatus.DONE}
        created_cases = await create_cases(service_dal_fixture, account_id)

        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[c.issue_id for c in created_cases])
        await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)

        [await service_dal_fixture.session.refresh(c) for c in created_cases]

        for case in created_cases:
            assert case.status == CaseStatus.DONE
            assert case.original_risk_score is None  # Unchanged
            assert case.labels == []  # Unchanged
            history_records = (
                await service_dal_fixture.session.exec(
                    select(CaseHistoryTable).where(CaseHistoryTable.case_id == case.id)
                )
            ).all()
            assert history_records
            assert all(record.audit_action == "update_status" for record in history_records)

    async def test_bulk_update_add_labels(self, cases_api: CasesApi, service_dal_fixture: ServiceDAL, account_id: str):
        created_cases = await create_cases(service_dal_fixture, account_id, count=3)

        [await service_dal_fixture.set_labels(account_id, SOURCE_ID, c.issue_id, ["high"]) for c in created_cases]

        bulk_update_data = {"labels": []}
        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[c.issue_id for c in created_cases])
        await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)
        db_cases = await service_dal_fixture.cases_dal.get_cases_by(
            account_id, SOURCE_ID, issues=[c.issue_id for c in created_cases]
        )
        assert all(c.labels == ["high"] for c in db_cases)

        bulk_update_data = {"labels": ["urgent", "low"]}
        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[c.issue_id for c in created_cases])
        await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)

        [await service_dal_fixture.session.refresh(c) for c in created_cases]
        for case in created_cases:
            assert set(case.labels) == {"high", "urgent", "low"}
            assert case.status == CaseStatus.OPEN  # Unchanged
            assert case.original_risk_score is None  # Unchanged
            assert case.case_history == []

    async def test_bulk_update_risk_score(self, cases_api: CasesApi, service_dal_fixture: ServiceDAL, account_id: str):
        bulk_update_data = {"risk_score_category": RiskScoreCategory.ANALYZE}
        created_cases = await create_cases(
            service_dal_fixture,
            account_id,
            SOURCE_ID,
            count=3,
            extra=[{"risk_score": risk_category_to_score(RiskScoreCategory.INTERVENE).start}] * 3,
        )
        original_status = created_cases[0].status
        original_labels = []

        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[c.issue_id for c in created_cases])
        await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)

        [await service_dal_fixture.session.refresh(c) for c in created_cases]

        for case in created_cases:
            assert risk_score_to_category(case.original_risk_score) == RiskScoreCategory.INTERVENE
            assert case.status == original_status  # Unchanged
            assert case.labels == original_labels  # Unchanged
            history_record = (
                await service_dal_fixture.session.exec(
                    select(CaseHistoryTable).where(CaseHistoryTable.case_id == case.id)
                )
            ).one()
            assert history_record.audit_action == "override_risk_category"

    async def test_bulk_update_case_without_issue_analysis(
        self, cases_api: CasesApi, service_dal_fixture: ServiceDAL, account_id: str
    ):
        bulk_update_data = {
            "labels": ["urgent", "security"],
            "risk_score_category": RiskScoreCategory.ANALYZE,
        }

        created_case = await create_case(
            service_dal_fixture,
            get_issue_id(0),
            with_analysis=False,
        )

        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[created_case.issue_id])
        await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)

        await service_dal_fixture.session.refresh(created_case)

        assert set(created_case.labels) == {"urgent", "security"}
        assert created_case.original_risk_score is None
        assert not (
            await service_dal_fixture.session.exec(
                select(CaseHistoryTable).where(CaseHistoryTable.case_id == created_case.id)
            )
        ).all()

    async def test_bulk_update_risk_score_for_case_with_classification_false(
        self, cases_api: CasesApi, service_dal_fixture: ServiceDAL, account_id: str
    ):
        bulk_update_data = {
            "labels": ["urgent", "security"],
            "risk_score_category": RiskScoreCategory.ANALYZE,
        }

        created_case = await create_case(
            service_dal_fixture,
            get_issue_id(0),
            with_analysis=True,
            classification=False,
        )

        request_data = BulkUpdateCasesRequest(**bulk_update_data, issues_ids=[created_case.issue_id])
        await cases_api.bulk_update_cases(account_id, SOURCE_ID, bulk_update_cases_request=request_data)

        await service_dal_fixture.session.refresh(created_case)

        assert set(created_case.labels) == {"urgent", "security"}
        assert created_case.original_risk_score is None
        assert not (
            await service_dal_fixture.session.exec(
                select(CaseHistoryTable).where(CaseHistoryTable.case_id == created_case.id)
            )
        ).all()
