from datetime import UTC, datetime, timedelta

import freezegun
import pytest
from prime_rat_logic_service_client import CasesPerCategoryCount

from client.prime_rat_logic_service_client.api.trends_api import TrendsApi
from service.db import ServiceDAL
from service.models.psv import PsvStatus
from tests.mock_utils import _basic_mocks, get_issue_id
from tests.psv_utils import create_psvs

from ..case_test_utils import create_case, create_cases
from ..statistics.helpers import check_values
from .test_get_cases_view import test_cases_view_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestStats:
    async def test_get_cases_by_risk_category(
        self, service_dal_fixture: ServiceDAL, trends_api: TrendsApi, account_id: str
    ):
        extra = [{"risk_score": risk_score} for risk_score in [10, 40, 80, 100]]
        await create_cases(service_dal_fixture, count=4, extra=extra)
        with freezegun.freeze_time(time_to_freeze=datetime.now(UTC) - timedelta(days=2)):
            await create_case(service_dal_fixture, get_issue_id(5), risk_score=44)  # analyze issue
        results = await trends_api.get_cases_by_risk_category(account_id=account_id)
        assert results.start is not None
        assert results.end is not None
        check_values(results.intervene, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2])
        check_values(results.analyze, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1])
        check_values(results.monitor, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])

    async def test_get_cases_by_status(self, service_dal_fixture: ServiceDAL, trends_api: TrendsApi, account_id: str):
        extra = [{"classification": True}, {"classification": False}]
        await create_cases(service_dal_fixture, count=2, extra=extra)
        with freezegun.freeze_time(time_to_freeze=datetime.now(UTC) - timedelta(days=1)):
            await create_case(service_dal_fixture, issue_id=get_issue_id(3))
        results = await trends_api.get_cases_by_status(account_id=account_id)
        assert results.start is not None
        assert results.end is not None
        size = (results.end - results.start).days + 1
        check_values(results.close, [0] * size)
        check_values(results.identified, [0] * (size - 2) + [1, 2])
        check_values(results.scanned, [0] * (size - 2) + [1, 3])

        now = datetime.now(UTC)
        results = await trends_api.get_cases_by_status(account_id=account_id, start=now, end=now)
        assert results.close[-1].y == 0
        assert results.close[-1].x == now
        assert results.identified[-1].y == 2
        assert results.identified[-1].x == now
        assert results.scanned[-1].y == 3
        assert results.scanned[-1].x == now

    async def test_get_count_psv(self, service_dal_fixture: ServiceDAL, trends_api: TrendsApi, account_id: str):
        psvs = await create_psvs(service_dal_fixture)
        open_psvs = await trends_api.get_count_psv(account_id)
        assert open_psvs.count == 4

        # change the status of the first psv to done
        await service_dal_fixture.psv_dal.update_psv_status(account_id, psvs[0].id, PsvStatus.DONE)
        done_psvs = await trends_api.get_count_psv(account_id)
        assert done_psvs.count == 3

    async def test_get_mitre_by_view_id(self, service_dal_fixture: ServiceDAL, trends_api: TrendsApi, account_id: str):
        extra = [{"risk_score": risk_score} for risk_score in [10, 40, 80, 100]]
        cases = await create_cases(service_dal_fixture, count=4, extra=extra)
        issue_analysis = await service_dal_fixture.issues_analysis_dal.get(
            cases[3].account_id, cases[3].source_id, cases[3].issue_id
        )
        issue_analysis.confidence = 30
        await service_dal_fixture.session.commit()
        with _basic_mocks():
            results = await trends_api.get_mitre(account_id=account_id)
        expected = {}
        for category in ["Defense Evasion", "Collection", "Exfiltration", "Reconnaissance"]:
            risk_scores = {"None": 0, "monitor": 0, "analyze": 0, "intervene": 2}
            expected[category] = CasesPerCategoryCount(risk_scores=risk_scores)
        assert results.categories == expected
        with _basic_mocks():
            results = await trends_api.get_mitre(account_id=account_id, query_cases_view_id=test_cases_view_id)
        for category in ["Defense Evasion", "Collection", "Exfiltration", "Reconnaissance"]:
            risk_scores = {"None": 0, "monitor": 1, "analyze": 1, "intervene": 1}
            expected[category] = CasesPerCategoryCount(risk_scores=risk_scores)
        assert results.categories == expected

    async def test_get_linddun_by_view_id(
        self, service_dal_fixture: ServiceDAL, trends_api: TrendsApi, account_id: str
    ):
        extra = [{"risk_score": risk_score} for risk_score in [10, 40, 80, 100]]
        cases = await create_cases(service_dal_fixture, count=4, extra=extra, with_summary=False)
        issue_analysis = await service_dal_fixture.issues_analysis_dal.get(
            cases[3].account_id, cases[3].source_id, cases[3].issue_id
        )
        issue_analysis.confidence = 30
        await service_dal_fixture.session.commit()
        with _basic_mocks():
            results = await trends_api.get_linddun(account_id=account_id)
        expected = {}
        for category in ["Disclosure of information", "Identifiability", "Linkability", "Non-compliance"]:
            risk_scores = {"None": 0, "monitor": 0, "analyze": 0, "intervene": 2}
            expected[category] = CasesPerCategoryCount(risk_scores=risk_scores)
        assert results.categories == expected
        with _basic_mocks():
            results = await trends_api.get_linddun(account_id=account_id, query_cases_view_id=test_cases_view_id)
        for category in ["Disclosure of information", "Identifiability", "Linkability", "Non-compliance"]:
            risk_scores = {"None": 0, "monitor": 1, "analyze": 1, "intervene": 1}
            expected[category] = CasesPerCategoryCount(risk_scores=risk_scores)
        assert results.categories == expected
