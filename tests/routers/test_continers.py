from prime_rat_logic_service_client import ContainersApi

from service.db import ServiceDAL
from tests.case_test_utils import create_case
from tests.mock_utils import SOURCE_ID, _basic_mocks
from tests.routers.test_containers_view_filters import _create_cases_and_summary, _update_risk_scores_for_source


class TestContainers:
    def test_get_container_data(self, containers_api: ContainersApi, service_dal_fixture: ServiceDAL, account_id: str):
        pass

    async def test_get_container(self, containers_api: ContainersApi, service_dal_fixture: ServiceDAL, account_id: str):
        with _basic_mocks():
            containers = await _create_cases_and_summary(service_dal_fixture)
            container = await containers_api.get_container(account_id, SOURCE_ID, containers[0].issue_id)
            assert container.issue_id == containers[0].issue_id
            assert container.source_id == SOURCE_ID
            assert container.risk_score == 9  # 3 * 3
            assert container.risk.analyze == 3
            assert container.risk.intervene == 0
            assert container.risk.monitor == 0

    async def test_update_risk_score(
        self, service_dal_fixture: ServiceDAL, containers_api: ContainersApi, account_id: str
    ):
        with _basic_mocks():
            containers = await _create_cases_and_summary(service_dal_fixture)
            container = containers[0]
            await create_case(
                service_dal_fixture,
                issue_id="monitor-issue",
                classification=True,
                with_analysis=True,
                risk_score=20,
                provider_fields={"parent_id": container.issue_id},
            )
            await _update_risk_scores_for_source(service_dal_fixture)
            container = await containers_api.get_container(account_id, SOURCE_ID, containers[0].issue_id)
            assert container.risk_score == 10  # 3 * 3 + 1 * 1
