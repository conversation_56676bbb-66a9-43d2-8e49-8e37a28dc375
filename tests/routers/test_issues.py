import json
from datetime import UTC, datetime, timed<PERSON>ta
from typing import cast

import pytest
from prime_rat_logic_service_client import Issues<PERSON><PERSON>
from prime_rat_logic_service_client.exceptions import ServiceException
from prime_tests import MockResponse

from service.db import ServiceDAL
from tests.case_test_utils import create_cases
from tests.mock_utils import SOURCE_ID, TESTS_ISSUES, _basic_mocks, get_issue_data, get_zip_data


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestIssues:
    async def test_get_cases_keys(self, service_dal_fixture: ServiceDAL, issues_api: IssuesApi, account_id: str):
        extra = [{"classification": sec} for sec in [True, False, True, True, True, False, True, True, True, True]]
        created_cases = await create_cases(service_dal_fixture, extra=extra)
        keys = await issues_api.get_issues_keys(account_id, SOURCE_ID)
        assert keys == [c.issue_id for c in created_cases]

        no_keys = await issues_api.get_issues_keys(account_id, 12232)
        assert no_keys == []
        no_keys = await issues_api.get_issues_keys("no-account", SOURCE_ID)
        assert no_keys == []

        await service_dal_fixture.cases_dal.delete_case(account_id, SOURCE_ID, cast(int, created_cases[0].issue_id))
        keys = await issues_api.get_issues_keys(account_id, SOURCE_ID)
        assert created_cases[0].id not in keys

        now = datetime.now(UTC)
        keys = await issues_api.get_issues_keys(account_id, SOURCE_ID, since=now)
        assert keys == []

        case = created_cases[1]
        analysis = await service_dal_fixture.issues_analysis_dal.get_issue_analysis_by_id(case.issue_analysis_id)
        analysis.created_at = now + timedelta(days=1)
        await service_dal_fixture.session.commit()
        service_dal_fixture.session.expunge_all()
        keys = await issues_api.get_issues_keys(account_id, SOURCE_ID, since=now)
        assert keys == [case.issue_id]

    async def test_get_container_children_data(
        self, service_dal_fixture: ServiceDAL, issues_api: IssuesApi, account_id: str
    ):
        with _basic_mocks() as (file_manager_mocker, _, _):
            expected_sub_issues = ["ISSUE-1", "ISSUE-2", "ISSUE-3", "ISSUE-5", "ISSUE-6"]
            issues_data = [(key, json.dumps(get_issue_data(i + 1))) for i, key in enumerate(TESTS_ISSUES)]
            _files = {}
            for _file_name, _issue_data in issues_data:
                if _file_name in expected_sub_issues:
                    origin_id = f"{_file_name}.json"
                    _files[origin_id] = _issue_data

            file_manager_mocker.put(
                f"/files/{account_id}/data/source/{SOURCE_ID}",
                MockResponse(status_code=200, content=get_zip_data(_files)),
            )
            created_cases = await create_cases(service_dal_fixture)
            case = created_cases[0]
            sub_issues = await issues_api.get_container_children_data(account_id, case.id)
            assert len(sub_issues) == 5
            assert [sub_issue.id for sub_issue in sub_issues] == expected_sub_issues

            assert sub_issues[0].summary == "Issue 1 - lets go"
            assert sub_issues[4].summary == "Lets do Issue 4 & and more"

            with pytest.raises(ServiceException):
                await issues_api.get_container_children_data(account_id, 897000)
