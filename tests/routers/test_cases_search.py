import pytest

from client.prime_rat_logic_service_client.api.cases_api import CasesApi
from service.db import ServiceDAL
from tests.case_test_utils import create_cases
from tests.mock_utils import SOURCE_ID, _basic_mocks


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCasesAutoComplete:
    async def test_search_issue_id(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            search_result = await cases_api.autocomplete_search_global_cases(account_id, "2", 100)
            assert len(search_result) == 1

            assert search_result[0].id >= 1
            assert search_result[0].issue_id == "ISSUE-2"
            assert search_result[0].title == "Issue 2 is here"
            assert search_result[0].issue_type == "Task"
            assert search_result[0].is_container
            assert search_result[0].source_id == SOURCE_ID

    async def test_search_title(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            search_result = await cases_api.autocomplete_search_global_cases(account_id, "Issue 1", 100)
            assert len(search_result) == 2
            assert search_result[0].id >= 1
            assert search_result[0].issue_id == "ISSUE-1"
            assert search_result[1].id >= 1
            assert search_result[1].issue_id == "ISSUE-10"
            assert not search_result[1].is_container
            assert search_result[1].source_id == SOURCE_ID

    async def test_id_for_several_tickets(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            search_result = await cases_api.autocomplete_search_global_cases(account_id, "ISSUE", 100)

            assert search_result[0].id >= 1
            assert search_result[1].id >= 1

            search_result = await cases_api.autocomplete_search_global_cases(account_id, "ISSUE-10", 100)
            assert search_result[0].id >= 1

    async def test_search_title_for_several_tickets(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            search_result = await cases_api.autocomplete_search_global_cases(account_id, "1", 100)
            assert len(search_result) == 2
            assert search_result[0].id >= 1
            assert search_result[0].issue_id == "ISSUE-1"
            assert search_result[1].id >= 1
            assert search_result[1].issue_id == "ISSUE-10"

    async def test_search_with_summary_title(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            await cases_api.get_cases_for_account(account_id)
            search_result = await cases_api.autocomplete_search_global_cases(
                account_id, "Issue 4 Title Lets do Issue 4 & and more", 100
            )
            assert len(search_result) == 1
            assert search_result[0].id >= 1
            assert search_result[0].issue_id == "ISSUE-4"
            assert search_result[0].title == "Issue 4 Title Lets do Issue 4 & and more"
            assert search_result[0].issue_type == "Task"
            assert not search_result[0].is_container
            assert search_result[0].source_id == SOURCE_ID

    async def test_limit(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            await cases_api.get_cases_for_account(account_id)
            search_result = await cases_api.autocomplete_search_global_cases(account_id, "1", 1)
            assert len(search_result) == 1
            assert search_result[0].id >= 1
            assert search_result[0].issue_id == "ISSUE-1"
            assert search_result[0].source_id == SOURCE_ID

    async def test_search_container_no_classification(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            issue_analysis = await service_dal_fixture.issues_analysis_dal.get(account_id, SOURCE_ID, "ISSUE-1")
            issue_analysis.classification = False
            await service_dal_fixture.session.commit()

            search_result = await cases_api.autocomplete_search_global_cases(account_id, "Issue 1 - lets go", 10)
            assert len(search_result) == 1
            assert search_result[0].id >= 1
            assert search_result[0].issue_id == "ISSUE-1"
            assert search_result[0].title == "Issue 1 - lets go"
            assert search_result[0].issue_type == "Epic"
            assert search_result[0].is_container
            assert search_result[0].source_id == SOURCE_ID
