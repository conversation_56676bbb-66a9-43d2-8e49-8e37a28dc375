import pytest
from prime_rat_logic_service_client import CasesApi

from client.prime_rat_logic_service_client.models.concern_type import ConcernType
from service.db import ServiceDAL
from service.models.filters_and_sort import Filter, Operator
from tests.case_test_utils import create_cases, modify_concern_category
from tests.mock_utils import TOTAL_TESTS_ISSUES, _basic_mocks


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCasesMethodology:
    async def test_filter_by_linddun_category_equal_operator(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            created_cases = await create_cases(service_dal_fixture)
            issues_ids = [case.issue_analysis_id for case in created_cases]
            await modify_concern_category(service_dal_fixture.session, issues_ids)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="linddun_categories", value=["Non-compliance"], op=Operator.EQ).as_json_str(),
                ],
            )

            assert len(account_cases.results) == 5

    async def test_filter_by_linddun_category_equal_operator_multiple_values(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture)

            await modify_concern_category(
                service_dal_fixture.session,
                issue_analysis_ids=[cases[0].issue_analysis_id],
                concern_type=ConcernType.LINDDUN,
                category="Disclosure of information",
            )
            await modify_concern_category(
                service_dal_fixture.session,
                issue_analysis_ids=[cases[1].issue_analysis_id],
                concern_type=ConcernType.LINDDUN,
                category="Non-repudiation",
            )

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="linddun_categories", value=["Non-compliance", "Non-repudiation"], op=Operator.EQ
                    ).as_json_str(),
                ],
            )
            assert (
                len(account_cases.results) == TOTAL_TESTS_ISSUES - 1
            )  # One of the cases has changed to Disclosure of information

    async def test_filter_by_linddun_category_equal_operator_value_not_exist(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=["value not exist"], op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 0

    async def test_filter_by_linddun_category_not_equal_operator(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="linddun_categories", value=["Linkability"], op=Operator.NE).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 0

    async def test_filter_by_linddun_category_not_equal_operator_value_not_exist(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=["value not exists"], op=Operator.NE).as_json_str(),
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES

    async def test_filter_by_linddun_category_equal_not_operator_value_empty(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=[""], op=Operator.NE).as_json_str(),
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES

    async def test_filter_by_linddun_category_equal_operator_value_empty(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=[""], op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 0

    async def test_filter_by_mitre_category_equal_operator(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture)

            await modify_concern_category(
                service_dal_fixture.session,
                issue_analysis_ids=[cases[0].issue_analysis_id],
                concern_type=ConcernType.MITRE,
                category="TA0042",
            )

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=["Reconnaissance"], op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES - 1

    async def test_filter_by_mitre_category_equal_operator_multiple_values(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture)

            # Currently all the tickets contains the same categories - modify the concern category for some of the cases
            await modify_concern_category(
                service_dal_fixture.session,
                issue_analysis_ids=[cases[0].issue_analysis_id],
                concern_type=ConcernType.MITRE,
                category="TA0042",
            )
            await modify_concern_category(
                service_dal_fixture.session,
                issue_analysis_ids=[cases[1].issue_analysis_id],
                concern_type=ConcernType.MITRE,
                category="TA0003",
            )

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="mitre_categories",
                        value=["Reconnaissance", "Resource Development"],
                        op=Operator.EQ,  # Resource Development is TA0042
                    ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES - 1

    async def test_filter_by_mitre_category_equal_operator_multiple_values_with_not_exists_value(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="mitre_categories", value=["Reconnaissance", "Not exists"], op=Operator.EQ
                    ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES

    async def test_filter_by_mitre_category_equal_operator_value_not_exist(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=["not exists tactic"], op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 0

    async def test_filter_by_mitre_category_not_equal_operator(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture)

            await modify_concern_category(
                service_dal_fixture.session,
                issue_analysis_ids=[cases[0].issue_analysis_id],
                concern_type=ConcernType.MITRE,
                category="TA0042",
            )

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=["Reconnaissance"], op=Operator.NE).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 1

    async def test_filter_by_mitre_category_not_equal_operator_value_not_exist(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(
                service_dal_fixture,
            )

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=["Some not exsits tactic"], op=Operator.NE).as_json_str(),
                ],
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES

    async def test_filter_by_mitre_category_equal_operator_value_empty(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(field="mitre_categories", value=[""], op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 0

    async def test_filter_by_mitre_category_not_equal_operator_value_empty(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id, f=[Filter(field="mitre_categories", value=[""], op=Operator.NE).as_json_str()]
            )
            assert len(account_cases.results) == TOTAL_TESTS_ISSUES
