from datetime import UTC, datetime

import pytest
from prime_rat_logic_service_client import (
    CaseAuditAction,
    CasesApi,
    CaseStatus,
    ConfidenceScoreLevel,
    ExternalCase,
    RiskScoreCategory,
)
from prime_shared.common_values import USER_ID_HEADER_NAME, USER_IP_HEADER_NAME

from service.db import <PERSON>DA<PERSON>
from service.models import CaseAuditUpdateStatusArgs
from service.models.filters_and_sort import Filter, Operator, SortField
from service.models.filters_and_sort.sorting import SortDirection
from tests.mock_utils import (
    FAKE_PROVIDER_FIELDS_DATA,
    FAKE_PROVIDER_FIELDS_INFO,
    JIRA_HOST,
    SOURCE_ID,
    TESTS_ISSUES,
    TOTAL_TESTS_ISSUES,
    _basic_mocks,
)

from ..case_test_utils import create_case, create_cases, get_external_case_summary
from .test_get_cases_filter import _validate_cases


def _set_order(cases: list[ExternalCase], order: list[int]) -> list[ExternalCase]:
    return [cases[o] for o in order]


async def _verify_case(cases_api: CasesApi, account_id: str, risk_category: RiskScoreCategory, expected_case):
    f = Filter(field="risk_score_category", value=risk_category, op=Operator.EQ)
    response = await cases_api.get_cases_for_account(account_id, f=[f.as_json_str()])
    assert response.results is not None and len(response.results) == 1
    assert expected_case.model_dump() == response.results[0].model_dump()


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCases:
    async def test_get_case_by_id(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            assert len((await cases_api.get_cases_for_account_and_source(account_id, SOURCE_ID)).results) == 0
            assert len((await cases_api.get_cases_for_account(account_id)).results) == 0

            created_case = (await create_cases(service_dal_fixture, count=1))[0]

            case = await cases_api.get_case(account_id, SOURCE_ID, created_case.issue_id)

            assert case.issue_id == created_case.issue_id
            assert case.provider_fields

    async def test_get_cases(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            assert len((await cases_api.get_cases_for_account_and_source(account_id, SOURCE_ID)).results) == 0
            assert len((await cases_api.get_cases_for_account(account_id)).results) == 0

            created_cases = await create_cases(service_dal_fixture, count=2)

            account_cases = await cases_api.get_cases_for_account(account_id)
            assert len(account_cases.results) == 2
            for idx in range(len(account_cases.results)):
                expected_case = await get_external_case_summary(created_cases[idx], service_dal_fixture)
                assert expected_case.model_dump() == account_cases.results[idx].model_dump()

            # test we only get cases without security
            case = await create_case(service_dal_fixture, TESTS_ISSUES[2], classification=False)
            _filter = [Filter(field="classification", value=["True"], op=Operator.EQ).as_json_str()]
            secure_cases = await cases_api.get_cases_for_account(account_id, f=_filter)
            assert account_cases == secure_cases
            case.deleted_at = datetime.now(UTC)
            await service_dal_fixture.session.commit()

    async def test_get_cases_from_source(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        await create_cases(service_dal_fixture, count=2)
        source_id_b = SOURCE_ID + 1
        await create_cases(service_dal_fixture, source_id=source_id_b, count=10)

        source_a_cases = await cases_api.get_cases_for_account_and_source(account_id, SOURCE_ID)
        assert source_a_cases.size == 2

        source_b_cases = await cases_api.get_cases_for_account_and_source(account_id, source_id_b)
        assert source_b_cases.size == 10

    async def test_get_cases_sort(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        cases_count = 10
        extra = [{"confidence": i + 1} for i in range(cases_count)]
        cases = await create_cases(service_dal_fixture, count=cases_count, extra=extra)

        with _basic_mocks():
            expected_cases = [await get_external_case_summary(case, service_dal_fixture) for case in cases]
            await _validate_cases(
                _set_order(expected_cases, [0, 9, 1, 2, 3, 4, 5, 6, 7, 8]),
                cases_api,
                s=[SortField(field="issue_id", direction=SortDirection.ASC).as_url_str()],
            )
            await _validate_cases(
                _set_order(expected_cases, [8, 7, 6, 5, 4, 3, 2, 1, 9, 0]),
                cases_api,
                s=[SortField(field="issue_id", direction=SortDirection.DESC).as_url_str()],
            )

            for case, source_id in zip(cases, [1, 1, 2, 2], strict=False):
                case.source_id = source_id
            await service_dal_fixture.session.commit()

            cases2 = await service_dal_fixture.cases_dal.get_cases_by(account_id)
            expected_case2 = [await get_external_case_summary(case, service_dal_fixture) for case in cases2]
            s = [
                SortField(field="junk", direction=SortDirection.DESC).as_url_str(),
                SortField(field="source_id", direction=SortDirection.ASC).as_url_str(),
                SortField(field="issue_id", direction=SortDirection.DESC).as_url_str(),
            ]

            await _validate_cases(_set_order(expected_case2, [1, 0, 3, 2, 8, 7, 6, 5, 4, 9]), cases_api, s=s)
            s = [
                SortField(field="junk", direction=SortDirection.DESC).as_url_str(),
                SortField(field="source_id", direction=SortDirection.DESC).as_url_str(),
                SortField(field="issue_id", direction=SortDirection.DESC).as_url_str(),
            ]
            await _validate_cases(_set_order(expected_case2, [8, 7, 6, 5, 4, 9, 3, 2, 1, 0]), cases_api, s=s)

    async def test_get_cases_sort_issue_analysis(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        confidence = [i + 1 for i in range(TOTAL_TESTS_ISSUES)]
        extra = [{"confidence": c} for c in confidence]
        cases = await create_cases(service_dal_fixture, count=TOTAL_TESTS_ISSUES, extra=extra)
        actual_order = [(case.issue_id, conf) for case, conf in zip(cases, confidence, strict=False)]
        with _basic_mocks():
            response = await cases_api.get_cases_for_account(
                account_id,
                s=[SortField(field="confidence", direction=SortDirection.DESC).as_url_str()],
            )
        actual_confidence = [(r.issue_id, r.issue_analysis.confidence) for r in response.results]
        assert actual_confidence == sorted(actual_order, key=lambda x: x[1], reverse=True)
        with _basic_mocks():
            response = await cases_api.get_cases_for_account(
                account_id,
                s=[SortField(field="confidence", direction=SortDirection.ASC).as_url_str()],
            )
        actual_confidence = [(r.issue_id, r.issue_analysis.confidence) for r in response.results]
        assert actual_confidence == sorted(actual_order, key=lambda x: x[1], reverse=False)

    async def test_get_single_case(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            await create_case(
                service_dal_fixture,
                TESTS_ISSUES[0],
                classification=True,
                with_analysis=True,
                provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
                with_summary=False,
            )
            headers = {USER_ID_HEADER_NAME: "moshe", USER_IP_HEADER_NAME: "*******"}
            await cases_api.update_status(
                account_id,
                SOURCE_ID,
                TESTS_ISSUES[0],
                CaseStatus.DISMISSED,
                _headers=headers,
            )

            case = await cases_api.get_case(account_id, source_id=SOURCE_ID, issue_id=TESTS_ISSUES[0], _headers=headers)

            assert case.link == f"{JIRA_HOST}/browse/ISSUE-1"
            assert len(case.history) == 1

            assert case.history[0].audit_action == CaseAuditAction.UPDATE_STATUS
            expected = CaseAuditUpdateStatusArgs(old_status=CaseStatus.OPEN, new_status=CaseStatus.DISMISSED)
            assert case.history[0].audit_action_args == expected.model_dump()
            assert case.history[0].user == "moshe"

            await create_case(
                service_dal_fixture,
                TESTS_ISSUES[1],
                classification=False,
                with_analysis=True,
                provider_fields=FAKE_PROVIDER_FIELDS_DATA[1],
            )
            case = await cases_api.get_case(account_id, source_id=SOURCE_ID, issue_id=TESTS_ISSUES[1], _headers=headers)
            assert case.issue_analysis.confidence_level == ConfidenceScoreLevel.LOW
            assert case.link == f"{JIRA_HOST}/browse/ISSUE-2"
            assert case.provider_fields["id"] == "ISSUE-2"

    async def test_get_cases_pagination(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        await create_cases(service_dal_fixture, count=3)

        with _basic_mocks():
            response = await cases_api.get_cases_for_account(
                account_id,
            )
            assert response.size == response.total == len(response.results) == 3
            assert response.has_next is False

            response = await cases_api.get_cases_for_account(
                account_id,
                limit=1,
            )
            assert response.size == len(response.results) == 1
            assert response.total == 3
            assert response.has_next is True

            response = await cases_api.get_cases_for_account(
                account_id,
                limit=1,
                offset=1,
            )
            assert response.size == len(response.results) == 1
            assert response.total == 3
            assert response.has_next is True

            response = await cases_api.get_cases_for_account(
                account_id,
                limit=2,
                offset=0,
            )
            assert response.size == len(response.results) == 2
            assert response.total == 3
            assert response.has_next is True

            response = await cases_api.get_cases_for_account(
                account_id,
                limit=2,
                offset=2,
            )
            assert response.size == len(response.results) == 1
            assert response.total == 3
            assert response.has_next is False

            response = await cases_api.get_cases_for_account(
                account_id,
                limit=200,
                offset=2,
            )
            assert response.size == len(response.results) == 1
            assert response.total == 3
            assert response.has_next is False

    async def test_get_autocomplete(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks(selected_jira_fields=FAKE_PROVIDER_FIELDS_INFO.keys()):
            await create_cases(
                service_dal_fixture,
            )
            result = await cases_api.autocomplete(account_id, "provider_fields.field_1", "Never")
            assert result == []
            result = await cases_api.autocomplete(account_id, "provider_fields.creator", "jane")
            assert set(result) == {"Jane Smith the 4th", "Jane Doe the 55nd", "Jane Doe the 2nd"}
            result = await cases_api.autocomplete(account_id, "provider_fields.labels", "label1")
            assert set(result) == {"label1", "label10"}
            result = await cases_api.autocomplete(account_id, "provider_fields.labels", "label")
            assert set(result) == {"label1", "label2", "label3", "label7", "label10"}
            result = await cases_api.autocomplete(account_id, "provider_fields.field_6", "option")
            assert set(result) == {"option1", "option2", "option4"}

    async def test_get_cases_parents(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(account_id)
            assert len(account_cases.results) == 10

            assert account_cases.results[0].parents == []
            assert account_cases.results[1].parents == ["ISSUE-1"]
            assert account_cases.results[2].parents == ["ISSUE-2", "ISSUE-1"]
            assert account_cases.results[3].parents == []
            assert account_cases.results[4].parents == ["ISSUE-1"]
            assert account_cases.results[5].parents == ["ISSUE-1"]
            assert account_cases.results[6].parents == []
            assert account_cases.results[7].parents == []
            assert account_cases.results[8].parents == ["ISSUE-8"]
            assert account_cases.results[9].parents == ["ISSUE-8"]
