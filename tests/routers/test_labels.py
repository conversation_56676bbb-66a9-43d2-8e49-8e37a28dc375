import pytest
from prime_rat_logic_service_client import A<PERSON><PERSON>x<PERSON> as RatLogicApiException
from prime_rat_logic_service_client import CasesApi
from pydantic import ValidationError
from sqlalchemy import text

from service.db import ServiceDAL
from tests.case_test_utils import create_case, create_cases
from tests.mock_utils import FAKE_PROVIDER_FIELDS_DATA, SOURCE_ID, TESTS_ISSUES, _basic_mocks


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestLabel:
    @pytest.mark.parametrize(
        "labels",
        [
            [],
            ["YveQB5ICBcFvBK{8#Aq-6FWp7(>kfy^%Y;fq}MHb6$Jf/9QxsJ].p;NP4H5\\t#"],
            # [''.join(random.choices(string.printable, k=i)) for i in range(1, 100)]
        ],
    )
    async def test_set_labels(
        self, labels: list[str], service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        assert len(await cases_api.get_labels(account_id)) == 0
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        await cases_api.set_labels(case.account_id, case.source_id, case.issue_id, labels)
        with _basic_mocks():
            case = await cases_api.get_case(case.account_id, case.source_id, case.issue_id)
            assert case.labels == labels
        assert await cases_api.get_labels(account_id) == labels

    async def test_set_labels_with_dup(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        label = "label1"
        assert len(await cases_api.get_labels(account_id)) == 0
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        await cases_api.set_labels(case.account_id, case.source_id, case.issue_id, [label] * 100)
        labels = await cases_api.get_labels(account_id)
        assert len(labels) == 1
        assert labels[0] == label
        with _basic_mocks():
            case = await cases_api.get_case(case.account_id, case.source_id, case.issue_id)
            assert case.labels == [label]

    async def test_set_labels_override(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        assert len(await cases_api.get_labels(account_id)) == 0
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        labels_count = 0
        for label in [["label"], [], ["label1", "label2"]]:
            labels_count += len(label)
            await cases_api.set_labels(case.account_id, case.source_id, case.issue_id, label)
            response = await cases_api.get_labels(account_id)
            assert len(response) == labels_count
            with _basic_mocks():
                case = await cases_api.get_case(case.account_id, case.source_id, case.issue_id)
                assert case.labels == label

    async def test_set_labels_label_already_exist(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        label = "label1"
        assert len(await cases_api.get_labels(account_id)) == 0
        await service_dal_fixture.session.exec(
            text(f"INSERT INTO labels (account_id, name) VALUES ('{account_id}', '{label}')")
        )
        assert len(await service_dal_fixture.labels_dal.get_labels(account_id)) == 1
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        await cases_api.set_labels(case.account_id, case.source_id, case.issue_id, [label])
        response = await cases_api.get_labels(account_id)
        assert len(response) == 1
        with _basic_mocks():
            case = await cases_api.get_case(case.account_id, case.source_id, case.issue_id)
            assert case.labels == [label]

    async def test_set_labels_multiple_accounts(self, service_dal_fixture: ServiceDAL, cases_api, account_id: str):
        account2 = account_id + "2"
        label = "label1"
        cases = {}
        for account in [account_id, account2]:
            response = await service_dal_fixture.labels_dal.get_labels(account)
            assert len(response) == 0
            case = await create_case(
                service_dal_fixture,
                TESTS_ISSUES[0],
                account_id=account,
                provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
            )
            await cases_api.set_labels(case.account_id, case.source_id, case.issue_id, [label])
            cases[account] = case
        for account in [account_id, account2]:
            response = await cases_api.get_labels(account)
            assert len(response) == 1
            with _basic_mocks(account_id=account):
                case = cases[account]
                response = await cases_api.get_case(case.account_id, case.source_id, case.issue_id)
                assert response.labels == [label]

    async def test_set_labels_multiple_cases(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        label = "label1"
        cases = await create_cases(service_dal_fixture)
        await service_dal_fixture.set_labels(cases[0].account_id, cases[0].source_id, cases[0].issue_id, [label])
        await service_dal_fixture.set_labels(cases[1].account_id, cases[1].source_id, cases[1].issue_id, [label])
        response = await cases_api.get_labels(account_id)
        assert len(response) == 1
        with _basic_mocks():
            cases = await cases_api.get_cases_for_account(
                account_id,
            )
            assert len(cases.results) == 10
            assert cases.results[0].labels == cases.results[1].labels == [label]

    @pytest.mark.parametrize("label", ["", " ", "\n", "\n\b]", "\t", "\t\b", "\t\b\n", "\t\b\n "])
    async def test_set_labels_not_valid_labels(self, label: str, cases_api: CasesApi, account_id: str):
        with pytest.raises((RatLogicApiException, ValidationError)) as e:
            await cases_api.set_labels(account_id, SOURCE_ID, "issue_id", [label])
        if e.type == RatLogicApiException:
            assert "string_pattern_mismatch" in e.value.body
