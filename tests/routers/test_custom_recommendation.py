import contextlib
import datetime

import pytest
from prime_config_service_client import AccountConfig, CustomRecommendationOutput
from prime_rat_logic_service_client import CasesA<PERSON>
from prime_shared.common_dataclasses import SecurityFramework
from prime_tests import MockResponse

from client.prime_rat_logic_service_client.models.user_implementation_status import UserImple<PERSON><PERSON>tatus
from service.db import CaseTable, ServiceDAL
from service.models import ExternalCase, ImplementationStatus
from tests.case_test_utils import TEST_RECOMMENDATIONS, add_recommendation_to_case, create_cases
from tests.mock_utils import ACCOUNT_ID_CONTEXT, SOURCE_ID, _basic_mocks
from tests.routers.test_cases import ImplementationNotFound, _get_implementation_by_id, _test_implementation_status

CUSTOM_RECOMMENDATION_ID = 5000


@contextlib.contextmanager
def _custom_recommendation_mock(case: CaseTable):
    now = datetime.datetime.now(datetime.UTC)
    custom_recommendations = []
    for i, test_rec in enumerate(TEST_RECOMMENDATIONS[:4]):
        custom_recommendations.append(
            CustomRecommendationOutput(
                id=CUSTOM_RECOMMENDATION_ID + i,
                framework=SecurityFramework.NIST,
                recommendation=f"custom recommendation {i}",
                control_id=test_rec.control_id,
                issue_id=case.issue_id,
                source_id=SOURCE_ID,
                created_by="test",
                updated_by="test",
                updated_at=now,
                created_at=now,
            )
        )
    with _basic_mocks() as (_, _, config_mocker):
        config_data = AccountConfig(
            company_description="", providers_attributes={}, custom_recommendations=custom_recommendations
        )
        config_mocker.get(f"config/{ACCOUNT_ID_CONTEXT.get()}", MockResponse(config_data.model_dump()))
        yield custom_recommendations


def _get_custom_recommendation(case: ExternalCase):
    for concern in case.concerns:
        for control in concern.controls:
            for rec in control.recommendations:
                if "custom" in rec.raci:
                    yield rec


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestCustomRecommendation:
    async def test_update_custom_recommendation(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        case = (await create_cases(service_dal_fixture, count=1))[0]
        recommendations = await add_recommendation_to_case(service_dal_fixture, case)
        concern_id = recommendations[0].concern_id
        control_id = recommendations[0].control_id
        with _custom_recommendation_mock(case) as custom_recommendations:
            recommendation = custom_recommendations[0]
            await _test_implementation_status(
                cases_api,
                case,
                recommendation.id,
                service_dal_fixture,
                ImplementationStatus.APPROVED,
                UserImplementationStatus.APPROVED,
                concern_id,
                control_id,
            )
            await _test_implementation_status(
                cases_api,
                case,
                recommendation.id,
                service_dal_fixture,
                None,
                UserImplementationStatus.DISMISSED,
                concern_id,
                control_id,
            )
            with pytest.raises(ImplementationNotFound):
                external_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
                _get_implementation_by_id(external_case, recommendation.id)
