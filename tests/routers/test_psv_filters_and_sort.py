import pytest
from prime_rat_logic_service_client import PsvApi

from service.db import ServiceDAL
from service.models.filters_and_sort import Filter, Operator, SortDirection, SortField
from tests.mock_utils import (
    _basic_mocks,
    get_issue_id,
)
from tests.psv_utils import create_psv, create_psvs


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestPSVFilterAndSort:
    async def test_psv_filter_project(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            await create_psvs(service_dal_fixture)
            _filter = [Filter(field="provider_fields.project", op=Operator.EQ, value="test_project_id").as_json_str()]
            results = await psv_api.get_psv(account_id, f=_filter)
            assert results.total == 3
            assert all(psv.project == "test_project_id" for psv in results.results)

    async def test_psv_filter_type(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            type1 = "test_type1"
            type2 = "test_type2"
            await create_psvs(service_dal_fixture, count=3, psv_type=type1)
            await create_psv(service_dal_fixture, issue_id=get_issue_id(4), psv_type=type2)

            _filter = [Filter(field="type", op=Operator.EQ, value=type1).as_json_str()]
            results = await psv_api.get_psv(account_id, f=_filter)
            assert results.total == 3
            assert all(psv.type == type1 for psv in results.results)

            _filter = [Filter(field="type", op=Operator.EQ, value=type2).as_json_str()]
            results = await psv_api.get_psv(account_id, f=_filter)
            assert results.total == 1
            assert all(psv.type == type2 for psv in results.results)

            _filter = [Filter(field="type", op=Operator.EQ, value=[type1, type2]).as_json_str()]
            results = await psv_api.get_psv(account_id, f=_filter)
            assert results.total == 4
            assert all(psv.type in (type1, type2) for psv in results.results)

    async def test_psv_filter_reporter(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            await create_psvs(service_dal_fixture)
            _filter = [Filter(field="provider_fields.reporter", op=Operator.EQ, value="Matan Markovics").as_json_str()]
            results = await psv_api.get_psv(account_id, f=_filter)
            assert results.total == 3
            assert all(psv.reporter == "Matan Markovics" for psv in results.results)

    async def test_psv_sort_created_at(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            await create_psvs(service_dal_fixture)
            _sort = [SortField(field="created_at", direction=SortDirection.DESC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.created_at, reverse=True) == results.results
            _sort = [SortField(field="created_at", direction=SortDirection.ASC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.created_at) == results.results

    async def test_psv_sort_detected(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            await create_psvs(service_dal_fixture)
            _sort = [SortField(field="detection_date", direction=SortDirection.DESC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.created_at, reverse=True) == results.results
            _sort = [SortField(field="detection_date", direction=SortDirection.ASC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.created_at) == results.results

    async def test_psv_sort_project(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            await create_psvs(service_dal_fixture)
            _sort = [SortField(field="provider_fields.project", direction=SortDirection.DESC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.project, reverse=True) == results.results
            _sort = [SortField(field="provider_fields.project", direction=SortDirection.ASC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.project) == results.results

    async def test_psv_sort_title(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            await create_psvs(service_dal_fixture)
            _sort = [SortField(field="title", direction=SortDirection.DESC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.title, reverse=True) == results.results
            _sort = [SortField(field="title", direction=SortDirection.ASC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.title) == results.results

    async def test_psv_sort_issue_id(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            await create_psvs(service_dal_fixture)
            _sort = [SortField(field="issue_id", direction=SortDirection.DESC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.issue_id, reverse=True) == results.results
            _sort = [SortField(field="issue_id", direction=SortDirection.ASC).as_json_str()]
            results = await psv_api.get_psv(account_id, s=_sort)
            assert sorted(results.results, key=lambda x: x.issue_id) == results.results
