import pytest
from prime_rat_logic_service_client import (
    ContainersApi,
    LlmContextApi,
)
from sqlmodel import update

from service.db import ServiceDAL
from service.db.tables.issues_analysis import IssueAnalysisTable
from service.logic.issues_graph.generator import GraphGenerator

from .test_containers_view_filters import _create_cases_and_summary


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestLLMContextRoute:
    async def test_get_case_by_container_id(
        self,
        service_dal_fixture: ServiceDAL,
        containers_api: ContainersApi,
        llm_context_api: LlmContextApi,
        account_id: str,
    ):
        containers = await _create_cases_and_summary(service_dal_fixture)
        cases_1_3 = [case for case in containers if case.issue_id.split("-")[-1] in ["1", "3"]]
        await self._update_issue_analysis(service_dal_fixture, cases_1_3[0].issue_analysis_id, False)
        await self._update_issue_analysis(service_dal_fixture, cases_1_3[1].issue_analysis_id, False)
        # verify cross account context
        not_mine_containers = await _create_cases_and_summary(service_dal_fixture, account_id="moshe-test-account")  # noqa: F841
        container = await containers_api.get_container(account_id, containers[0].source_id, containers[0].issue_id)
        result = await llm_context_api.cases(account_id, container_id=container.id)

        assert result.size == 5
        issue_ids = [case.issue_id.split("-")[-1] for case in result.results]
        assert issue_ids == ["2", "5", "6", "1", "3"]  # verify sort according to classification,provider_fields.updated

        tree_generator = GraphGenerator(service_dal_fixture, account_id, container.source_id)
        tree = await tree_generator.load_from_db(container.issue_id)
        node = tree.get(container.issue_id)
        descendants = tree.get_all_descendants(node["name"])
        assert len(descendants) == 4
        all_case_ids = sorted(case.issue_id for case in result.results)
        assert all_case_ids == sorted(case.issue_id for case in result.results)

    async def _update_issue_analysis(
        self, service_dal_fixture: ServiceDAL, issue_analysis_id: int, classification: bool
    ) -> None:
        sql_query = (
            update(IssueAnalysisTable)
            .where(IssueAnalysisTable.id == issue_analysis_id)
            .values(classification=classification)
        )
        await service_dal_fixture.session.exec(sql_query)
        await service_dal_fixture.session.commit()
