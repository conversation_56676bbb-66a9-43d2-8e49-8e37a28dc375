import pytest
from prime_rat_logic_service_client import JobClassification<PERSON><PERSON><PERSON>rgs, Jobs<PERSON><PERSON>

from service.db import ServiceDA<PERSON>
from service.job_type import JobType
from service.k8s_jobs.classification_job.models import ClassificationJobArgs
from tests.mock_utils import SOURCE_ID

from ...fixtures import TEST_CREATED_BY
from .jobs_test_utils import generic_job_validation, get_next_job_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestClassificationJob:
    async def test_classification_job_run(self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, account_id: str):
        create_arg = JobClassificationCreateArgs(
            job=JobType.CLASSIFICATION, source_id=SOURCE_ID, force=False, created_by=TEST_CREATED_BY
        )
        job_id = await get_next_job_id(service_dal_fixture)
        expected_job_args = ClassificationJobArgs(
            account_id=account_id, source_id=SOURCE_ID, force=False, job_id=job_id, parent_id=None
        )
        await generic_job_validation(jobs_api, JobType.CLASSIFICATION, create_arg, expected_job_args)

        create_arg = JobClassificationCreateArgs(
            job=JobType.CLASSIFICATION, source_id=SOURCE_ID + 1, force=True, created_by=TEST_CREATED_BY
        )
        expected_job_args = ClassificationJobArgs(
            account_id=account_id, source_id=SOURCE_ID + 1, force=True, job_id=job_id + 1, parent_id=None
        )
        await generic_job_validation(jobs_api, JobType.CLASSIFICATION, create_arg, expected_job_args)
