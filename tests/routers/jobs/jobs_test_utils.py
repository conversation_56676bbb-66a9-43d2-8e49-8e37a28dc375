from contextlib import asynccontextmanager, contextmanager
from unittest.mock import patch

from kubernetes.client import V1<PERSON><PERSON>, V1ObjectMeta
from prime_jobs import Job, JobStatus, ServiceInfo
from prime_rat_logic_service_client import JobCreateArgs, JobCreatedResponse, JobsApi
from prime_utils.helm_and_kubectl import deploy_helm
from sqlmodel import select

from service.config import get_config
from service.db import ServiceDAL
from service.job_type import JobType
from service.k8s_jobs.job_spawners import (
    JobConfig,
    RatJobInfo,
    RatJobSpawnerBase,
    RatJobSpawnerFactory,
    job_info_types,
)
from tests.mock_utils import ACCOUNT_ID_CONTEXT


async def get_next_job_id(service_dal: ServiceDAL) -> int:
    last_job = (await service_dal.session.exec(select(Job).order_by(Job.id.desc()).limit(1))).one_or_none()
    return last_job.id + 1 if last_job else 1


@contextmanager
def mock_deploy_helm(job_name: str | None = None):
    job_name = job_name or "job_name"
    ret_value = [V1Job(api_version="batch/v1", kind="Job", metadata=V1ObjectMeta(name=job_name, namespace="default"))]
    with patch("prime_jobs.job_spawner.deploy_helm") as deploy_helm_mock:
        deploy_helm_mock.return_value = ret_value
        yield deploy_helm_mock


async def _get_job_args(spawner_obj: RatJobSpawnerBase, expected_job_args: job_info_types):
    job_id = expected_job_args.job_id
    job_name = spawner_obj.get_job_name(job_id)
    job_type = f"{spawner_obj.NAME}-job"
    expected_job_config = JobConfig(
        job_env=spawner_obj._convert_to_helm_job_env(expected_job_args),
        job_info=RatJobInfo(
            image=get_config().jobs_image_url,
            name=job_name,
            id=job_id,
            job_type=job_type,
            module_path=f"service.k8s_jobs.{job_type.replace('-', '_')}.main",
        ),
        service=ServiceInfo(name=get_config().service_name),
        datadog_enabled=get_config().datadog_enabled,
    )
    return job_name, expected_job_config


# TODO: can we remove the need for passing the spawner object?
@asynccontextmanager
async def mock_job_creation():
    with mock_deploy_helm() as deploy_helm_mock:
        yield deploy_helm_mock


async def validate_job_creation(spawner: RatJobSpawnerBase, expected_job_args: job_info_types, deploy_helm_mock):
    job_name, expected_job_config = await _get_job_args(spawner, expected_job_args)
    deploy_helm_mock.assert_called_with(
        job_name, spawner.job_path(), expected_job_config.model_dump(mode="json", serialize_as_any=True)
    )


async def generic_job_validation(
    jobs_api: JobsApi, job_type: JobType, create_arg: JobCreateArgs, expected_job_args: job_info_types
) -> JobCreatedResponse:
    account_id = ACCOUNT_ID_CONTEXT.get()
    spawner = RatJobSpawnerFactory.JOB_SPAWNERS[job_type]
    spawner_args = create_arg.to_dict()
    spawner_args.pop("job")
    spawner_args.pop("created_by")
    spawner_obj = spawner(account_id=account_id, **spawner_args)
    async with mock_job_creation() as deploy_helm_mock:
        resp = await jobs_api.add_job(account_id=account_id, job_create_args=JobCreateArgs(actual_instance=create_arg))
    await validate_job_creation(spawner_obj, expected_job_args, deploy_helm_mock)
    assert resp.job_id
    assert resp.status == JobStatus.PENDING
    # make sure the template is ok
    job_name, expected_job_config = await _get_job_args(spawner_obj, expected_job_args)
    with patch("prime_utils.helm_and_kubectl.run_kubectl_apply"):
        deploy_helm(job_name, spawner.job_path(), expected_job_config.model_dump(mode="json", serialize_as_any=True))
    return resp
