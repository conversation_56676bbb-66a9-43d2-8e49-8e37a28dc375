from unittest.mock import patch

import pytest
from kubernetes.client import V1<PERSON><PERSON>, V1ObjectMeta
from prime_rat_logic_service_client import (
    JobClassificationCreateArgs,
    JobCreateArgs,
    JobsApi,
    JobStatus,
    JobType,
    JobUpdateIssuesCreateArgs,
)

from service.db import <PERSON>DAL
from tests.fixtures import TEST_CREATED_BY
from tests.mock_utils import ACCOUNT_ID_CONTEXT, SOURCE_ID


async def _create_jobs(jobs_api: JobsA<PERSON>):
    ret_value = [V1Job(api_version="batch/v1", kind="Job", metadata=V1ObjectMeta(name="test-job", namespace="default"))]
    with patch("prime_jobs.job_spawner.deploy_helm") as deploy_helm_mock:
        deploy_helm_mock.return_value = ret_value
        create_arg = JobClassificationCreateArgs(
            job=JobType.CLASSIFICATION, source_id=SOURCE_ID, force=False, created_by=TEST_CREATED_BY
        )
        job1 = await jobs_api.add_job(
            ACCOUNT_ID_CONTEXT.get(), job_create_args=JobCreateArgs(actual_instance=create_arg)
        )
        create_arg = JobUpdateIssuesCreateArgs(
            job=JobType.UPDATE_ISSUES, created_by=TEST_CREATED_BY, source_id=SOURCE_ID
        )
        job2 = await jobs_api.add_job(
            ACCOUNT_ID_CONTEXT.get(), job_create_args=JobCreateArgs(actual_instance=create_arg)
        )
        return [job1, job2]


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestJobsApi:
    async def test_jobs(self, service_dal_fixture: ServiceDAL, jobs_api: JobsApi, account_id: str):
        jobs = await jobs_api.get_jobs(account_id=account_id)
        assert len(jobs) == 0

        await _create_jobs(jobs_api)

        jobs = await jobs_api.get_jobs(account_id=account_id)
        assert len(jobs) == 2

        jobs = await jobs_api.get_jobs(account_id=account_id, source_id=SOURCE_ID)
        assert len(jobs) == 2

        jobs = await jobs_api.get_jobs(account_id=account_id, job_status=[JobStatus.PENDING])
        assert len(jobs) == 2

        jobs = await jobs_api.get_jobs(account_id=account_id, source_id=SOURCE_ID, job_status=[JobStatus.COMPLETED])
        assert len(jobs) == 0

        jobs = await jobs_api.get_jobs(account_id=account_id + "1")
        assert len(jobs) == 0

        jobs = await jobs_api.get_jobs(account_id=account_id, source_id=SOURCE_ID + 1)
        assert len(jobs) == 0
