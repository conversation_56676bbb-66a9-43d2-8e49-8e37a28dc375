import pytest
from prime_rat_logic_service_client import JobPsv<PERSON><PERSON><PERSON>rgs, Jobs<PERSON><PERSON>

from service.db import ServiceDAL
from service.job_type import JobType
from service.k8s_jobs.security_violation_job.models import PsvJobArgs
from tests.mock_utils import SOURCE_ID

from ...fixtures import TEST_CREATED_BY
from .jobs_test_utils import generic_job_validation, get_next_job_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestPsvJob:
    async def test_psv_job_run(self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, account_id: str):
        create_arg = JobPsvCreateArgs(
            job=JobType.SECURITY_VIOLATION,
            source_id=SOURCE_ID,
            created_by=TEST_CREATED_BY,
            force=False,
        )
        job_id = await get_next_job_id(service_dal_fixture)
        expected_job_args = PsvJobArgs(
            account_id=account_id,
            source_id=SOURCE_ID,
            job_id=job_id,
            force=False,
            last_psv_at=None,
            datadog_enabled=False,
        )
        await generic_job_validation(jobs_api, JobType.SECURITY_VIOLATION, create_arg, expected_job_args)

        create_arg = JobPsvCreateArgs(
            job=JobType.SECURITY_VIOLATION,
            source_id=SOURCE_ID + 1,
            created_by=TEST_CREATED_BY,
            force=True,
        )
        expected_job_args = PsvJobArgs(
            account_id=account_id,
            source_id=SOURCE_ID + 1,
            job_id=job_id + 1,
            force=True,
            last_psv_at=None,
            datadog_enabled=False,
        )
        await generic_job_validation(jobs_api, JobType.SECURITY_VIOLATION, create_arg, expected_job_args)
