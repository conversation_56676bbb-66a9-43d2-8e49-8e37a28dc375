import pytest
from prime_rat_logic_service_client import JobB<PERSON><PERSON>ieldsDataCreateArgs, Jobs<PERSON><PERSON>

from service.db import Service<PERSON><PERSON>
from service.job_type import JobType
from service.k8s_jobs.build_fields_data_job.models import BuildFieldsDataJobArgs
from tests.mock_utils import SOURCE_ID

from ...fixtures import TEST_CREATED_BY
from .jobs_test_utils import generic_job_validation, get_next_job_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestBuildFieldsDataJob:
    async def test_build_fields_data_job(self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, account_id: str):
        create_arg = JobBuildFieldsDataCreateArgs(
            job=JobType.BUILD_FIELDS_DATA, source_id=SOURCE_ID, created_by=TEST_CREATED_BY
        )
        job_id = await get_next_job_id(service_dal_fixture)
        expected_job_args = BuildFieldsDataJobArgs(account_id=account_id, source_id=SOURCE_ID, job_id=job_id)
        await generic_job_validation(jobs_api, JobType.BUILD_FIELDS_DATA, create_arg, expected_job_args)
