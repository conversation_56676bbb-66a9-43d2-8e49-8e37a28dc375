import asyncio
import contextlib
import threading
from datetime import UTC, datetime
from pathlib import Path
from typing import Any as TypingAny
from unittest.mock import patch

import pytest
from kubernetes.client import V1Job, V1ObjectMeta
from prime_jobs import Job<PERSON>tatus, UpdateJob
from prime_rat_logic_service_client import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>, JobUpdateIssuesC<PERSON><PERSON>rgs
from prime_source_service_client import SourceModel, SourceType
from prime_tests import MockResponse

from service.db import ServiceDAL, get_service_dal_context
from service.job_type import JobType
from service.k8s_jobs.job_spawners import JobConfig, RatJobEnv
from service.k8s_jobs.update_issues_job.models import UpdateIssuesJobArgs
from service.k8s_jobs.update_issues_job.update_issues import UpdateIssuesJobLogic
from service.logic.issues_graph import GraphGenerator, IssuesGraph
from tests.fixtures import TEST_CREATED_BY
from tests.mock_utils import SOURCE_ID, _basic_mocks, mock_get_descendants_links, mock_get_issues_files

from ...case_test_utils import create_cases
from .jobs_test_utils import generic_job_validation, get_next_job_id


async def _async_helper(job_name, job_config: JobConfig):
    account_id = job_config.job_env.account_id
    source_id = job_config.job_env.source_id
    job_id = job_config.job_info.id

    async with get_service_dal_context() as service_dal:
        await service_dal.scheduler_dal.jobs_dal.update_job(account_id, job_id, UpdateJob(job_name=job_name))
        job = await UpdateIssuesJobLogic.build(
            account_id=account_id,
            source_id=source_id,
            job_id=job_id,
            service_dal=service_dal,
            last_update_at=None,
            force=False,
            update_fields_only=False,
        )
        await job.start()


def _create_event_loop() -> asyncio.AbstractEventLoop:
    loop = asyncio.get_event_loop_policy().new_event_loop()
    threading.Thread(target=loop.run_forever, name="test_thread", daemon=True).start()
    while not loop.is_running():
        pass
    return loop


@contextlib.contextmanager
def mock_spawn_update_job():
    ret_value = [V1Job(api_version="batch/v1", kind="Job", metadata=V1ObjectMeta(name="test-job", namespace="default"))]

    def build_method(job_name: str, path: Path, job_config: dict) -> list[dict[str, TypingAny]]:
        loop = _create_event_loop()
        job_env = RatJobEnv.model_validate(job_config.pop("job_env"))
        job_config = JobConfig(job_env=job_env, **job_config)
        asyncio.run_coroutine_threadsafe(_async_helper(job_name, job_config), loop=loop).result()
        return ret_value

    with patch("prime_jobs.job_spawner.deploy_helm", side_effect=build_method):
        yield


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestUpdateIssuesJob:
    async def test_update_issues_job(self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, account_id: str):
        create_arg = JobUpdateIssuesCreateArgs(
            job=JobType.UPDATE_ISSUES, created_by=TEST_CREATED_BY, source_id=SOURCE_ID
        )
        job_id = await get_next_job_id(service_dal_fixture)
        expected_job_args = UpdateIssuesJobArgs(
            account_id=account_id,
            job_id=job_id,
            source_id=SOURCE_ID,
            force=False,
            last_update_at=None,
            update_fields_only=False,
        )
        job_resp = await generic_job_validation(jobs_api, JobType.UPDATE_ISSUES, create_arg, expected_job_args)
        job = await service_dal_fixture.scheduler_dal.jobs_dal.get_job_by_id(account_id, job_resp.job_id)
        job.status = JobStatus.COMPLETED
        await service_dal_fixture.session.commit()

        create_arg = JobUpdateIssuesCreateArgs(
            job=JobType.UPDATE_ISSUES, created_by=TEST_CREATED_BY, source_id=SOURCE_ID
        )
        expected_job_args = UpdateIssuesJobArgs(
            account_id=account_id,
            job_id=job_id + 1,
            source_id=SOURCE_ID,
            last_update_at=job.created_at,
            force=False,
            update_fields_only=False,
        )
        job_resp = await generic_job_validation(jobs_api, JobType.UPDATE_ISSUES, create_arg, expected_job_args)
        job = await service_dal_fixture.scheduler_dal.jobs_dal.get_job_by_id(account_id, job_resp.job_id)
        job.status = JobStatus.COMPLETED
        await service_dal_fixture.session.commit()

        create_arg = JobUpdateIssuesCreateArgs(
            job=JobType.UPDATE_ISSUES, created_by=TEST_CREATED_BY, source_id=SOURCE_ID, force=True
        )
        expected_job_args = UpdateIssuesJobArgs(
            account_id=account_id,
            job_id=job_id + 2,
            source_id=SOURCE_ID,
            last_update_at=job.created_at,
            force=True,
            update_fields_only=False,
        )
        await generic_job_validation(jobs_api, JobType.UPDATE_ISSUES, create_arg, expected_job_args)

    async def test_update_issues_expected(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, jobs_api: JobsApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with (
            _basic_mocks() as (file_manager_mocker, source_mocker, _),
            mock_spawn_update_job(),
            patch.object(GraphGenerator, "update_since", return_value=IssuesGraph.build([])),
        ):
            mock_get_descendants_links(file_manager_mocker, account_id)
            mock_get_issues_files(file_manager_mocker)
            source = SourceModel(id=SOURCE_ID, source_type=SourceType.JIRA, created_at=datetime.now(UTC))
            source_mocker.get(f"/sources/{account_id}", MockResponse([source.model_dump()]))
            cases1 = await cases_api.get_cases_for_account(account_id=account_id)
            org_provider_fields = {c.issue_id: c.provider_fields for c in cases1.results}
            job_args = JobUpdateIssuesCreateArgs(
                job=JobType.UPDATE_ISSUES, created_by=TEST_CREATED_BY, source_id=SOURCE_ID
            )
            res = await jobs_api.add_job(account_id=account_id, job_create_args=JobCreateArgs(actual_instance=job_args))
            assert res.job_id
            assert res.status == JobStatus.COMPLETED
            cases2 = await cases_api.get_cases_for_account(account_id=account_id)
            for case in cases2.results:
                assert case.provider_fields != org_provider_fields[case.issue_id]
