import pytest
from prime_rat_logic_service_client import <PERSON><PERSON><PERSON><PERSON>, JobSummaryCreateArgs

from service.db import ServiceDAL
from service.job_type import JobType
from service.k8s_jobs.summary_job.models import SummaryJobArgs
from tests.mock_utils import SOURCE_ID

from ...fixtures import TEST_CREATED_BY
from .jobs_test_utils import generic_job_validation, get_next_job_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestSummaryJob:
    async def test_final_summary_job_run(self, jobs_api: JobsApi, service_dal_fixture: ServiceDAL, account_id: str):
        create_arg = JobSummaryCreateArgs(
            job=JobType.SUMMARY,
            source_id=SOURCE_ID,
            created_by=TEST_CREATED_BY,
        )
        job_id = await get_next_job_id(service_dal_fixture)
        expected_job_args = SummaryJobArgs(
            account_id=account_id, source_id=SOURCE_ID, job_id=job_id, force=False, parent_id=None
        )
        await generic_job_validation(jobs_api, JobType.SUMMARY, create_arg, expected_job_args)

        create_arg = JobSummaryCreateArgs(
            job=JobType.SUMMARY,
            source_id=SOURCE_ID + 1,
            created_by=TEST_CREATED_BY,
            force=True,
        )
        expected_job_args = SummaryJobArgs(
            account_id=account_id, source_id=SOURCE_ID + 1, job_id=job_id + 1, force=True, parent_id=None
        )
        await generic_job_validation(jobs_api, JobType.SUMMARY, create_arg, expected_job_args)
