import pytest
from prime_rat_logic_service_client import Cases<PERSON><PERSON>

from service.db import <PERSON><PERSON><PERSON>
from service.models import CaseStatus
from service.models.filters_and_sort import Filter, Operator
from tests.case_test_utils import (
    create_cases,
)
from tests.mock_utils import SOURCE_ID, _basic_mocks

test_cases_view_id = "confidence_level_high"  # hardcoded in the test config.json
test_containers_view_id = "risk_score_high"  # hardcoded in the test config.json


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCasesView:
    async def test_get_cases_by_query_view(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            extra = [{"confidence": rank} for rank in [10, 40, 80, 90, 30, 20, 15, 88, 92, 5]]
            await create_cases(service_dal_fixture, account_id, SOURCE_ID, extra=extra)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                query_cases_view_id=test_cases_view_id,
            )
            assert len(account_cases.results) == 4
            assert all(case.issue_analysis.confidence >= 75 for case in account_cases.results)

    async def test_get_containers_by_query_view(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            extra = [{"risk_score": rank} for rank in [10, 40, 80, 90, 30, 20, 15, 88, 92, 5]]
            await create_cases(service_dal_fixture, account_id, SOURCE_ID, extra=extra)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                query_cases_view_id=test_containers_view_id,
            )
            assert len(account_cases.results) == 3
            assert all(case.issue_analysis.risk_score >= 87 for case in account_cases.results)

    async def test_get_cases_by_query_cases_view_and_filter(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            extra = [{"confidence": rank} for rank in [10, 40, 80, 90, 30, 20, 15, 88, 92, 5]]
            cases = await create_cases(service_dal_fixture, account_id, SOURCE_ID, extra=extra)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                query_cases_view_id=test_cases_view_id,
                f=[
                    Filter(field="status", value="Open", op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 4

            await service_dal_fixture.cases_dal.update_case_status(
                account_id, SOURCE_ID, cases[2].issue_id, CaseStatus.DONE
            )
            await service_dal_fixture.session.commit()
            account_cases = await cases_api.get_cases_for_account(
                account_id,
                query_cases_view_id=test_cases_view_id,
                f=[
                    Filter(field="status", value="Open", op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 3

            # check with provider fields filter
            account_cases = await cases_api.get_cases_for_account(
                account_id,
                query_cases_view_id=test_cases_view_id,
                f=[
                    Filter(field="provider_fields.status", value="Open", op=Operator.EQ).as_json_str(),
                ],
            )
            assert len(account_cases.results) == 1
