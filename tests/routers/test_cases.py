import copy
import json
import os
from unittest import mock
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>

import pytest
from prime_jira_client import PrimeJiraClient
from prime_rat_logic_service_client import (
    CasesApi,
    ContainersApi,
    CreateCaseComment,
    ExternalImplementation,
    ImplementationStatusUpdate,
    RiskScoreCategory,
)
from prime_rat_logic_service_client.exceptions import NotFoundException, ServiceException
from prime_security_review_service_client import DesignDocResponse
from prime_redis_utils import AsyncRedisClient
from prime_shared.common_dataclasses import SecurityFramework
from prime_shared.common_values import USER_ID_HEADER_NAME
from prime_source_service_client import JiraConnectionDetails
from prime_tests import (
    EnvSave,
    MockResponse,
    service_mocker,
)
from pydantic import BaseModel

from client.prime_rat_logic_service_client.models.external_prime_concern import ExternalPrimeConcern
from service.config import get_config
from service.db import CaseTable, ServiceDAL
from service.errors import CaseNotFound<PERSON>rror, CaseRecommendationNotFound
from service.k8s_jobs.classification_job.task_helpers import get_recommendations_and_concerns
from service.logic.external_cases import build_external_case
from service.logic.issue_analysis import build_external_concerns
from service.logic.issues.utils import get_provider_fields_info_stored
from service.logic.recommendation_builder import get_controls_dict
from service.logic.write_back import _build_concerns_recommendations_map, _get_comment_template
from service.models import (
    CaseAuditOverrideRiskCategoryArgs,
    CaseAuditUpdateStatusArgs,
    CaseStatus,
    ConcernType,
    ExternalCase,
    ImplementationStatus,
    UserImplementationStatus,
    risk_category_to_score,
)
from service.models.cases import CaseAuditAction
from service.models.filters_and_sort import Filter, Operator
from tests.case_test_utils import (
    add_recommendation_to_case,
    build_external_case_for_testing,
    create_cases,
)
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    FAKE_PROVIDER_FIELDS_DATA,
    JIRA_HOST,
    SOURCE_ID,
    TESTS_ISSUES,
    _basic_mocks,
    get_classification_output,
    mock_jira_client,
)

from ..case_test_utils import TEST_CONCERNS, TEST_RECOMMENDATIONS, create_case


class ImplementationNotFound(Exception):
    def __init__(self, implementation_id: str):
        self.implementation_id = implementation_id
        super().__init__(f"Implementation {self.implementation_id} not found")


def _check_for_framework(result_case: ExternalCase, security_framework: SecurityFramework):
    case_controls = []
    for framework in result_case.framework_concerns.values():
        for concern in framework:
            case_controls.extend(concern.controls)
    framework_controls = get_controls_dict(security_framework)
    framework_controls_keys = set(framework_controls.keys())
    assert all(control.id in framework_controls for control in case_controls)
    assert all(control.framework == security_framework for control in case_controls)
    assert all(set(control.control_names).issubset(framework_controls_keys) for control in case_controls)


def _get_implementation_by_id(case: ExternalCase, implementation_id: int) -> ExternalImplementation:
    def find_recommendation(implementations: list[ExternalImplementation]):
        for implementation in implementations:
            if implementation.id == implementation_id:
                return implementation

    for concern in case.prime_concerns:
        for recommendation in concern.recommendations:
            if result := find_recommendation(recommendation.implementations):
                return result

    for framework in case.framework_concerns.values():
        for concern in framework:
            for control in concern.controls:
                if result := find_recommendation(control.implementations):
                    return result

    raise ImplementationNotFound(implementation_id=implementation_id)


async def _test_implementation_status(
    cases_api: CasesApi,
    case: CaseTable,
    recommendation_id: int,
    service_dal: ServiceDAL,
    validate_status: ImplementationStatus | None = None,
    update_status: UserImplementationStatus | None = None,
    concern_id: int | None = None,
    control_id: str | None = None,
):
    if update_status:
        updated_rec = ImplementationStatusUpdate(
            id=recommendation_id, status=update_status, concern_id=concern_id, control_id=control_id
        )
        await cases_api.update_recommendations(
            account_id=ACCOUNT_ID_CONTEXT.get(),
            source_id=SOURCE_ID,
            issue_id=case.issue_id,
            implementation_status_update=[updated_rec],
        )
    if validate_status:
        result_case = await cases_api.get_case(ACCOUNT_ID_CONTEXT.get(), SOURCE_ID, case.issue_id)
        result_rec = _get_implementation_by_id(result_case, recommendation_id)
        assert result_rec.status == validate_status


def _check_mitre_category_has_updated(prime_concerns: list[ExternalPrimeConcern]):
    for prime_concern in prime_concerns:
        if prime_concern.methodology.type.value == ConcernType.MITRE.value:
            assert not prime_concern.methodology.category.startswith("TA"), "Tactic id should be updated to tactic name"


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestCasesRoute:
    async def test_add_comment(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        assert case.comments is None
        comment1 = CreateCaseComment(user="momo", text="test comment")
        await cases_api.add_comment(account_id, SOURCE_ID, case.issue_id, comment1)

        comment2 = CreateCaseComment(user="momo2", text="test comment 2")
        await cases_api.add_comment(account_id, SOURCE_ID, case.issue_id, comment2)

        with _basic_mocks():
            response = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)

            assert len(response.comments) == 2
            for idx, comment in enumerate([comment1, comment2]):
                assert response.comments[idx].text == comment.text
                assert response.comments[idx].user == comment.user

        with pytest.raises(NotFoundException) as e:
            comment3 = CreateCaseComment(user="momo", text="test comment 3")
            await cases_api.add_comment(account_id, SOURCE_ID, "some_issue", comment3)
        error = json.loads(e.value.body)
        assert error["exception"] == repr(CaseNotFoundError(account_id, SOURCE_ID, "some_issue"))

    async def test_case_status(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        assert case.status == CaseStatus.OPEN
        dismiss_reason = "test reason"
        headers = {USER_ID_HEADER_NAME: "moshe"}
        with _basic_mocks():
            await cases_api.update_status(
                account_id, SOURCE_ID, case.issue_id, CaseStatus.DISMISSED, dismiss_reason, _headers=headers
            )
            updated_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
        assert updated_case.status == CaseStatus.DISMISSED
        assert len(updated_case.history) == 1
        assert updated_case.history[0].audit_action == CaseAuditAction.update_status
        expected = CaseAuditUpdateStatusArgs(
            old_status=CaseStatus.OPEN, new_status=CaseStatus.DISMISSED, reason=dismiss_reason
        )
        assert updated_case.history[0].audit_action_args == expected.model_dump()
        assert updated_case.history[0].user == "moshe"

        with pytest.raises(NotFoundException) as e, _basic_mocks():
            await cases_api.update_status(account_id, SOURCE_ID, "some_issue", CaseStatus.DISMISSED)
        error = json.loads(e.value.body)
        assert error["exception"] == repr(CaseNotFoundError(account_id, SOURCE_ID, "some_issue"))

    async def test_customer_overrides_risk_score(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        original_risk_score = 10
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            risk_score=original_risk_score,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        assert case.original_risk_score is None
        with _basic_mocks():
            new_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            assert new_case.issue_analysis.risk_score_category == RiskScoreCategory.MONITOR
            new_risk_category = RiskScoreCategory.INTERVENE
            await cases_api.update_risk_score_category(
                account_id, SOURCE_ID, case.issue_id, new_risk_category, _headers={USER_ID_HEADER_NAME: "moshe"}
            )
            updated_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            assert updated_case.issue_analysis.risk_score_category == new_risk_category

            assert len(updated_case.history) == 1
            assert updated_case.history[0].audit_action == CaseAuditAction.override_risk_category
            expected = CaseAuditOverrideRiskCategoryArgs(
                old_risk_score=original_risk_score, new_risk_score=risk_category_to_score(new_risk_category).start
            )
            assert updated_case.history[0].audit_action_args == expected.model_dump(mode="json")
            assert updated_case.history[0].user == "moshe"

            new_risk_category = RiskScoreCategory.NONE
            await cases_api.update_risk_score_category(account_id, SOURCE_ID, case.issue_id, new_risk_category)
            updated_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            assert updated_case.issue_analysis.risk_score_category == new_risk_category
            assert len(updated_case.history) == 2
            assert updated_case.history[1].audit_action == CaseAuditAction.override_risk_category

            # previous expected is the current old value
            expected = CaseAuditOverrideRiskCategoryArgs(
                old_risk_score=expected.new_risk_score, new_risk_score=risk_category_to_score(new_risk_category).start
            )
            assert updated_case.history[1].audit_action_args == expected.model_dump(mode="json")

    async def test_case_no_recommendations(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1))[0]
        ai_output = get_classification_output(is_security=True, with_recommendations=False)
        ai_concerns, ai_controls, ai_recommendations = get_recommendations_and_concerns(
            ai_output.personal_recommendations, ai_output.privacy_concerns, ai_output.mitre_attack_concerns
        )
        with _basic_mocks():
            result_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            expected_framework_concerns, expected_prime_concerns = await build_external_concerns(
                account_id, ai_concerns, ai_controls, ai_recommendations
            )
        assert len(result_case.prime_concerns) == len(TEST_CONCERNS)
        assert len(result_case.prime_concerns) == len(expected_prime_concerns)
        for i, concern in enumerate(result_case.prime_concerns):
            assert concern.model_dump_json() == expected_prime_concerns[i].model_dump_json()
        # make sure all are NIST in results
        _check_mitre_category_has_updated(result_case.prime_concerns)

    async def test_recommendations(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        extra = [{"with_recommendations": True}]
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1, extra=extra))[0]
        await add_recommendation_to_case(service_dal_fixture, case)
        ai_output = get_classification_output(is_security=True, with_recommendations=True)
        ai_concerns, ai_controls, ai_recommendations = get_recommendations_and_concerns(
            ai_output.personal_recommendations, ai_output.privacy_concerns, ai_output.mitre_attack_concerns
        )
        with _basic_mocks():
            expected_framework_concerns, expected_prime_concerns = await build_external_concerns(
                account_id, ai_concerns, ai_controls, ai_recommendations
            )
            result_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)

        assert "NIST" in result_case.framework_concerns
        nist_framework = result_case.framework_concerns["NIST"]

        assert len(nist_framework) == len(TEST_CONCERNS)
        assert len(result_case.prime_concerns) == len(TEST_CONCERNS)

        sample_recommendation1 = ExternalImplementation(**TEST_RECOMMENDATIONS[2650].model_dump())
        sample_recommendation2 = ExternalImplementation(**TEST_RECOMMENDATIONS[1958].model_dump())

        assert nist_framework[1].controls[0].implementations[0] == sample_recommendation1
        assert nist_framework[0].controls[0].implementations[0] == sample_recommendation2

        implementations_controls_framework = (
            result_case.prime_concerns[0].recommendations[0].implementations[0].controls.keys()
        )
        assert len(implementations_controls_framework) == 1
        assert "NIST" in implementations_controls_framework

        assert nist_framework[1].controls[0].implementations[0] == sample_recommendation1
        assert nist_framework[0].controls[0].implementations[0] == sample_recommendation2

        assert len(result_case.prime_concerns) == len(expected_prime_concerns)
        for i, concern in enumerate(result_case.prime_concerns):
            assert concern.model_dump_json() == expected_prime_concerns[i].model_dump_json()

        assert len(result_case.framework_concerns) == len(expected_framework_concerns)
        for i, concern in enumerate(result_case.framework_concerns["NIST"]):
            assert concern.model_dump_json() == expected_framework_concerns["NIST"][i].model_dump_json()

        # make sure all are NIST in results
        _check_for_framework(result_case, SecurityFramework.NIST)
        _check_mitre_category_has_updated(result_case.prime_concerns)

    async def test_framework_change(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1))[0]
        with _basic_mocks(framework="HITRUST"):
            result_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
        _check_for_framework(result_case, SecurityFramework.HITRUST)

        with _basic_mocks(framework="CIS"):
            with _basic_mocks():
                result_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            _check_for_framework(result_case, SecurityFramework.CIS)

        with _basic_mocks(framework="PCI"):
            with _basic_mocks():
                result_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            _check_for_framework(result_case, SecurityFramework.PCI)

    async def test_write_back(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        mock_comment = MagicMock()
        mock_comment.id = "123"

        recommendations = copy.deepcopy(TEST_RECOMMENDATIONS)
        recommendations[0].status = ImplementationStatus.APPROVED
        recommendations[1].status = ImplementationStatus.APPROVED
        test_issue = TESTS_ISSUES[0]
        case = await create_case(
            service_dal_fixture,
            test_issue,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )

        assert case.write_back_ref_id is None
        with (
            _basic_mocks(),
            service_mocker("source-service") as source_mocker,
            mock.patch.object(PrimeJiraClient, "add_comment") as jira_client_mocked,
        ):
            jira_client_mocked.return_value = mock_comment

            connection = JiraConnectionDetails(jira_url=JIRA_HOST, email="<EMAIL>", api_token="123456")
            mocked_resp = MockResponse(connection.model_dump())
            source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}/connection_details/", mocked_resp)

            await cases_api.write_back(account_id, SOURCE_ID, case.issue_id)
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, case.issue_id)
            assert case.write_back_ref_id is None

            await add_recommendation_to_case(service_dal_fixture, case)
            updated_rec = ImplementationStatusUpdate(
                id=TEST_RECOMMENDATIONS[0].id, status=UserImplementationStatus.APPROVED
            )
            await cases_api.update_recommendations(
                account_id=account_id,
                source_id=SOURCE_ID,
                issue_id=case.issue_id,
                implementation_status_update=[updated_rec],
            )
            await cases_api.write_back(account_id, SOURCE_ID, case.issue_id)

            await service_dal_fixture.session.refresh(case)
            case, external_case = await build_external_case_for_testing(
                case, service_dal_fixture, test_issue, case.recommendations
            )
            concerns_recommendations_map = await _build_concerns_recommendations_map(external_case)
            write_back_text = _get_comment_template(concerns_recommendations_map)
            jira_client_mocked.assert_called_with(test_issue, write_back_text)

        await service_dal_fixture.session.refresh(case)
        assert case.write_back_ref_id == "123"

    async def test_delete_source_cases(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, containers_api: ContainersApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            source_id=SOURCE_ID + 1,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        with _basic_mocks():
            await cases_api.delete_cases_for_source(account_id, SOURCE_ID)
            response = await cases_api.get_cases_for_account_and_source(account_id, SOURCE_ID)
            assert len(response.results) == 0
            response = await cases_api.get_cases_for_account(account_id)
            assert len(response.results) == 1

    async def test_export_for_account(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        await create_cases(service_dal_fixture)
        with _basic_mocks() as (file_manager_mocker, source_manager_mocker, _):
            selected_columns = [
                "id",
                "summary",
                "status",
                "confidence_level",
                "risk_score_category",
                "is_automated",
                "labels",
                "confidentiality_level",
                "integrity_level",
                "availability_level",
                "provider_fields.resolution",
                "provider_fields.issuetype",
                "provider_fields.labels",
                "provider_fields.reporter",
            ]

            expected_result = """"id","summary","status","confidence_level","risk_score_category","is_automated","labels","confidentiality_level","integrity_level","availability_level","provider_fields.resolution","provider_fields.issuetype","provider_fields.labels","provider_fields.reporter"
"ISSUE-1","Issue 1 - lets go","open","high","analyze",False,"[]","high","medium","medium","","Epic","['label1', 'label2']","Matan Markovics"
"ISSUE-2","Issue 2 is here","open","high","analyze",False,"[]","high","medium","medium","","Task","['label2', 'label3']","Matan Markovics"
"ISSUE-3","Issue 3 is here","open","high","analyze",False,"[]","high","medium","medium","","Subtask","[]","Orna Khait-Marelly"
"ISSUE-4","Issue 4 Title Lets do Issue 4 & and more","open","high","analyze",False,"[]","high","medium","medium","","Task","['label7', 'label10']","Matan Markovics"
"ISSUE-5","Issue 5 is here","open","high","analyze",False,"[]","high","medium","medium","","Task","['label3']","Matan Markovics"
"ISSUE-6","Issue 6 is here","open","high","analyze",False,"[]","high","medium","medium","","Task","['label3']","Matan Markovics"
"ISSUE-7","Issue 7 is here","open","high","analyze",False,"[]","high","medium","medium","","Epic","['label3']","Matan Markovics"
"ISSUE-8","Issue 8 is here","open","high","analyze",False,"[]","high","medium","medium","","Epic","['label3']","Matan Markovics"
"ISSUE-9","Issue 9 is here","open","high","analyze",False,"[]","high","medium","medium","","Epic","['label3']","Matan Markovics"
"ISSUE-10","Issue 10 is here","open","high","analyze",False,"[]","high","medium","medium","","Task","['label3']","Matan Markovics"
"""
            with EnvSave():
                os.environ["EXPORT_LIMIT"] = "2"  # to test iterations
                get_config.cache_clear()
                _filter = Filter(field="status", value="open", op=Operator.EQ)
                actual_result = await cases_api.export_for_account_without_preload_content(
                    account_id=account_id,
                    selected_columns=selected_columns,
                    f=[_filter.as_json_str()],
                    _request_timeout=3,
                )
            assert (await actual_result.read()).decode() == expected_result

    async def test_check_external_case_provider_fields_info(
        self,
        fake_redis_fixture: AsyncRedisClient,
        service_dal_fixture: ServiceDAL,
        cases_api: CasesApi,
        account_id: str,
    ):
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            with_analysis=True,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[2],
        )

        chosen_fields_names = ["creator", "status", "field_2", "field_6"]
        with _basic_mocks(selected_jira_fields=chosen_fields_names):
            actual_case_response = await cases_api.get_case(case.account_id, case.source_id, case.issue_id)
            stored_fields = await get_provider_fields_info_stored(account_id, SOURCE_ID, fake_redis_fixture)
            expected_case = await build_external_case(case, service_dal_fixture, provider_fields_info=stored_fields)
        actual_resp_dict = actual_case_response.provider_fields
        assert len(actual_resp_dict) == len(expected_case.provider_fields.keys())
        for k, v in actual_resp_dict.items():
            expected_value = expected_case.provider_fields[k]
            assert v == expected_value.model_dump() if isinstance(expected_value, BaseModel) else expected_value
        assert expected_case.title
        assert expected_case.link
        assert sorted(actual_resp_dict.keys()) == sorted(expected_case.provider_fields.keys())

        await fake_redis_fixture.flushall()
        with _basic_mocks():
            account_cases_response = await cases_api.get_cases_for_account(case.account_id)
        assert len(account_cases_response.results) == 1
        actual_case_response = account_cases_response.results[0]
        assert len(actual_case_response.provider_fields) == len(FAKE_PROVIDER_FIELDS_DATA[2])

    async def test_update_recommendation_approved(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        extra = [{"with_recommendations": True}]
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1, extra=extra))[0]
        await add_recommendation_to_case(service_dal_fixture, case)
        with _basic_mocks():
            result_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            recommendation_id = result_case.prime_concerns[4].recommendations[0].implementations[0].id
            await _test_implementation_status(
                cases_api, case, recommendation_id, service_dal_fixture, ImplementationStatus.UNKNOWN
            )
            await _test_implementation_status(
                cases_api,
                case,
                recommendation_id,
                service_dal_fixture,
                ImplementationStatus.APPROVED,
                UserImplementationStatus.APPROVED,
            )

    async def test_update_recommendation_not_found(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        extra = [{"with_recommendations": True}]
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1, extra=extra))[0]
        with _basic_mocks():
            with pytest.raises(ServiceException) as e:
                await cases_api.update_recommendations(
                    account_id=account_id,
                    source_id=SOURCE_ID,
                    issue_id=case.issue_id,
                    implementation_status_update=[
                        ImplementationStatusUpdate(id=8879878, status=UserImplementationStatus.APPROVED)
                    ],
                )
            assert json.loads(e.value.body)["type"] == CaseRecommendationNotFound.__name__

    async def test_update_recommendation_dismiss(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        extra = [{"with_recommendations": True}]
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1, extra=extra))[0]
        await add_recommendation_to_case(service_dal_fixture, case)
        with _basic_mocks():
            result_case = await cases_api.get_case(account_id, SOURCE_ID, case.issue_id)
            recommendation_id = result_case.prime_concerns[0].recommendations[0].implementations[0].id
            updated_rec = ImplementationStatusUpdate(id=recommendation_id, status=UserImplementationStatus.APPROVED)
            await cases_api.update_recommendations(
                account_id=account_id,
                source_id=SOURCE_ID,
                issue_id=case.issue_id,
                implementation_status_update=[updated_rec],
            )
            await _test_implementation_status(
                cases_api,
                case,
                recommendation_id,
                service_dal_fixture,
                ImplementationStatus.DISMISSED,
                UserImplementationStatus.DISMISS,
            )

    async def test_add_watcher(self, cases_api: CasesApi, account_id: str):
        with mock_jira_client(get_jira_target="service.logic.jira_manager.jira_issues_manager.get_jira_client") as (
            _,
            get_client,
            source_mocker,
        ):
            jira_client: PrimeJiraClient = await get_client()
            jira_client.add_watcher_to_issue = Mock()

            watcher_email = "<EMAIL>"
            issue_id = "ISSUE-1"
            await cases_api.add_watcher(account_id, SOURCE_ID, issue_id, watcher_email)

            jira_client.add_watcher_to_issue.assert_called_once_with(issue_id, watcher_email)

    async def test_get_case_id(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1))[0]
        with _basic_mocks():
            result_id = await cases_api.get_case_id(account_id, SOURCE_ID, case.issue_id)
            assert case.id == result_id

    async def test_get_case_hash(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        case = (await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=1))[0]
        with _basic_mocks():
            result_case = await cases_api.get_case_by_id(account_id, case.id)
            assert result_case.issue_analysis.issue_hash == case.issue_analysis.issue_hash


