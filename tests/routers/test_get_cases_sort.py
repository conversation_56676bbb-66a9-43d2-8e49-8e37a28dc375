import pytest
from prime_rat_logic_service_client import Cases<PERSON>pi
from sqlalchemy.exc import DBAPIError

from service.db import ServiceDAL
from service.models.filters_and_sort import SortDirection, SortField
from tests.case_test_utils import create_cases
from tests.mock_utils import FAKE_JIRA_FIELDS_SCHEMA, SOURCE_ID, _basic_mocks


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCasesSort:
    async def test_get_cases_with_sort_title(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=9)

        with _basic_mocks():
            sort = SortField(field="title", direction=SortDirection.ASC)
            cases = (await cases_api.get_cases_for_account(account_id, s=[sort.as_json_str()])).results
            assert cases == sorted(cases, key=lambda x: x.title)

            sort = SortField(field="title", direction=SortDirection.DESC)
            cases = (await cases_api.get_cases_for_account(account_id, s=[sort.as_json_str()])).results
            assert cases == sorted(cases, key=lambda x: x.title, reverse=True)

    async def test_get_cases_with_sort_issue_id(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)

        with _basic_mocks():
            sort = SortField(field="issue_id", direction=SortDirection.ASC)
            cases = (await cases_api.get_cases_for_account(account_id, s=[sort.as_json_str()])).results
            assert cases == sorted(cases, key=lambda x: x.issue_id)

            sort = SortField(field="issue_id", direction=SortDirection.DESC)
            cases = (await cases_api.get_cases_for_account(account_id, s=[sort.as_json_str()])).results
            assert cases == sorted(cases, key=lambda x: x.issue_id, reverse=True)

    async def test_get_cases_sort_wrong_type(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        provider_fields_override = [item.model_dump(by_alias=True) for item in FAKE_JIRA_FIELDS_SCHEMA]
        field_with_wrong_type = next(f for f in provider_fields_override if f["id"] == "field_4")
        field_with_wrong_type["schema"]["type"] = "date"
        with _basic_mocks(selected_jira_fields=["field_4"], jira_fields_override=provider_fields_override):
            sort = SortField(field="provider_fields.field_4", direction=SortDirection.ASC)
            with pytest.raises(DBAPIError):
                t = await cases_api.get_cases_for_account(account_id, s=[sort.as_url_str()])
                print(t)

    async def test_get_cases_sort_provider_fields(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=["field_3", "field_4", "field_5"]):
            sort = SortField(field="provider_fields.field_5", direction=SortDirection.ASC)
            cases = (await cases_api.get_cases_for_account(account_id, s=[sort.as_json_str()])).results
        assert cases == sorted(cases, key=lambda x: x.provider_fields["field_5"])

    async def test_get_cases_with_sort_integrity_level(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        integrity_scores = [3, 8, 2, 9, 0, 1, 4, 5, 6, 7]
        created_cases = await create_cases(service_dal_fixture)

        for integrity, case in zip(integrity_scores, created_cases, strict=False):
            issue_analysis = await service_dal_fixture.issues_analysis_dal.get(
                case.account_id, case.source_id, case.issue_id
            )
            issue_analysis.integrity = integrity
        await service_dal_fixture.session.commit()

        sort = SortField(field="integrity_level", direction=SortDirection.ASC)
        with _basic_mocks(selected_jira_fields=["field_3", "field_4", "field_5"]):
            cases = (
                await cases_api.get_cases_for_account(
                    account_id,
                    s=[sort.as_json_str()],
                )
            ).results
        cases_ids = [case.issue_id for case in cases]
        expected_order = [sorted(integrity_scores).index(level) for level in integrity_scores]
        assert expected_order == [cases_ids.index(actual_case.issue_id) for actual_case in created_cases]

    @pytest.mark.parametrize(
        "field_level_name, field_name, levels",
        [
            ("confidence_level", "confidence", [40, 60, 10, 100, 20, 50, 70, 90, 30, 80]),
            ("risk_score_category", "risk_score", [90, 50, 10, 30, 100, 70, 20, 15, 21, 99]),
        ],
    )
    async def test_get_cases_with_sort_level(
        self,
        service_dal_fixture: ServiceDAL,
        field_level_name: str,
        field_name: str,
        levels: list[int],
        cases_api: CasesApi,
        account_id: str,
    ):
        extra = [{field_name: level} for level in levels]
        created_cases = await create_cases(service_dal_fixture, extra=extra)
        sort = SortField(field=field_level_name, direction=SortDirection.ASC)
        with _basic_mocks():
            cases = (
                await cases_api.get_cases_for_account(
                    account_id,
                    s=[sort.as_json_str()],
                )
            ).results
        cases_ids = [case.issue_id for case in cases]
        expected_order = [sorted(levels).index(level) for level in levels]
        actual_order = [cases_ids.index(actual_case.issue_id) for actual_case in created_cases]
        assert expected_order == actual_order

    async def test_get_cases_with_sort_parents(
        self,
        service_dal_fixture: ServiceDAL,
        cases_api: CasesApi,
        account_id: str,
    ):
        created_cases = await create_cases(service_dal_fixture)
        sort = SortField(field="parents", direction=SortDirection.ASC)
        with _basic_mocks():
            result_cases = (
                await cases_api.get_cases_for_account(
                    account_id,
                    s=[sort.as_json_str()],
                )
            ).results
        results_cases_ids = [case.issue_id for case in result_cases]
        sorted_cases = sorted(created_cases, key=lambda x: str(x.parent_issue_id))
        expected_order = [sorted_cases.index(case) for case in created_cases]
        actual_order = [results_cases_ids.index(case.issue_id) for case in created_cases]
        assert expected_order == actual_order
