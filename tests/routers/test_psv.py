import datetime
import os
from unittest.mock import patch

import pytest
from prime_rat_logic_service_client import BulkUpdatePsvRequest, PsvApi, SinglePsvUpdateRequest
from prime_rat_logic_service_client.exceptions import NotFoundException
from prime_tests import EnvSave

from service.config import get_config
from service.db import PsvTable, ServiceDAL
from service.db.dal_psv import PsvDAL
from service.logic.jira_manager.jira_issues_manager import JiraIssuesManager
from service.models.filters_and_sort import Filter, Operator
from service.models.psv import PotentialSecurityViolation, PsvStatus
from service.routers.psv import EXCLUDED_PSV_FIELDS
from tests.mock_utils import (
    SOURCE_ID,
    _basic_mocks,
    get_issue_id,
    mock_get_issues_files,
)
from tests.psv_utils import create_psv, create_psvs


def _compare_external_psv_to_db(external_psv: PotentialSecurityViolation, psv_table: PsvTable):
    assert external_psv.psv_id == psv_table.id
    assert external_psv.description == psv_table.description
    assert external_psv.type == psv_table.type
    assert external_psv.source_id == psv_table.source_id
    assert external_psv.issue_id == psv_table.issue_id
    assert external_psv.status == psv_table.status
    assert external_psv.dismissed_reason == psv_table.dismissed_reason


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestPSV:
    async def test_get_psv(
        self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, issue_manager: JiraIssuesManager, account_id: str
    ):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            expected_psvs = await create_psvs(service_dal_fixture)
            request_results = await psv_api.get_psv(account_id)
            assert len(request_results.results) == len(expected_psvs)
            for psv in request_results.results:
                db_psv = next(db_psv for db_psv in expected_psvs if psv.issue_id == db_psv.issue_id)
                _compare_external_psv_to_db(psv, db_psv)

            request_results = await psv_api.get_psv(account_id, limit=1)
            assert len(request_results.results) == 1
            assert request_results.results[0].issue_id == get_issue_id(1)
            for psv in request_results.results:
                db_psv = next(db_psv for db_psv in expected_psvs if psv.issue_id == db_psv.issue_id)
                _compare_external_psv_to_db(psv, db_psv)

            request_results = await psv_api.get_psv(account_id, limit=1, offset=1)
            assert len(request_results.results) == 1
            assert request_results.results[0].issue_id == get_issue_id(2)
            for psv in request_results.results:
                db_psv = next(db_psv for db_psv in expected_psvs if psv.issue_id == db_psv.issue_id)
                _compare_external_psv_to_db(psv, db_psv)

    async def test_psv_update_status(
        self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, issue_manager: JiraIssuesManager, account_id: str
    ):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            db_psvs = await create_psvs(service_dal_fixture, account_id, SOURCE_ID)
            filters = [Filter(field="status", op=Operator.EQ, value=PsvStatus.DISMISSED).as_json_str()]
            request_results = await psv_api.get_psv(account_id, f=filters)
            assert len(request_results.results) == 0
            request_results = await psv_api.get_psv(account_id)
            assert len(request_results.results) == len(db_psvs)
            updated_psv = await psv_api.update_psv_status(
                account_id,
                db_psvs[0].id,
                SinglePsvUpdateRequest(new_status=PsvStatus.DISMISSED, dismissed_reason="reason"),
            )
            assert updated_psv.status == PsvStatus.DISMISSED
            filters = [Filter(field="status", op=Operator.EQ, value=PsvStatus.DISMISSED).as_json_str()]
            request_results = await psv_api.get_psv(account_id, f=filters)
            assert [psv.psv_id for psv in request_results.results] == [db_psvs[0].id]
            assert request_results.results[0].dismissed_reason == "reason"

            await psv_api.update_psv_status(
                account_id, db_psvs[0].id, SinglePsvUpdateRequest(new_status=PsvStatus.DONE)
            )
            filters = [Filter(field="status", op=Operator.EQ, value=PsvStatus.DONE).as_json_str()]
            request_results = await psv_api.get_psv(account_id, f=filters)
            assert [psv.psv_id for psv in request_results.results] == [db_psvs[0].id]

            with pytest.raises(NotFoundException):
                await psv_api.update_psv_status(
                    account_id,
                    92348,
                    SinglePsvUpdateRequest(new_status=PsvStatus.DONE),
                )

    async def test_export_for_account(self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, account_id: str):
        def format_field_value(value):
            if "PsvStatus" in str(value):
                return f'"{PsvStatus(value).value}"'
            if isinstance(value, str | datetime.datetime):
                return f'"{value}"'
            if value is None:
                return '""'
            return str(value)

        await create_psvs(service_dal_fixture)
        selected_psv_fields = [k for k in PotentialSecurityViolation.model_fields if k not in EXCLUDED_PSV_FIELDS]
        with EnvSave(), _basic_mocks() as (file_mock, _, _):
            mock_get_issues_files(file_mock)
            fetched_psvs = await psv_api.get_psv(account_id)
            os.environ["EXPORT_LIMIT"] = "2"
            get_config.cache_clear()
            actual_result = await psv_api.export_psv_for_account_without_preload_content(account_id=account_id)

        lines = [",".join(format_field_value(field) for field in selected_psv_fields)]
        lines.extend(
            ",".join(format_field_value(getattr(psv, field)) for field in selected_psv_fields)
            for psv in fetched_psvs.results
        )
        expected_result = "\n".join(lines) + "\n"
        assert (await actual_result.read()).decode() == expected_result

    async def test_delete_psv_for_source(
        self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, issue_manager: JiraIssuesManager, account_id: str
    ):
        with _basic_mocks() as (file_mock, _, _):
            mock_get_issues_files(file_mock)

            await create_psv(service_dal_fixture, get_issue_id(1))
            await create_psv(service_dal_fixture, get_issue_id(2))
            psv = await psv_api.get_psv(account_id, source_id=SOURCE_ID)
            assert len(psv.results) == 2
            await psv_api.delete_source_psv(account_id, SOURCE_ID)
            psv = await psv_api.get_psv(account_id, source_id=SOURCE_ID)
            assert len(psv.results) == 0

    async def test_bulk_update_psv_status(
        self, service_dal_fixture: ServiceDAL, psv_api: PsvApi, issue_manager: JiraIssuesManager, account_id: str
    ):
        with _basic_mocks(account_id=account_id) as (file_mock, _, _):
            mock_get_issues_files(file_mock, account_id)

            # Creating psv entries
            psv_list = [
                await create_psv(service_dal_fixture, get_issue_id(i + 1), account_id, SOURCE_ID) for i in range(3)
            ]

            bulk_update_request = BulkUpdatePsvRequest(
                violations={
                    str(psv_list[0].id): SinglePsvUpdateRequest(new_status=PsvStatus.DONE),
                    str(psv_list[1].id): SinglePsvUpdateRequest(
                        new_status=PsvStatus.DISMISSED, dismissed_reason="test reason"
                    ),
                    str(psv_list[2].id): SinglePsvUpdateRequest(new_status=PsvStatus.OPEN),
                    "4": SinglePsvUpdateRequest(new_status=PsvStatus.OPEN),  # this doesn't exist
                }
            )

            with patch("service.db.dal_psv.PsvDAL.build_query", wraps=PsvDAL.build_query) as psv_spy:
                # Perform batch update
                updated_psv = await psv_api.bulk_update_psv_status(account_id, bulk_update_request)
            actual_ids = psv_spy.call_args.kwargs["psv_filters"]._filters[0].value
            assert {int(value) for value in actual_ids} == {psv.id for psv in psv_list} | {4}

            assert len(updated_psv) == 3

            # Verify DONE status
            _filter = [Filter(field="status", op=Operator.EQ, value=PsvStatus.DONE).as_json_str()]
            done_psv = await psv_api.get_psv(account_id, f=_filter)
            assert len(done_psv.results) == 1
            assert done_psv.results[0].psv_id == psv_list[0].id

            # Verify DISMISSED status
            _filter = [Filter(field="status", op=Operator.EQ, value=PsvStatus.DISMISSED).as_json_str()]
            dismissed_psv = await psv_api.get_psv(account_id, f=_filter)
            assert len(dismissed_psv.results) == 1
            assert dismissed_psv.results[0].psv_id == psv_list[1].id
            assert dismissed_psv.results[0].dismissed_reason == "test reason"

            # Verify OPEN status
            _filter = [Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN).as_json_str()]
            open_psv = await psv_api.get_psv(account_id, f=_filter)
            assert len(open_psv.results) == 1
            assert open_psv.results[0].psv_id == psv_list[2].id

            # Verify total violations count
            total_psv = await psv_api.get_psv(account_id)
            assert len(total_psv.results) == 3
