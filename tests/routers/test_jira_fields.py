import os

import pytest
from prime_rat_logic_service_client import ProviderFieldInfoOptions, ProviderFieldType
from prime_tests import EnvSave, MockResponse, service_mocker

from client.prime_rat_logic_service_client.api.jira_fields_api import JiraFieldsApi
from service.config import get_config
from service.db import ServiceDAL
from tests.mock_utils import (
    FAKE_PROVIDER_FIELDS_INFO,
    SOURCE_ID,
    _basic_mocks,
    mock_jira_client,
)

from ..case_test_utils import create_cases

TOTAL_ISSUES = 4
ERROR_RESPONSE_GENAI = "bad response from gen-ai-service. please fix me"


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestAccountConfiguration:
    async def test_get_jira_fields_without_field_data(self, jira_fields_api: JiraFieldsApi, account_id: str):
        expected_fields = [
            ProviderFieldInfoOptions(type=ProviderFieldType.ARRAY, id="labels", name="labels"),
            ProviderFieldInfoOptions(type=ProviderFieldType.STRING, id="field_1", name="field 1"),
            ProviderFieldInfoOptions(type=ProviderFieldType.ENUM, id="status", name="status"),
            ProviderFieldInfoOptions(type=ProviderFieldType.DATE, id="created", name="created at"),
        ]
        with mock_jira_client():
            provider_fields = await jira_fields_api.get_all_provider_fields(account_id=account_id, source_id=SOURCE_ID)
            assert all(expected_field in provider_fields.fields for expected_field in expected_fields)
            assert provider_fields.total == 18

    async def test_get_jira_fields_with_field_data(self, jira_fields_api: JiraFieldsApi, account_id: str):
        field_1_options = ["1", "2", "3"]
        status_options = ["OPEN", "CLOSED"]
        expected_fields = [
            ProviderFieldInfoOptions(
                type=ProviderFieldType.STRING, id="field_1", name="field 1", options=field_1_options
            ),
            ProviderFieldInfoOptions(type=ProviderFieldType.ENUM, id="status", name="status", options=status_options),
        ]
        with mock_jira_client(), service_mocker("file-manager-service") as file_manager_mocker:
            content = {"field_1": field_1_options, "status": status_options}
            url = f"/files/{account_id}/data/source/{SOURCE_ID}/origin_id/fields_data.json"
            file_manager_mocker.get(url, MockResponse(content))
            provider_fields = await jira_fields_api.get_all_provider_fields(account_id=account_id, source_id=SOURCE_ID)
            assert all(expected_field in provider_fields.fields for expected_field in expected_fields)

    async def test_get_selected_fields(self, jira_fields_api, account_id: str):
        selected_jira_fields = ["field_1", "field_2", "labels"]
        with _basic_mocks(selected_jira_fields=selected_jira_fields):
            jira_fields = await jira_fields_api.get_selected_provider_fields(account_id=account_id, source_id=SOURCE_ID)
            assert {field.id for field in jira_fields} == set(selected_jira_fields)

    async def test_get_workroom_fields_both_container_and_non_container_views(
        self, jira_fields_api, service_dal_fixture: ServiceDAL, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=FAKE_PROVIDER_FIELDS_INFO.keys()):
            jira_fields = await jira_fields_api.get_workroom_fields(account_id=account_id)

            field_status = next(field for field in jira_fields if field.id == "provider_fields.status")
            assert set(field_status.options) == {"Open", "Closed", "In Progress"}
            assert field_status.type == ProviderFieldType.ENUM

            labels = next(field for field in jira_fields if field.id == "provider_fields.labels")
            assert set(labels.options) == {"label1", "label2", "label3", "label7", "label10"}
            assert labels.type == ProviderFieldType.ARRAY

            field6 = next(field for field in jira_fields if field.id == "provider_fields.field_6")
            assert set(field6.options) == {"option1", "option2", "option4"}
            assert field6.type == ProviderFieldType.ENUM

            field_array_optional = next(
                field for field in jira_fields if field.id == "provider_fields.field_array_optional"
            )
            assert set(field_array_optional.options) == {"optional1", "optional2", "null"}
            assert field_array_optional.type == ProviderFieldType.ARRAY

            field_status = next(field for field in jira_fields if field.id == "provider_fields.issuetype")
            assert set(field_status.options) == {"Epic", "Task", "Subtask"}
            assert field_status.type == ProviderFieldType.ENUM

            # make sure none string or array are been returned
            field_1 = next(field for field in jira_fields if field.id == "provider_fields.field_1")
            assert field_1.options == []
            assert field_1.type == ProviderFieldType.STRING
            field2_options = next(field for field in jira_fields if field.id == "provider_fields.field_2").options
            assert field2_options == []
            field5_options = next(field for field in jira_fields if field.id == "provider_fields.field_5").options
            assert field5_options == []

            with EnvSave():
                os.environ["MAX_FIELD_OPTIONS_TO_RETURN"] = "2"
                get_config.cache_clear()
                jira_fields = await jira_fields_api.get_workroom_fields(account_id=account_id)
                field_6_options = next(field for field in jira_fields if field.id == "provider_fields.field_6").options
                assert set(field_6_options) == {"option1", "option2"}

        with _basic_mocks():
            # make sure no exception in case of empyt selected fields
            await jira_fields_api.get_workroom_fields(account_id=account_id)

        get_config.cache_clear()

    async def test_get_workroom_fields_non_container_view(
        self, jira_fields_api, service_dal_fixture: ServiceDAL, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=FAKE_PROVIDER_FIELDS_INFO.keys()):
            jira_fields = await jira_fields_api.get_workroom_fields(account_id=account_id, is_container_view=False)

            field_status = next(field for field in jira_fields if field.id == "provider_fields.creator")
            assert set(field_status.options) == {
                "Jane Doe the 2nd",
                "Jane Doe the 55nd",
                "Jane Smith the 4th",
                "John Smith the 3rd",
            }
            assert field_status.type == ProviderFieldType.ENUM

            field6 = next(field for field in jira_fields if field.id == "provider_fields.field_6")
            assert set(field6.options) == {"option2", "option4"}
            assert field6.type == ProviderFieldType.ENUM

            field_array_optional = next(
                field for field in jira_fields if field.id == "provider_fields.field_array_optional"
            )
            assert field_array_optional.options == ["null"]
            assert field_array_optional.type == ProviderFieldType.ARRAY

            field_status = next(field for field in jira_fields if field.id == "provider_fields.status")
            assert set(field_status.options) == {"Open", "Closed", "In Progress"}
            assert field_status.type == ProviderFieldType.ENUM

            field_status = next(field for field in jira_fields if field.id == "provider_fields.issuetype")
            assert set(field_status.options) == {"Epic", "Task", "Subtask"}
            assert field_status.type == ProviderFieldType.ENUM

            labels = next(field for field in jira_fields if field.id == "provider_fields.labels")
            assert set(labels.options) == {"label3", "label7", "label10"}
            assert labels.type == ProviderFieldType.ARRAY

            # make sure none string or array are been returned
            field_1 = next(field for field in jira_fields if field.id == "provider_fields.field_1")
            assert field_1.options == []
            assert field_1.type == ProviderFieldType.STRING
            field2_options = next(field for field in jira_fields if field.id == "provider_fields.field_2").options
            assert field2_options == []
            field5_options = next(field for field in jira_fields if field.id == "provider_fields.field_5").options
            assert field5_options == []

            with EnvSave():
                os.environ["MAX_FIELD_OPTIONS_TO_RETURN"] = "2"
                get_config.cache_clear()
                jira_fields = await jira_fields_api.get_workroom_fields(account_id=account_id, is_container_view=False)
                labels = next(field for field in jira_fields if field.id == "provider_fields.labels")
                assert set(labels.options) == {"label10", "label3"}
                assert labels.type == ProviderFieldType.ARRAY

            get_config.cache_clear()

    async def test_get_workroom_fields_with_container_view(
        self, jira_fields_api, service_dal_fixture: ServiceDAL, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=FAKE_PROVIDER_FIELDS_INFO.keys()):
            jira_fields = await jira_fields_api.get_workroom_fields(account_id=account_id, is_container_view=True)

            field_status = next(field for field in jira_fields if field.id == "provider_fields.creator")
            assert set(field_status.options) == {"Jane Doe the 2nd", "John Doe"}
            assert field_status.type == ProviderFieldType.ENUM

            field6 = next(field for field in jira_fields if field.id == "provider_fields.field_6")
            assert set(field6.options) == {"option2", "option1"}
            assert field6.type == ProviderFieldType.ENUM

            field_status = next(field for field in jira_fields if field.id == "provider_fields.status")
            assert set(field_status.options) == {"Open", "Closed"}
            assert field_status.type == ProviderFieldType.ENUM

            labels = next(field for field in jira_fields if field.id == "provider_fields.labels")
            assert set(labels.options) == {"label2", "label3", "label1"}
            assert labels.type == ProviderFieldType.ARRAY

            field_status = next(field for field in jira_fields if field.id == "provider_fields.issuetype")
            assert set(field_status.options) == {"Epic", "Task"}
            assert field_status.type == ProviderFieldType.ENUM

            # make sure none string or array are been returned
            field_1 = next(field for field in jira_fields if field.id == "provider_fields.field_1")
            assert field_1.options == []
            assert field_1.type == ProviderFieldType.STRING
            field2_options = next(field for field in jira_fields if field.id == "provider_fields.field_2").options
            assert field2_options == []
            field5_options = next(field for field in jira_fields if field.id == "provider_fields.field_5").options
            assert field5_options == []

            with EnvSave():
                os.environ["MAX_FIELD_OPTIONS_TO_RETURN"] = "1"
                get_config.cache_clear()
                jira_fields = await jira_fields_api.get_workroom_fields(account_id=account_id, is_container_view=True)
                labels = next(field for field in jira_fields if field.id == "provider_fields.labels")
                assert set(labels.options) == {"label2"}
                assert labels.type == ProviderFieldType.ARRAY

        get_config.cache_clear()
