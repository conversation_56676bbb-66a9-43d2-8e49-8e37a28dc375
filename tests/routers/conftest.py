import pytest
from prime_rat_logic_service_client import (
    <PERSON>s<PERSON><PERSON>,
    Containers<PERSON><PERSON>,
    IssuesApi,
    JiraFieldsApi,
    JobsApi,
    LlmContextApi,
    PsvApi,
    TrendsApi,
)
from prime_tests import create_async_test_service_client
from prime_tests.fastapi_test_client import AsyncAppWrapper

from service.service_app import app


@pytest.fixture(scope="session")
def app_wrapper():
    return AsyncAppWrapper(app)


@pytest.fixture(scope="session")
async def issues_api(app_wrapper) -> IssuesApi:
    return create_async_test_service_client(IssuesApi, app_wrapper)


@pytest.fixture(scope="session")
async def jobs_api(app_wrapper) -> JobsApi:
    return create_async_test_service_client(JobsApi, app_wrapper)


@pytest.fixture(scope="session")
async def cases_api(app_wrapper) -> CasesApi:
    return create_async_test_service_client(CasesApi, app_wrapper)


@pytest.fixture(scope="session")
async def containers_api(app_wrapper) -> ContainersApi:
    return create_async_test_service_client(ContainersApi, app_wrapper)


@pytest.fixture(scope="session")
async def jira_fields_api(app_wrapper) -> JiraFieldsApi:
    return create_async_test_service_client(JiraFieldsApi, app_wrapper)


@pytest.fixture(scope="session")
async def trends_api(app_wrapper) -> TrendsApi:
    return create_async_test_service_client(TrendsApi, app_wrapper)


@pytest.fixture(scope="session")
async def psv_api(app_wrapper) -> PsvApi:
    return create_async_test_service_client(PsvApi, app_wrapper)


@pytest.fixture(scope="session")
async def llm_context_api(app_wrapper: AsyncAppWrapper) -> LlmContextApi:
    return create_async_test_service_client(LlmContextApi, app_wrapper)
