import ast
from datetime import UTC, datetime, timedelta

import pytest
from prime_rat_logic_service_client import (
    CasesApi,
    CaseStatus,
    ConfidenceScoreLevel,
    PaginationResponseExternalCaseWorkroom,
    RiskFactorLevel,
    RiskScoreCategory,
)

from service.db import <PERSON><PERSON><PERSON>
from service.db.tables.cases import CaseTable
from service.logic.filters_and_sort.cases import CONTAINER_FILTER_NAME
from service.models.filters_and_sort import Filter, Operator
from tests.case_test_utils import add_issue_analysis_to_case, create_case, create_cases, get_external_case_summary
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    FAKE_PROVIDER_FIELDS_DATA,
    SOURCE_ID,
    TESTS_ISSUES,
    TOTAL_TESTS_ISSUES,
    _basic_mocks,
)


async def _validate_cases(expected_cases, cases_api: CasesApi, **kwargs):
    response = await cases_api.get_cases_for_account(ACCOUNT_ID_CONTEXT.get(), **kwargs)
    assert response.results is not None
    assert len(response.results) == len(expected_cases)
    for i in range(len(expected_cases)):
        assert expected_cases[i].model_dump() == response.results[i].model_dump()
        assert expected_cases[i].issue_id == response.results[i].issue_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCasesFilter:
    @pytest.mark.parametrize(
        "filter_name, score_test",
        [
            ("confidentiality", 2),
            ("integrity", 4),
            ("availability", 5),
        ],
    )
    async def test_get_cases_filter_classification_score(
        self, service_dal_fixture: ServiceDAL, filter_name, score_test, cases_api: CasesApi, account_id: str
    ):
        kwargs = {filter_name: score_test}
        case = await create_case(
            service_dal_fixture,
            TESTS_ISSUES[0],
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
            **kwargs,
        )
        with _basic_mocks():
            expected_case = await get_external_case_summary(case, service_dal_fixture)
            f = Filter(field=filter_name, value=str(score_test), op=Operator.EQ)
            response = await cases_api.get_cases_for_account(account_id, f=[f.as_json_str()])
            assert response.results is not None and len(response.results) == 1
            assert expected_case.model_dump() == response.results[0].model_dump()

    @pytest.mark.parametrize(
        "score_level_list, expected_cases",
        [
            ([RiskFactorLevel.HIGH], 4),
            ([RiskFactorLevel.LOW], 2),
            ([RiskFactorLevel.MEDIUM], 4),
            ([RiskFactorLevel.MEDIUM, RiskFactorLevel.HIGH], 8),
            ([RiskFactorLevel.LOW, RiskFactorLevel.MEDIUM, RiskFactorLevel.HIGH], TOTAL_TESTS_ISSUES),
        ],
    )
    async def test_get_cases_filter_integrity_level(
        self, score_level_list, expected_cases, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        integrity_scores = [1, 2, 8, 9, 7, 5, 6, 9, 3, 4]
        cases = await create_cases(service_dal_fixture)
        for integrity, case in zip(integrity_scores, cases, strict=False):
            issue_analysis = await service_dal_fixture.issues_analysis_dal.get(
                case.account_id, case.source_id, case.issue_id
            )
            issue_analysis.integrity = integrity
            await service_dal_fixture.session.commit()
        with _basic_mocks():
            cases = await cases_api.get_cases_for_account(
                account_id,
                f=[
                    Filter(
                        field="integrity_level",
                        value=score_level_list,
                        op=Operator.EQ,
                    ).as_url_str(),
                ],
            )
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "score_level_list, expected_cases",
        [
            ([ConfidenceScoreLevel.HIGH], 2),
            ([ConfidenceScoreLevel.LOW], 3),
            ([ConfidenceScoreLevel.MEDIUM], 5),
            ([ConfidenceScoreLevel.MEDIUM, ConfidenceScoreLevel.HIGH], 7),
            ([ConfidenceScoreLevel.LOW, ConfidenceScoreLevel.MEDIUM, ConfidenceScoreLevel.HIGH], TOTAL_TESTS_ISSUES),
        ],
    )
    async def test_get_cases_filter_confidence_level(
        self, score_level_list, expected_cases, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        extra = [{"confidence": rank} for rank in [20, 50, 60, 90, 10, 25, 45, 55, 65, 75]]
        await create_cases(service_dal_fixture, account_id, SOURCE_ID, extra=extra)
        with _basic_mocks():
            confidence_filter = Filter(field="confidence_level", value=score_level_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[confidence_filter.as_json_str()])
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "value, excepted_cases",
        [
            (CaseStatus.DISMISSED.value, 1),
            (CaseStatus.DONE.value, 0),
            ("dISMISSED", 1),
            ([CaseStatus.DISMISSED.value, CaseStatus.OPEN.value], 2),
            ([CaseStatus.DISMISSED.value, "Open"], 2),
            ([CaseStatus.DONE.value, CaseStatus.OPEN.value], 1),
        ],
    )
    async def test_get_cases_filter_status(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, value, excepted_cases, account_id: str
    ):
        cases = await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=2)
        with _basic_mocks():
            expected_case = await get_external_case_summary(cases[0], service_dal_fixture)
            response = await cases_api.get_cases_for_account(account_id)
            assert response.results is not None and len(response.results) == 2
            assert expected_case.model_dump() == response.results[0].model_dump()

            cases_dal = service_dal_fixture.cases_dal
            await cases_dal.update_case_status(account_id, SOURCE_ID, cases[0].issue_id, CaseStatus.DISMISSED)
            await add_issue_analysis_to_case(service_dal_fixture, account_id, cases[0].id)
            open_case_filter = Filter(field="status", value=CaseStatus.OPEN.value)
            cases = await cases_api.get_cases_for_account(account_id, f=[open_case_filter.as_url_str()])
            assert len(cases.results) == 1

            _status_filter = Filter(field="status", value=value)
            cases = await cases_api.get_cases_for_account(account_id, f=[_status_filter.as_url_str()])
            assert len(cases.results) == excepted_cases

            _status_filter = Filter(field="status", value=[CaseStatus.DISMISSED.value, CaseStatus.OPEN.value])
            cases = await cases_api.get_cases_for_account(account_id, f=[_status_filter.as_url_str()])
            assert len(cases.results) == 2

            _status_filter = Filter(field="status", value=[CaseStatus.DONE.value, CaseStatus.OPEN.value])
            cases = await cases_api.get_cases_for_account(account_id, f=[_status_filter.as_url_str()])
            assert len(cases.results) == 1

    async def test_get_cases_filter_between_dates_created(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        now = datetime.now(tz=UTC)
        with _basic_mocks():
            cases = await create_cases(service_dal_fixture, account_id, SOURCE_ID, count=3)

            date_1 = now + timedelta(minutes=1)
            date_2 = date_1 - timedelta(minutes=5)
            date_3 = date_1 - timedelta(minutes=10)

            cases[0].created_at = date_2 - timedelta(minutes=1)  # to minimize exact time effect
            cases[1].created_at = date_3 - timedelta(minutes=1)  # to minimize exact time effect
            await service_dal_fixture.session.commit()

            tomorrow = now + timedelta(days=1)
            yesterday = now - timedelta(days=1)
            pairs = [
                (date_2, date_1, 1),
                (date_3, date_1, 2),
                (yesterday, tomorrow, 3),
                (tomorrow, yesterday, 0),
            ]
            for _from, _to, expected_cases in pairs:
                _created_at_filter = Filter(field="created_at", value=[str(_from), str(_to)], op=Operator.BETWEEN)
                cases = await cases_api.get_cases_for_account(
                    account_id,
                    f=[
                        _created_at_filter.as_url_str(),
                    ],
                )
                assert len(cases.results) == expected_cases

    async def test_get_cases_filter_parent_id(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            _filter = Filter(field="parent_id", value="ISSUE-1", op=Operator.EQ)
            cases_view = await cases_api.get_cases_for_account(account_id, f=[_filter.as_url_str()])
            assert len(cases_view.results) == 4
            assert cases_view.results[0].issue_id == "ISSUE-2"
            case: CaseTable = await service_dal_fixture.cases_dal.get_case(
                account_id, SOURCE_ID, cases_view.results[0].issue_id
            )
            assert case.parent_issue_id == "ISSUE-1"

            assert cases_view.results[1].issue_id == "ISSUE-3"
            assert cases_view.results[1].parents == ["ISSUE-2", "ISSUE-1"]

            _filter = Filter(field="parent_id", value="ISSUE-2", op=Operator.EQ)
            no_cases_view = await cases_api.get_cases_for_account(account_id, f=[_filter.as_url_str()])
            assert len(no_cases_view.results) == 1
            assert no_cases_view.results[0].issue_id == "ISSUE-3"

            _filter = Filter(field="parent_id", value="ISSUE-1", op=Operator.EQ)
            no_cases_view = await cases_api.get_cases_for_account(account_id, f=[_filter.as_url_str()])
            assert len(no_cases_view.results) == 4

            # Test non container with parent it issue 1
            _filter = Filter(field="parent_id", value="ISSUE-1", op=Operator.EQ)
            _filter_container = Filter(field=CONTAINER_FILTER_NAME, value="False", op=Operator.EQ)
            no_cases_view = await cases_api.get_cases_for_account(
                account_id, f=[_filter.as_url_str(), _filter_container.as_url_str()]
            )
            assert len(no_cases_view.results) == 3

            # Test container with parent it issue 1
            _filter = Filter(field="parent_id", value="ISSUE-1", op=Operator.EQ)
            _filter_container = Filter(field=CONTAINER_FILTER_NAME, value="True", op=Operator.EQ)
            no_cases_view = await cases_api.get_cases_for_account(
                account_id, f=[_filter.as_url_str(), _filter_container.as_url_str()]
            )
            assert len(no_cases_view.results) == 1
            assert no_cases_view.results[0].issue_id == "ISSUE-2"

    @pytest.mark.parametrize(
        "threshold,operator, expected_cases",
        [
            (0, Operator.GT, 10),
            (25, Operator.GT, 2),
            (30, Operator.GTE, 2),
            (80, Operator.LT, 10),
            (5, Operator.LT, 0),
            (10, Operator.LTE, 1),
        ],
    )
    async def test_get_cases_filter_provider_field_number(
        self, threshold, operator, expected_cases, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks(selected_jira_fields=["field_2"]):
            await create_cases(service_dal_fixture)
            _field_2_filter = Filter(field="provider_fields.field_2", value=f"{threshold}", op=operator)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field_2_filter.as_url_str()])
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "threshold,operator, expected_cases",
        [
            ("2025-01-01 00:00:00.0", Operator.LT, 10),
            ("2023-01-01 00:00:00.0", Operator.LT, 2),
            ("2023-01-01 11:24:33.091271", Operator.LTE, 9),
            ("2020-01-01 00:00:0.0", Operator.GT, 10),
            ("2022-06-01 00:00:0.0", Operator.GT, 8),
            ("2023-01-01 11:24:33.091271", Operator.GTE, 8),
        ],
    )
    async def test_get_cases_filter_provider_field_date(
        self, threshold, operator, expected_cases, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks(selected_jira_fields=["field_5"]):
            await create_cases(service_dal_fixture)
            _field_5_filter = Filter(field="provider_fields.field_5", value=f"{threshold}", op=operator)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field_5_filter.as_json_str()])
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "days_offset_from_now,operator, expected_cases",
        [
            (1, Operator.LT, 10),
            (0, Operator.LT, 10),
            (0, Operator.LTE, 10),
            (-1, Operator.LT, 0),
            (-1, Operator.GT, 10),
            (-1, Operator.GTE, 10),
        ],
    )
    async def test_get_cases_filter_provider_field_created(
        self,
        days_offset_from_now,
        operator,
        expected_cases,
        service_dal_fixture: ServiceDAL,
        cases_api: CasesApi,
        account_id: str,
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)
            today = datetime.now(tz=UTC)
            threshold = today + timedelta(days=days_offset_from_now)
            _created_at_filter = Filter(field="created_at", value=str(threshold), op=operator)
            cases = await cases_api.get_cases_for_account(account_id, f=[_created_at_filter.as_url_str()])
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "categories_list, expected_cases",
        [
            ([RiskScoreCategory.MONITOR], 3),
            ([RiskScoreCategory.ANALYZE], 4),
            ([RiskScoreCategory.INTERVENE], 3),
            ([RiskScoreCategory.NONE], 0),
            ([RiskScoreCategory.MONITOR, RiskScoreCategory.ANALYZE], 7),
            ([RiskScoreCategory.MONITOR, RiskScoreCategory.ANALYZE, RiskScoreCategory.INTERVENE], 10),
            ([RiskScoreCategory.MONITOR, RiskScoreCategory.INTERVENE, RiskScoreCategory.NONE], 6),
        ],
    )
    async def test_get_cases_filter_risk_score(
        self, categories_list, expected_cases, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        extra = [{"risk_score": rank} for rank in [10, 50, 60, 90, 10, 50, 60, 90, 20, 100]]
        await create_cases(service_dal_fixture, account_id, SOURCE_ID, extra=extra)
        with _basic_mocks():
            _risk_score_filter = Filter(field="risk_score_category", value=categories_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_risk_score_filter.as_url_str()])
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "labels_list, expected_cases_len",
        [
            (["label1"], 1),
            (["label2"], 2),
            (["label8"], 0),
            (["label1", "label3"], 8),
        ],
    )
    async def test_get_cases_filter_provider_field_array(
        self, labels_list, expected_cases_len, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=["labels"]):
            _labels_filter = Filter(field="provider_fields.labels", value=labels_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_labels_filter.as_url_str()])
            assert len(cases.results) == expected_cases_len

        with _basic_mocks(selected_jira_fields=["labels"]):
            _labels_filter = Filter(field="provider_fields.labels", value=labels_list, op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_labels_filter.as_url_str()])
            assert len(cases.results) == 10 - expected_cases_len

    @pytest.mark.parametrize(
        "value_list, expected_cases",
        [
            (["True"], 7),
            (["False"], 2),
            (["True", "False"], 10),
        ],
    )
    async def test_get_cases_filter_provider_field_boolean(
        self, value_list, expected_cases, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks(selected_jira_fields=["field_4"]):
            await create_cases(service_dal_fixture)
            _field_4_filter = Filter(field="provider_fields.field_4", value=value_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field_4_filter.as_url_str()])
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "from_date, to_date, expected_cases",
        [
            ("2024-01-01 00:00:0.0", "2025-01-01 00:00:0.0", 1),
            ("2020-01-01 00:00:0.0", "2023-07-01 00:00:0.0", 9),
            ("2020-01-01 00:00:0.0", "2020-07-01 00:00:0.0", 0),
        ],
    )
    async def test_get_cases_filter_between_provider_field_date(
        self, from_date, to_date, expected_cases, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks(selected_jira_fields=["field_5"]):
            await create_cases(service_dal_fixture)
            _field_5_filter = Filter(field="provider_fields.field_5", value=[from_date, to_date], op=Operator.BETWEEN)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field_5_filter.as_url_str()])
            assert len(cases.results) == expected_cases

    @pytest.mark.parametrize(
        "option_list, expected_cases_len",
        [
            (["option1"], 1),
            (["option2"], 8),
            (["option1", "option4"], 2),
            (["option3"], 0),
        ],
    )
    async def test_get_cases_filter_provider_field_enum(
        self, option_list, expected_cases_len, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=["field_6"]):
            _field6_filter = Filter(field="provider_fields.field_6", value=option_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field6_filter.as_url_str()])
            assert len(cases.results) == expected_cases_len

            _field6_filter = Filter(field="provider_fields.field_6", value=option_list, op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field6_filter.as_url_str()])
            assert len(cases.results) == TOTAL_TESTS_ISSUES - expected_cases_len

    async def test_get_cases_filter_provider_special_cases(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=["field_6"]):
            _field6_filter = Filter(field="provider_fields.field_6", value=[], op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field6_filter.as_url_str()])
            assert len(cases.results) == 10

            _field6_filter = Filter(field="provider_fields.field_6", value=[], op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field6_filter.as_url_str()])
            assert len(cases.results) == 10

    @pytest.mark.parametrize(
        "string_list, expected_cases_len",
        [
            (["lets"], 2),
            (["issue"], 10),
            ("issue 2", 1),
            ("and more", 1),
        ],
    )
    async def test_get_cases_filter_title(
        self, string_list, expected_cases_len, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks():
            _title_filter = Filter(field="title", value=string_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_title_filter.as_url_str()])
            assert len(cases.results) == expected_cases_len

            _title_filter = Filter(field="title", value=string_list, op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_title_filter.as_url_str()])
            assert len(cases.results) == TOTAL_TESTS_ISSUES - expected_cases_len

    async def test_get_cases_filter_title_url(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks():
            cases = await cases_api.get_cases_for_account(
                account_id, f=['{"field":"title","value":"%26 and more","op":"eq"}']
            )
            assert len(cases.results) == 1

    @pytest.mark.parametrize(
        "string_list, expected_cases_len",
        [
            (["ISSUE-1"], 1),
            (["issue-1"], 1),
            (["ISSUE-10"], 1),
            (["1"], 1),
            (["10"], 1),
            (["8"], 1),
            (["ISSUE-"], 10),
            (["13"], 0),
            (["0"], 0),
        ],
    )
    async def test_get_cases_filter_issue_id(
        self, string_list, expected_cases_len, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        await create_cases(service_dal_fixture, account_id + "1", SOURCE_ID + 1)

        with _basic_mocks():
            _issue_id_filter = Filter(field="issue_id", value=string_list[0], op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_issue_id_filter.as_url_str()])
            assert len(cases.results) == expected_cases_len
            for case in cases.results:
                assert string_list[0].lower() in case.issue_id.lower()

    async def test_get_cases_filter_issue_id_no_sec(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_case(
            service_dal_fixture,
            "ISSUE-1",
            with_analysis=True,
            classification=False,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0].copy(),
        )
        with _basic_mocks():
            _filter = Filter(field="issue_id", value="ISSUE-1", op=Operator.EQ)
            _no_sec_filter = Filter(field="classification", value=["false", "true"], op=Operator.EQ)
            _aut_filter = Filter(field="is_automated", value=["false", "true"], op=Operator.EQ)
            filters = [_filter.as_url_str(), _no_sec_filter.as_url_str(), _aut_filter.as_url_str()]
            cases = await cases_api.get_cases_for_account(account_id, f=filters)

        assert [case.issue_id for case in cases.results] == ["ISSUE-1"]

    @pytest.mark.parametrize(
        "string_list, expected_cases_len",
        [
            (["never"], 2),
            (["give you up"], 6),
            (["you"], 7),
            (["momo"], 0),
        ],
    )
    async def test_get_cases_filter_provider_field_string(
        self, string_list, expected_cases_len, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=["field_1"]):
            _field1_filter = Filter(field="provider_fields.field_1", value=string_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field1_filter.as_url_str()])

            assert len(cases.results) == expected_cases_len

            _field1_filter = Filter(field="provider_fields.field_1", value=string_list, op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field1_filter.as_url_str()])
            assert len(cases.results) == TOTAL_TESTS_ISSUES - expected_cases_len

    @pytest.mark.parametrize(
        "string_list, expected_cases_len",
        [
            (["optional1"], 1),
            ("optional1", 1),
            ("", 6),
        ],
    )
    async def test_get_cases_filter_provider_field_optional(
        self, string_list, expected_cases_len, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=["field_optional"]):
            _field_optional_filter = Filter(field="provider_fields.field_optional", value=string_list, op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field_optional_filter.as_url_str()])

            assert len(cases.results) == expected_cases_len

            _field_optional_filter = Filter(field="provider_fields.field_optional", value=string_list, op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_field_optional_filter.as_url_str()])
            assert (
                len(cases.results) == TOTAL_TESTS_ISSUES - expected_cases_len - 1
            )  # one case that doesn't have the field at all

    async def test_get_cases_filter_provider_field_optional_exist(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks(selected_jira_fields=["field_optional", "field_array_optional"]):
            _filter = Filter(field="provider_fields.field_optional", value="True", op=Operator.EXIST)
            cases = await cases_api.get_cases_for_account(account_id, f=[_filter.as_url_str()])
            number_of_issues_with_field_optional = 2
            assert len(cases.results) == number_of_issues_with_field_optional
            assert all(cases.provider_fields["field_optional"] for cases in cases.results)

            _filter = Filter(field="provider_fields.field_optional", value="False", op=Operator.EXIST)
            cases = await cases_api.get_cases_for_account(account_id, f=[_filter.as_url_str()])
            assert len(cases.results) == TOTAL_TESTS_ISSUES - number_of_issues_with_field_optional
            values = [cases.provider_fields.get("field_optional") for cases in cases.results]
            assert all(not value or value == "None" for value in values)

            _filter = Filter(field="provider_fields.field_array_optional", value="True", op=Operator.EXIST)
            cases = await cases_api.get_cases_for_account(account_id, f=[_filter.as_url_str()])
            assert len(cases.results) == 1
            assert all(cases.provider_fields.get("field_array_optional") for cases in cases.results)

            _filter = Filter(field="provider_fields.field_array_optional", value="False", op=Operator.EXIST)
            cases = await cases_api.get_cases_for_account(account_id, f=[_filter.as_url_str()])
            assert len(cases.results) == 9
            values = [cases.provider_fields.get("field_array_optional") for cases in cases.results]
            assert all(not value or value in {"None", "[]"} for value in values)

    async def test_get_cases_filter_labels(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        cases = await create_cases(service_dal_fixture, account_id, SOURCE_ID)
        with _basic_mocks():
            labels_to_set = ["momo", "momi", "momis", "label1"]
            await cases_api.set_labels(cases[0].account_id, cases[0].source_id, cases[0].issue_id, labels_to_set)
            await cases_api.set_labels(cases[1].account_id, cases[1].source_id, cases[1].issue_id, ["label1"])

            labels_filter = Filter(field="labels", value=["momo"], op=Operator.EQ).as_json_str()
            response = await cases_api.get_cases_for_account(account_id, f=[labels_filter])
            assert response.results[0].issue_id == cases[0].issue_id
            assert len(response.results) == 1

            labels_filter = Filter(field="labels", value=["momo", "nothing"], op=Operator.EQ).as_json_str()
            response = await cases_api.get_cases_for_account(account_id, f=[labels_filter])
            assert response.results[0].issue_id == cases[0].issue_id
            assert len(response.results) == 1

            labels_filter = Filter(field="labels", value=["nothing"], op=Operator.EQ).as_json_str()
            response = await cases_api.get_cases_for_account(account_id, f=[labels_filter])
            assert len(response.results) == 0

            labels_filter = Filter(field="labels", value=["nothing"], op=Operator.NE).as_json_str()
            response = await cases_api.get_cases_for_account(account_id, f=[labels_filter])
            assert len(response.results) == len(cases)

            labels_filter = Filter(field="labels", value=["true"], op=Operator.EXIST).as_json_str()
            response = await cases_api.get_cases_for_account(account_id, f=[labels_filter])
            assert len(response.results) == 2

            labels_filter = Filter(field="labels", value=["false"], op=Operator.EXIST).as_json_str()
            response = await cases_api.get_cases_for_account(account_id, f=[labels_filter])
            assert len(response.results) == len(cases) - 2

    async def test_get_cases_filter_labels_mixed_op(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        case = await create_case(
            service_dal_fixture,
            "PROJ-1",
            with_analysis=True,
            provider_fields=FAKE_PROVIDER_FIELDS_DATA[0],
        )
        labels_to_set = ["test-label1", "test-label2", "test-label3", "test-label4"]
        with _basic_mocks():
            await cases_api.set_labels(case.account_id, case.source_id, case.issue_id, labels_to_set)
            filters = [
                Filter(field="labels", value=["test-label1", "no-label"], op=Operator.EQ),
                Filter(field="labels", value=["fake-label"], op=Operator.NE),
            ]
            response: PaginationResponseExternalCaseWorkroom = await cases_api.get_cases_for_account(
                account_id, f=[f.as_json_str() for f in filters]
            )
            assert response.results is not None and len(response.results) == 1

            filters = [
                Filter(field="labels", value=["test-label1"], op=Operator.EQ),
                Filter(field="labels", value=["test-label1", "no-label"], op=Operator.NE),
            ]
            response: PaginationResponseExternalCaseWorkroom = await cases_api.get_cases_for_account(
                account_id, f=[f.as_json_str() for f in filters]
            )
            assert response.results is not None and len(response.results) == 0

    @pytest.mark.parametrize(
        "issuetype,expected_cases_len",
        [
            ["Epic", 4],
            ["Subtask", 1],
            ["Task", 5],
        ],
    )
    async def test_get_cases_filter_issue_type(
        self, issuetype, expected_cases_len, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        await create_cases(service_dal_fixture)
        with _basic_mocks():
            _issuetype_filter = Filter(field="provider_fields.issuetype", value=[issuetype], op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_issuetype_filter.as_url_str()])
            assert len(cases.results) == expected_cases_len

            _issuetype_filter = Filter(field="provider_fields.issuetype", value=[issuetype], op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_issuetype_filter.as_url_str()])
            assert len(cases.results) == TOTAL_TESTS_ISSUES - expected_cases_len

    async def test_multiple_filter(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        await create_cases(service_dal_fixture)
        with _basic_mocks():
            _risk_score_filters = [
                Filter(field="risk_score_category", value=RiskScoreCategory.MONITOR).as_url_str(),
                Filter(field="risk_score_category", value=RiskScoreCategory.ANALYZE).as_url_str(),
            ]
            cases = await cases_api.get_cases_for_account(account_id, f=_risk_score_filters)
            assert len(cases.results) == 0

            _issue_type_filters = [
                Filter(field="provider_fields.issuetype", value="Epic").as_url_str(),
                Filter(field="provider_fields.issuetype", value="Task").as_url_str(),
            ]
            cases = await cases_api.get_cases_for_account(account_id, f=_risk_score_filters)
            assert len(cases.results) == 0

    async def test_get_cases_with_active_sprint(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)
            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field="active_sprint", value="True", op=Operator.EQ).as_json_str()],
            )
            assert len(account_cases.results) == 1

            sprint = ast.literal_eval(account_cases.results[0].provider_fields["sprint"])
            for sprint_item in sprint:
                assert sprint_item["state"] == "active"

    async def test_get_cases_with_non_active_sprint(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field="active_sprint", value="False", op=Operator.EQ).as_json_str()],
            )
            assert len(account_cases.results) == 9

            sprint = ast.literal_eval(account_cases.results[0].provider_fields["sprint"])

            for sprint_item in sprint:
                assert sprint_item["state"] != "active"

    async def test_get_cases_active_and_non_active_sprint(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field="active_sprint", value=["True", "False"], op=Operator.EQ).as_json_str()],
            )
            assert len(account_cases.results) == 10

    async def test_get_cases_filter_parent(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        await create_cases(service_dal_fixture, account_id, SOURCE_ID)
        with _basic_mocks():
            _parent_filter = Filter(field="has_parent", value="True", op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_parent_filter.as_url_str()])
            expected_ids = ["ISSUE-2", "ISSUE-3", "ISSUE-5", "ISSUE-6", "ISSUE-9", "ISSUE-10"]
            assert [case.issue_id for case in cases.results] == expected_ids

            _parent_filter = Filter(field="has_parent", value="False", op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_parent_filter.as_url_str()])
            assert [case.issue_id for case in cases.results] == ["ISSUE-1", "ISSUE-4", "ISSUE-7", "ISSUE-8"]

            _parent_filter = Filter(field="has_parent", value="False", op=Operator.EQ)
            _filter_container = Filter(field=CONTAINER_FILTER_NAME, value="False", op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(
                account_id, f=[_parent_filter.as_url_str(), _filter_container.as_url_str()]
            )
            assert [case.issue_id for case in cases.results] == ["ISSUE-4", "ISSUE-7"]

    async def test_get_cases_filter_case_id(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        new_cases = await create_cases(service_dal_fixture, account_id, SOURCE_ID)
        with _basic_mocks():
            _cases_filter = Filter(field="id", value=str(new_cases[0].id), op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_cases_filter.as_url_str()])
            assert [case.case_id for case in cases.results] == [new_cases[0].id]

            _cases_filter = Filter(field="id", value=[str(new_cases[0].id), str(new_cases[1].id)], op=Operator.EQ)
            cases = await cases_api.get_cases_for_account(account_id, f=[_cases_filter.as_url_str()])
            assert [case.case_id for case in cases.results] == [new_cases[0].id, new_cases[1].id]

            _cases_filter = Filter(field="id", value=str(new_cases[-1].id), op=Operator.NE)
            cases = await cases_api.get_cases_for_account(account_id, f=[_cases_filter.as_url_str()])
            assert [case.case_id for case in cases.results] == [case.id for case in new_cases[:-1]]
