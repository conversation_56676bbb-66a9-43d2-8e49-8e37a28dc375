import pytest
from prime_rat_logic_service_client import Cases<PERSON><PERSON>

from service.db import <PERSON><PERSON><PERSON>
from service.logic.filters_and_sort.cases import CONTAINER_FILTER_NAME
from service.models.filters_and_sort import Filter, Operator
from tests.case_test_utils import create_cases
from tests.mock_utils import _basic_mocks


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestGetCasesContainers:
    async def test_get_cases_default_container_filter(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
            )
            assert len(account_cases.results) == 10

    async def test_get_cases_only_non_containers_upper_case(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field=CONTAINER_FILTER_NAME, value=["False"], op=Operator.EQ).as_json_str()],
            )

            assert len(account_cases.results) == 7

    async def test_get_cases_only_non_containers_lower_case(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field=CONTAINER_FILTER_NAME, value=["False"], op=Operator.EQ).as_json_str()],
            )

            assert len(account_cases.results) == 7

    async def test_get_cases_only_containers(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture)

            account_cases = await cases_api.get_cases_for_account(
                account_id,
                f=[Filter(field=CONTAINER_FILTER_NAME, value=["True"], op=Operator.EQ).as_json_str()],
            )
            assert len(account_cases.results) == 3
