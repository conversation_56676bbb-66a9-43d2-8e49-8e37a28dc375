import pytest
from prime_rat_logic_service_client import Cases<PERSON><PERSON>

from service.db import <PERSON><PERSON><PERSON>
from service.db.tables.cases import CaseTable
from service.k8s_jobs.classification_job.update_risk_score_logic import UpdateRiskScoreLogic
from service.logic.issues_graph import GraphGenerator
from service.models.filters_and_sort.filtering import Filter, Operator
from service.models.filters_and_sort.sorting import SortDirection, SortField
from tests.case_test_utils import (
    create_case,
    create_cases,
)
from tests.mock_utils import ACCOUNT_ID_CONTEXT, FAKE_PROVIDER_FIELDS_DATA, SOURCE_ID, TASK_TYPE, _basic_mocks

SCORES = [10, 20, 30, 40, 50, 60, 70, 75, 80, 85]
RISK_SCORES = {data.get("id", ""): score for data, score in zip(FAKE_PROVIDER_FIELDS_DATA, SCORES, strict=False)}


async def _update_risk_scores_for_source(service_dal_fixture: ServiceDAL, account_id: str | None = None) -> None:
    generator = GraphGenerator(service_dal_fixture, ACCOUNT_ID_CONTEXT.get(), SOURCE_ID)
    current_tree = await generator.load_from_db()
    account_ref_id = account_id or ACCOUNT_ID_CONTEXT.get()
    await UpdateRiskScoreLogic(service_dal_fixture).update_risk_score_for_tree(account_ref_id, SOURCE_ID, current_tree)


async def _create_cases_and_summary(service_dal_fixture: ServiceDAL, account_id: str | None = None):
    id_vs_parent = {a.get("id", ""): a for a in FAKE_PROVIDER_FIELDS_DATA[0:8]}
    all_cases: list[CaseTable] = []
    for issue_id, data in id_vs_parent.items():
        case = await create_case(
            service_dal_fixture,
            issue_id=issue_id,
            provider_fields=data,
            risk_score=RISK_SCORES[issue_id],
            account_id=account_id,
        )
        all_cases.append(case)

    fake_provider = FAKE_PROVIDER_FIELDS_DATA[8]
    fake_provider["id"] = "ISSUE-9"
    all_cases.append(
        await create_case(
            service_dal_fixture,
            issue_id="no-security-issue",
            classification=False,
            provider_fields=fake_provider,
            account_id=account_id,
        )
    )
    fake_provider = FAKE_PROVIDER_FIELDS_DATA[9]
    all_cases.append(
        await create_case(
            service_dal_fixture,
            issue_id="no-security-epic",
            classification=False,
            provider_fields=fake_provider,
            risk_score=0,
            account_id=account_id,
        )
    )
    await _update_risk_scores_for_source(service_dal_fixture)
    percentage = 10
    for case in all_cases:
        await service_dal_fixture.cases_dal.update_case(
            account_id, SOURCE_ID, case.issue_id, progress_percentage=percentage
        )
        percentage += 10
    return all_cases


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestContainersViewFilter:
    async def test_get_filter_by_progress(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            cases = await _create_cases_and_summary(service_dal_fixture)
            cases_vs_progress = {case.issue_id: case.progress_percentage for case in cases}

            cases_above_20 = [case for case in cases_vs_progress if cases_vs_progress[case] >= 20]
            cases_below_80 = [case for case in cases_vs_progress if cases_vs_progress[case] < 80]
            cases_between_20_and_80 = [
                case for case in cases_vs_progress if cases_vs_progress[case] >= 20 and cases_vs_progress[case] <= 80
            ]

            f_above_20 = Filter(
                field="progress_percentage",
                value="20",
                op=Operator.GTE,
            )

            cases = await cases_api.get_cases_for_account(account_id, f=[f_above_20.as_url_str()])
            assert len(cases.results) == len(cases_above_20)
            assert all(case.issue_id in cases_above_20 for case in cases.results)

            f_below_80 = Filter(
                field="progress_percentage",
                value="80",
                op=Operator.LT,
            )
            cases = await cases_api.get_cases_for_account(account_id, f=[f_below_80.as_url_str()])
            assert len(cases.results) == len(cases_below_80)
            assert all(case.issue_id in cases_below_80 for case in cases.results)

            f_between_20_and_80 = Filter(
                field="progress_percentage",
                value=["20", "80"],
                op=Operator.BETWEEN,
            )
            cases = await cases_api.get_cases_for_account(account_id, f=[f_between_20_and_80.as_url_str()])
            assert len(cases.results) == len(cases_between_20_and_80)
            assert all(case.issue_id in cases_between_20_and_80 for case in cases.results)

            sort_args = [SortField(field="progress_percentage", direction=SortDirection.ASC).as_json_str()]
            cases = await cases_api.get_cases_for_account(account_id, s=sort_args)
            assert [case.progress_percentage for case in cases.results] == sorted(cases_vs_progress.values())

            sort_args = [SortField(field="progress_percentage", direction=SortDirection.DESC).as_json_str()]
            cases = await cases_api.get_cases_for_account(account_id, s=sort_args)
            assert [case.progress_percentage for case in cases.results] == sorted(
                cases_vs_progress.values(), reverse=True
            )

    async def test_get_filter_by_issue_type(
        self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str
    ):
        with _basic_mocks():
            await create_cases(service_dal_fixture, account_id=account_id + "2", with_summary=True)
            cases = await _create_cases_and_summary(service_dal_fixture)
            issue_ids_1 = [case.issue_id for case in cases if case.provider_fields.get("issuetype", None) == TASK_TYPE]
            f = [
                Filter(
                    field="provider_fields.issuetype",
                    value=["Task"],
                    op=Operator.EQ,
                ).as_url_str(),
            ]
            cases = await cases_api.get_cases_for_account(account_id, f=f)
            assert len(cases.results) == len(issue_ids_1)
            assert cases.results[0].provider_fields["project"] == "test_project_id"
            assert cases.results[0].issue_analysis.risk_score == 3
            assert cases.size == len(issue_ids_1)
            assert cases.total == len(issue_ids_1)
            assert all(summary.issue_id in issue_ids_1 for summary in cases.results)

    async def test_get_sort_by_risk(self, service_dal_fixture: ServiceDAL, cases_api: CasesApi, account_id: str):
        with _basic_mocks():
            await _create_cases_and_summary(service_dal_fixture)
            filter_args = [Filter(field="classification", op=Operator.EQ, value=["True"]).as_json_str()]

            sort_args = [SortField(field="risk_score", direction=SortDirection.ASC).as_json_str()]
            cases = await cases_api.get_cases_for_account(account_id, s=sort_args, f=filter_args)
            assert [summary.issue_analysis.risk_score for summary in cases.results] == [0, 3, 9, 30, 40, 50, 60, 70]

            sort_args = [SortField(field="risk_score", direction=SortDirection.DESC).as_json_str()]
            cases = await cases_api.get_cases_for_account(account_id, s=sort_args, f=filter_args)
            assert [summary.issue_analysis.risk_score for summary in cases.results] == [70, 60, 50, 40, 30, 9, 3, 0]

    @pytest.mark.parametrize(
        "f_value,operator, expected_cases, expected_risk_scores",
        [
            ("0", Operator.GT, 2, [9, 70]),
            ("3", Operator.LT, 1, [0]),
            ("3", Operator.LTE, 1, [0]),
            ("3", Operator.EQ, 0, []),
            ("9", Operator.GTE, 2, [9, 70]),
            ("9", Operator.EQ, 1, [9]),
            (["1", "9"], Operator.BETWEEN, 1, [9]),
            (["0", "70"], Operator.BETWEEN, 3, [0, 9, 70]),
            (["0", "85"], Operator.BETWEEN, 3, [0, 9, 70]),
        ],
    )
    async def test_get_filter_by_risk(
        self,
        service_dal_fixture: ServiceDAL,
        cases_api: CasesApi,
        f_value: str | list[str],
        operator: Operator,
        expected_cases: int,
        expected_risk_scores: list[int],
        account_id: str,
    ):
        with _basic_mocks():
            await _create_cases_and_summary(service_dal_fixture)
            _filter = [
                Filter(field="risk_score", value=f_value, op=operator).as_json_str(),
                Filter(field="classification", value=["False", "True"], op=Operator.EQ).as_json_str(),
                Filter(
                    field="provider_fields.issuetype",
                    value=["Epic"],
                    op=Operator.EQ,
                ).as_json_str(),
            ]
            cases = await cases_api.get_cases_for_account(account_id, f=_filter)
            assert len(cases.results) == expected_cases
            assert sorted([case.issue_analysis.risk_score for case in cases.results]) == sorted(expected_risk_scores)
