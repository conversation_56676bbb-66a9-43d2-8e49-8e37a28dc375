from functools import cache

from prime_gen_ai_service_client import PSITaskOutput

from service.db import PsvTable, ServiceDAL
from service.errors import CaseNotFoundError
from service.k8s_jobs.security_violation_job.models import PsvJobLogicResult
from tests.case_test_utils import create_case
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    FAKE_PROVIDER_FIELDS_DATA,
    SOURCE_ID,
    TESTS_ISSUES,
    get_issue_id,
    resources_dir,
)


async def create_psv(
    service_dal: ServiceDAL,
    issue_id: str,
    account_id: str | None = None,
    source_id: int = SOURCE_ID,
    description: str = "description",
    psv_type: str = "psv_type",
) -> PsvTable:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    idx = int(issue_id.split("-")[-1])
    provider_fields = FAKE_PROVIDER_FIELDS_DATA[idx % (len(TESTS_ISSUES) - 1)]
    try:
        await service_dal.cases_dal.get_case(account_id, source_id, issue_id)
    except CaseNotFoundError:
        await create_case(
            service_dal, account_id=account_id, source_id=source_id, issue_id=issue_id, provider_fields=provider_fields
        )
    psv_result = PsvJobLogicResult(
        account_id=account_id,
        source_id=source_id,
        issue_id=issue_id,
        description=description,
        violation_type=psv_type,
        research_package_version="1.0",
        has_psv=True,
        issue_hash="hash",
    )
    table = await service_dal.psv_dal.add_psv(psv_result)
    return table


async def create_psvs(
    service_dal_fixture: ServiceDAL,
    account_id: str | None = None,
    source_id: int = SOURCE_ID,
    count: int = 4,
    psv_type: str = "psv_type",
) -> list[PsvTable]:
    return [
        await create_psv(
            service_dal_fixture,
            get_issue_id(idx + 1),
            account_id,
            source_id,
            f"description {idx}",
            psv_type,
        )
        for idx in range(0, count)
    ]


@cache
def get_psv_output(
    is_psv: bool = False,
    is_error: bool = False,
    is_null_results: bool = False,
) -> PSITaskOutput:
    if is_psv:
        file_name = "psv_output.json"
    elif is_error:
        file_name = "psv_output_error.json"
    elif is_null_results:
        file_name = "psv_null_output.json"
    else:
        file_name = "psv_no_psv_output.json"
    return PSITaskOutput.model_validate_json((resources_dir / "gen-ai-outputs" / file_name).read_text())
