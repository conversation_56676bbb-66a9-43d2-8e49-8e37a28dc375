from datetime import UTC, datetime, timedelta

import pytest
from sqlalchemy.exc import DB<PERSON><PERSON>rror, IntegrityError
from starlette import status

from service.db import ServiceDAL, UpsertCasesBulkArgs
from service.errors import CaseIdNotFoundError, CaseNotFoundError
from service.logic.external_container import ChildInfo
from service.logic.filters_and_sort import CaseFilters
from service.models import (
    CaseAuditAction,
    CaseAuditUpdateStatusArgs,
    CaseStatus,
    CreateCaseComment,
    ProviderFieldType,
    RiskScoreCategory,
    risk_category_to_score,
    risk_score_to_category,
)
from service.models.filters_and_sort import Filter, Operator, SortDirection, SortField
from tests.case_test_utils import (
    TEST_RECOMMENDATIONS,
    add_issue_analysis_to_case,
    create_case,
    create_cases,
)
from tests.mock_utils import (
    FAKE_PROVIDER_FIELDS_INFO,
    SOURCE_ID,
    STORY_TYPE,
    TOTAL_TESTS_ISSUES,
    get_issue_id,
)

ISSUE_ID = "test_issue_id"


async def _create_case(
    service_dal: ServiceDAL,
    issue_id: str,
    risk_score: RiskScoreCategory,
    with_analysis: bool = True,
    parent_id: str | None = None,
):
    provider_fields = None
    if parent_id:
        provider_fields = {
            "parent_id": parent_id,
        }

    await create_case(
        service_dal,
        issue_id,
        with_summary=False,
        with_analysis=with_analysis,
        risk_score=risk_category_to_score(risk_score).start,
        provider_fields=provider_fields,
    )


def _validate_issues(issues: list[ChildInfo], expected: dict[RiskScoreCategory, int]):
    results: dict[RiskScoreCategory, int] = dict.fromkeys(RiskScoreCategory, 0)
    for issue in issues:
        results[risk_score_to_category(issue.risk_score)] += 1
    assert results == expected


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestCasesDAL:
    async def test_add_case(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        case = await cases_dal.add_case(account_id, SOURCE_ID, ISSUE_ID, STORY_TYPE)
        assert case is not None
        assert case.issue_analysis_id is None
        assert case.status == CaseStatus.OPEN

    async def test_add_case_dup(self, service_dal_fixture: ServiceDAL):
        await create_case(service_dal_fixture, ISSUE_ID)
        with pytest.raises(IntegrityError):
            await create_case(service_dal_fixture, ISSUE_ID)

    async def test_get_case_by_id(self, service_dal_fixture: ServiceDAL, account_id: str):
        case_created = await create_case(service_dal_fixture, ISSUE_ID)
        case_get = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case_created.id)
        assert case_created == case_get
        assert len(case_get.case_history) == 0

        case_get = await service_dal_fixture.get_case_view(account_id, SOURCE_ID, ISSUE_ID)
        assert case_created == case_get
        assert len(case_get.case_history) == 1
        assert case_get.case_history[0].audit_action == CaseAuditAction.view

    async def test_get_case_by_id_no_case(self, account_id: str, service_dal_fixture: ServiceDAL):
        case_id = 10000
        with pytest.raises(CaseIdNotFoundError) as e:
            await service_dal_fixture.cases_dal.get_case_by_id(account_id, case_id)
        assert e.value.http_status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    async def test_get_case(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        case_created = await cases_dal.add_case(account_id, SOURCE_ID, ISSUE_ID, STORY_TYPE)
        case_get = await cases_dal.get_case(account_id, SOURCE_ID, ISSUE_ID)
        assert case_created == case_get

    async def test_get_case_not_exist(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        with pytest.raises(CaseNotFoundError):
            _ = await cases_dal.get_case(account_id, SOURCE_ID, ISSUE_ID)

    async def test_get_cases_for_account_and_source_no_cases(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await service_dal_fixture.cases_dal.get_cases_by(account_id, SOURCE_ID)
        assert len(cases) == 0

    async def test_get_cases_for_account_and_source(self, service_dal_fixture: ServiceDAL, account_id: str):
        case1 = await create_case(service_dal_fixture, ISSUE_ID, with_analysis=True)
        case2 = await create_case(service_dal_fixture, ISSUE_ID + "1", with_analysis=True)
        case3 = await create_case(service_dal_fixture, ISSUE_ID, account_id + "1", with_analysis=True)

        cases = await service_dal_fixture.cases_dal.get_cases_by(account_id, SOURCE_ID)
        assert len(cases) == 2
        assert case1 in cases
        assert case2 in cases
        assert case3 not in cases

    async def test_get_cases_for_account(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal

        case1 = await create_case(service_dal_fixture, ISSUE_ID, with_analysis=True)
        case2 = await create_case(service_dal_fixture, ISSUE_ID + "1", source_id=SOURCE_ID + 1, with_analysis=True)
        case3 = await create_case(service_dal_fixture, ISSUE_ID, account_id + "1", with_analysis=True)
        cases = await cases_dal.get_cases_by(account_id)
        assert len(cases) == 2
        assert case1 in cases
        assert case2 in cases
        assert case3 not in cases
        cases = await cases_dal.get_cases_by(account_id, source_id=SOURCE_ID)
        assert len(cases) == 1
        assert case1 in cases

    async def test_add_issue_analysis_to_case(self, service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(service_dal_fixture, ISSUE_ID, with_analysis=False)
        assert case.issue_analysis_id is None
        await add_issue_analysis_to_case(service_dal_fixture, account_id, case.id)
        assert case.issue_analysis_id is not None

    async def test_cases_add_comment(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        case = await cases_dal.add_case(account_id, SOURCE_ID, ISSUE_ID, STORY_TYPE)
        comment1 = CreateCaseComment(user="momo", text="test_comment")
        await service_dal_fixture.session.commit()

        await cases_dal.add_comment(account_id, SOURCE_ID, ISSUE_ID, comment1)
        comment2 = CreateCaseComment(user="momo2", text="test_comment2")
        await cases_dal.add_comment(account_id, SOURCE_ID, ISSUE_ID, comment2)
        await service_dal_fixture.session.commit()
        case_with_comment = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case.id)
        assert case_with_comment.comments is not None
        assert len(case_with_comment.comments) == 2
        for idx, comment in enumerate([comment1, comment2]):
            assert case_with_comment.comments[idx].user == comment.user
            assert case_with_comment.comments[idx].text == comment.text

    async def test_update_case_recommendation(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        case = await cases_dal.add_case(account_id, SOURCE_ID, ISSUE_ID, STORY_TYPE)
        assert case.recommendations is None
        recommendations1 = TEST_RECOMMENDATIONS[0:2].copy()
        await cases_dal.update_case_recommendations(
            account_id=case.account_id,
            source_id=case.source_id,
            issue_id=case.issue_id,
            recommendations=recommendations1,
        )
        await service_dal_fixture.session.commit()
        recommendations2 = [TEST_RECOMMENDATIONS[1].model_copy()]
        await cases_dal.update_case_recommendations(
            account_id=case.account_id,
            source_id=case.source_id,
            issue_id=case.issue_id,
            recommendations=recommendations2,
        )
        await service_dal_fixture.session.commit()
        case_with_recommendation = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case.id)
        await service_dal_fixture.session.refresh(
            case_with_recommendation, ["recommendations"]
        )  # to load deferred column
        assert case_with_recommendation.recommendations == recommendations2

    async def test_update_case_status(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        case = await cases_dal.add_case(account_id, SOURCE_ID, ISSUE_ID, STORY_TYPE)
        assert case.status == CaseStatus.OPEN
        await cases_dal.update_case_status(account_id, SOURCE_ID, ISSUE_ID, CaseStatus.DISMISSED)
        case_with_status = await cases_dal.get_case_by_id(account_id, case.id)
        assert case_with_status.status == CaseStatus.DISMISSED

        assert len(case_with_status.case_history) == 1
        assert case_with_status.case_history[0].audit_action == CaseAuditAction.update_status

        expected = CaseAuditUpdateStatusArgs(old_status=CaseStatus.OPEN, new_status=CaseStatus.DISMISSED, reason=None)
        assert case_with_status.case_history[0].audit_action_args == expected.model_dump()

        await cases_dal.update_case_status(account_id, SOURCE_ID, ISSUE_ID, CaseStatus.OPEN)
        await cases_dal.update_case_status(
            account_id, SOURCE_ID, ISSUE_ID, CaseStatus.DISMISSED, dismissed_reason="Just because I can"
        )
        case_with_status = await cases_dal.get_case_by_id(account_id, case.id)
        assert case_with_status.status == CaseStatus.DISMISSED
        assert case_with_status.dismissed_reason == "Just because I can"
        assert len(case_with_status.case_history) == 3
        assert case_with_status.case_history[2].audit_action == CaseAuditAction.update_status
        expected = CaseAuditUpdateStatusArgs(
            old_status=CaseStatus.OPEN, new_status=CaseStatus.DISMISSED, reason="Just because I can"
        )
        assert case_with_status.case_history[2].audit_action_args == expected.model_dump()

    async def test_get_cases_with_filters(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        created_cases = await create_cases(service_dal_fixture)

        # get filtered
        case_filters = CaseFilters(
            [
                Filter(field="provider_fields.field_2", value="40", op=Operator.EQ),
                Filter(field="provider_fields.field_4", value="false", op=Operator.EQ),
            ],
        )
        cases = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert len(cases) == 1
        assert cases[0] == created_cases[3]

    @pytest.mark.parametrize(
        "filed_name",
        ["field_2", "field_3"],
    )
    async def test_get_cases_with_sort_provider_field(
        self, service_dal_fixture: ServiceDAL, filed_name: str, account_id: str
    ):
        cases_dal = service_dal_fixture.cases_dal
        await create_cases(service_dal_fixture)

        sort_args = [
            SortField(
                field=f"provider_fields.{filed_name}",
                direction=SortDirection.ASC,
                inner_field_type=FAKE_PROVIDER_FIELDS_INFO[filed_name].type,
            )
        ]
        cases = await cases_dal.get_cases_by(account_id, sort_args=sort_args)
        assert cases == sorted(cases, key=lambda x: x.provider_fields[filed_name])

    async def test_get_cases_with_filters_and_sorted_raise_db_error(
        self, service_dal_fixture: ServiceDAL, account_id: str
    ):
        cases_dal = service_dal_fixture.cases_dal
        await create_cases(service_dal_fixture)
        case_filters = CaseFilters(
            [
                Filter(
                    field="provider_fields.field_3",
                    value="true",
                    op=Operator.EQ,
                    inner_field_type=ProviderFieldType.BOOLEAN,
                )
            ]
        )
        with pytest.raises(DBAPIError) as e:
            await cases_dal.get_cases_by(account_id, case_filters=case_filters)

        assert e.value.orig.pgcode == "22P02"

        # get sorted raise exception
        sort_args = [
            SortField(
                field="provider_fields.field_3",
                direction=SortDirection.ASC,
                inner_field_type=ProviderFieldType.BOOLEAN,
            ),
        ]
        with pytest.raises(DBAPIError) as e:
            await cases_dal.get_cases_by(account_id, sort_args=sort_args)

        assert e.value.orig.pgcode == "25P02"

    @pytest.mark.parametrize(
        "time_offset,time_op",
        [(1, Operator.GTE), (2, Operator.GTE), (1, Operator.EQ), (TOTAL_TESTS_ISSUES, Operator.NE)],
    )
    async def test_get_cases_with_since_date_filter(
        self, service_dal_fixture: ServiceDAL, time_offset: int, time_op: Operator, account_id: str
    ):
        cases_dal = service_dal_fixture.cases_dal
        created_cases = await create_cases(service_dal_fixture)

        for days_offset, case in enumerate(created_cases):
            analysis = await service_dal_fixture.issues_analysis_dal.get_issue_analysis_by_id(case.issue_analysis_id)
            analysis.created_at = datetime.now(UTC) - timedelta(days=days_offset)
            analysis.updated_at = datetime.now(UTC) - timedelta(days=days_offset)
        await service_dal_fixture.session.commit()
        # get filtered
        today = datetime.now(UTC)
        case_filters = CaseFilters(
            [Filter(field="since_date", value=(today - timedelta(days=time_offset)).isoformat(), op=time_op)],
        )
        cases = await cases_dal.get_cases_by(account_id, case_filters=case_filters)

        assert len(cases) == time_offset

    async def test_get_cases_partial_match(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        cases = await create_cases(service_dal_fixture)
        case_filters = CaseFilters([Filter(field="provider_fields.field_1", value="give you", op=Operator.EQ)])
        results = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert results[0] == cases[1]

        case_filters = CaseFilters([Filter(field="provider_fields.field_1", value="never", op=Operator.EQ)])
        results = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert results == [cases[0], cases[2]]

        case_filters = CaseFilters([Filter(field="provider_fields.field_1", value=["never", "give"], op=Operator.EQ)])
        cases = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert [case.issue_id for case in cases] == [
            "ISSUE-1",
            "ISSUE-2",
            "ISSUE-3",
            "ISSUE-5",
            "ISSUE-6",
            "ISSUE-7",
            "ISSUE-8",
            "ISSUE-9",
        ]

    async def test_get_cases_array_partial_match(self, service_dal_fixture: ServiceDAL, account_id: str):
        def _get_labels_filter(_value: str | list[str]):
            _filter = Filter(
                field="provider_fields.labels",
                value=_value,
                op=Operator.EQ,
                inner_field_type=ProviderFieldType(ProviderFieldType.ARRAY),
            )
            return CaseFilters([_filter])

        cases_dal = service_dal_fixture.cases_dal
        cases = await create_cases(service_dal_fixture)
        case_filters = _get_labels_filter("label1")
        results = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert results == [cases[0]]

        case_filters = _get_labels_filter("label2")
        results = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert results == [cases[0], cases[1]]

        case_filters = _get_labels_filter(["label1", "label3"])
        cases = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert [case.issue_id for case in cases] == [
            "ISSUE-1",
            "ISSUE-2",
            "ISSUE-5",
            "ISSUE-6",
            "ISSUE-7",
            "ISSUE-8",
            "ISSUE-9",
            "ISSUE-10",
        ]

        case_filters = _get_labels_filter("not exist")
        cases = await cases_dal.get_cases_by(account_id, case_filters=case_filters)
        assert cases == []

    async def test_get_cases_issues_id(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        cases = await create_cases(service_dal_fixture)
        issue_ids = [case.issue_id for case in cases[1:3]]
        results = await cases_dal.get_cases_by(account_id, source_id=SOURCE_ID, issues=issue_ids)
        assert len(results) == 2
        assert all(result in cases[1:3] for result in results)

    async def test_get_cases_upsert(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases_dal = service_dal_fixture.cases_dal
        cases = await create_cases(service_dal_fixture)

        provider_fields = {"test1": "test1", "test2": "test2"}
        labels = ["label1", "label3"]
        fields = {"provider_fields": provider_fields, "parent_issue_id": "20", "labels": labels}

        actual1 = await cases_dal.get_case(account_id, source_id=SOURCE_ID, issue_id=cases[0].issue_id)
        assert actual1.provider_fields != fields["provider_fields"]  # sanity
        assert actual1.labels != fields["labels"]  # sanity

        updated_issues = [case.issue_id for case in cases[:3]] + [get_issue_id(11)]
        bulk = [UpsertCasesBulkArgs(issue_id=issue_id, fields=fields) for issue_id in updated_issues]
        await service_dal_fixture.cases_dal.upsert_cases(account_id, SOURCE_ID, bulk)
        service_dal_fixture.session.expunge_all()
        for issue_id in updated_issues:
            actual = await cases_dal.get_case(account_id, source_id=SOURCE_ID, issue_id=issue_id)
            assert actual.provider_fields == fields["provider_fields"]
            assert actual.parent_issue_id == fields["parent_issue_id"]
            assert actual.labels == fields["labels"]

        fields = {"parent_issue_id": "10"}
        bulk = [UpsertCasesBulkArgs(issue_id=issue_id, fields=fields) for issue_id in updated_issues]
        await service_dal_fixture.cases_dal.upsert_cases(account_id, SOURCE_ID, bulk)
        service_dal_fixture.session.expunge_all()
        for issue_id in updated_issues:
            actual = await cases_dal.get_case(account_id, source_id=SOURCE_ID, issue_id=issue_id)
            assert actual.provider_fields == provider_fields
            assert actual.parent_issue_id == fields["parent_issue_id"]
            assert actual.labels == labels
