import random

import pytest
from sqlmodel import and_, select

from service.db import CaseTable, IssueStructure, IssueSummaryAttributesTable, IssueSummaryDAL, ServiceDAL
from service.k8s_jobs.base_job_logic import SummaryOutput, SummaryOutputData
from tests.case_test_utils import (
    create_case,
    create_cases,
    get_summary_output,
)
from tests.db.test_cases_dal import ISSUE_ID
from tests.mock_utils import SOURCE_ID, TOTAL_TESTS_ISSUES


@pytest.fixture
def issue_summary_dal(service_dal_fixture: ServiceDAL) -> IssueSummaryDAL:
    return service_dal_fixture.issue_summary_dal


@pytest.fixture
async def case(service_dal_fixture: ServiceDAL) -> CaseTable:
    return await create_case(service_dal_fixture, ISSUE_ID)


@pytest.fixture
async def issue_id() -> str:
    return ISSUE_ID


@pytest.fixture
async def node(service_dal_fixture: ServiceDAL) -> str:
    return ISSUE_ID


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestIssueSummaryDAL:
    async def test_update_issue_summary(self, service_dal_fixture: ServiceDAL, account_id: str):
        random_hash = str(random.randint(0, 100000))
        case = await create_case(service_dal_fixture, ISSUE_ID, with_summary=False)
        summary_question = get_summary_output().context_summary.questions.model_copy(deep=True)
        summary_question.what.summary.text = "summary1"
        version1 = "1.0.0"
        summary_data = SummaryOutputData(
            questions_summary="new partial_summary", questions=summary_question, short_summary="new short_summary"
        )
        await service_dal_fixture.issue_summary_dal.upsert_summary(
            case,
            summary=SummaryOutput(
                data=summary_data,
                issue_hash=random_hash,
                ai_version=version1,
            ),
        )
        service_dal_fixture.session.expunge_all()
        case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, ISSUE_ID, with_summary=True)
        assert case.partial.summary == summary_data.questions_summary
        assert case.partial.questions == summary_data.questions
        assert case.partial.ai_version == version1
        assert case.partial.issue_hash == random_hash
        assert case.final is None

        query = select(IssueSummaryAttributesTable).join(
            CaseTable, and_(IssueSummaryAttributesTable.id == CaseTable.partial_id, CaseTable.account_id == account_id)
        )

        total = (await service_dal_fixture.session.exec(query)).all()
        assert len(total) == 1

        random_hash = str(random.randint(0, 100000))
        summary_data2 = SummaryOutputData(
            questions_summary="new partial_summary2", questions=summary_question, short_summary="new short_summary2"
        )
        await service_dal_fixture.issue_summary_dal.upsert_summary(
            case,
            summary=SummaryOutput(
                data=summary_data2,
                issue_hash=random_hash,
                ai_version=version1,
            ),
        )
        case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, ISSUE_ID)
        assert case.partial.summary == summary_data2.questions_summary
        assert case.partial.short == summary_data2.short_summary
        assert case.partial.issue_hash == random_hash
        total = (await service_dal_fixture._session.exec(query)).all()
        assert len(total) == 1  # verify delete orphans works

        random_hash = str(random.randint(0, 100000))
        version2 = "1.0.2"
        await service_dal_fixture.issue_summary_dal.upsert_summary(
            case, SummaryOutput(issue_hash=random_hash, ai_version=version2, data=summary_data2)
        )
        case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, ISSUE_ID)
        assert case.partial.summary == summary_data2.questions_summary
        assert case.partial.issue_hash == random_hash
        assert case.partial.ai_version == version2

    async def test_get_issue_summaries_stamp(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await create_cases(service_dal_fixture, with_summary=True)
        for case in cases:
            assert case.partial_id is not None
            assert case.final_id is None

        service_dal_fixture.session.expunge_all()
        summaries_info = await service_dal_fixture.issue_summary_dal.get_summaries_info(account_id, SOURCE_ID)
        assert len(summaries_info) == TOTAL_TESTS_ISSUES
        for info in summaries_info:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, info.issue_id, with_summary=True)
            assert info.partial_hash == case.partial.issue_hash
            assert info.partial_version == case.partial.ai_version

    async def test_load_cases_skeleton(self, issue_summary_dal: IssueSummaryDAL, account_id: str):
        await create_cases(issue_summary_dal._service_dal)
        summaries: IssueStructure = await issue_summary_dal._service_dal.cases_dal.get_issues_structure(
            account_id, SOURCE_ID
        )
        expected_structure = [
            (None, "ISSUE-1"),
            ("ISSUE-1", "ISSUE-2"),
            ("ISSUE-2", "ISSUE-3"),
            (None, "ISSUE-4"),
            ("ISSUE-1", "ISSUE-5"),
            ("ISSUE-1", "ISSUE-6"),
            (None, "ISSUE-7"),
            (None, "ISSUE-8"),
            ("ISSUE-8", "ISSUE-9"),
            ("ISSUE-8", "ISSUE-10"),
        ]

        assert [(s[0], s[1]) for s in summaries] == [(e[0], e[1]) for e in expected_structure]

    async def test_load_cases_skeleton_parent_id(self, issue_summary_dal: IssueSummaryDAL, account_id: str):
        await create_cases(issue_summary_dal._service_dal)
        summaries: IssueStructure = await issue_summary_dal._service_dal.cases_dal.get_issues_structure(
            account_id, SOURCE_ID, parent_id="ISSUE-1"
        )
        expected_structure = [
            ("ISSUE-1", "ISSUE-2"),
            ("ISSUE-2", "ISSUE-3"),
            ("ISSUE-1", "ISSUE-5"),
            ("ISSUE-1", "ISSUE-6"),
        ]

        assert [(s[0], s[1]) for s in summaries] == [(e[0], e[1]) for e in expected_structure]

    @pytest.mark.parametrize(
        "parent_id, expected_structure",
        [
            (
                "ISSUE-1",
                [
                    (None, "ISSUE-1"),
                    ("ISSUE-1", "ISSUE-2"),
                    ("ISSUE-2", "ISSUE-3"),
                    ("ISSUE-1", "ISSUE-5"),
                    ("ISSUE-1", "ISSUE-6"),
                ],
            ),
            ("ISSUE-2", [(None, "ISSUE-1"), ("ISSUE-1", "ISSUE-2"), ("ISSUE-2", "ISSUE-3")]),
            ("ISSUE-3", [(None, "ISSUE-1"), ("ISSUE-1", "ISSUE-2"), ("ISSUE-2", "ISSUE-3")]),
        ],
    )
    async def test_load_cases_skeleton_parent_id_with_parents(
        self, parent_id: str, expected_structure: IssueStructure, issue_summary_dal: IssueSummaryDAL, account_id: str
    ):
        cases_dal = issue_summary_dal._service_dal.cases_dal
        await create_cases(issue_summary_dal._service_dal)

        summaries = await cases_dal.get_issues_structure(account_id, SOURCE_ID, parent_id=parent_id, with_parents=True)
        assert [(s[0], s[1]) for s in summaries] == [(e[0], e[1]) for e in expected_structure]
