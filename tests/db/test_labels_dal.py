import pytest
import sqlalchemy
from sqlalchemy import text

from service.db import ServiceDAL
from tests.case_test_utils import create_case
from tests.mock_utils import get_issue_id


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestLabelDAL:
    @pytest.mark.parametrize(
        "labels",
        [
            [],
            ["YveQB5ICBcFvBK{8#Aq-6FWp7(>kfy^%Y;fq}MHb6$Jf/9QxsJ].p;NP4H5\\t#"],
            # [''.join(random.choices(string.printable, k=i)) for i in range(1, 100)]
        ],
    )
    async def test_set_labels(self, labels: list[str], service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(service_dal_fixture, get_issue_id(1), with_analysis=False)
        await service_dal_fixture.set_labels(case.account_id, case.source_id, case.issue_id, labels)
        db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert db_labels is not None
        assert {label.name for label in db_labels} == set(labels)
        await service_dal_fixture.session.refresh(case)
        assert case.labels == labels

    async def test_set_labels_with_dup(self, service_dal_fixture: ServiceDAL, account_id: str):
        label = "label1"
        db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert not db_labels
        case = await create_case(service_dal_fixture, get_issue_id(1), with_analysis=False)
        await service_dal_fixture.set_labels(case.account_id, case.source_id, case.issue_id, [label] * 100)
        db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert len(db_labels) == 1
        assert db_labels[0].name == label
        await service_dal_fixture.session.refresh(case)
        assert case.labels == [label]

    async def test_set_labels_override(self, service_dal_fixture: ServiceDAL, account_id: str):
        db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert not db_labels
        case = await create_case(service_dal_fixture, get_issue_id(1), with_analysis=False)
        labels_count = 0
        for label in [["label"], [], ["label1", "label2"]]:
            labels_count += len(label)
            await service_dal_fixture.set_labels(case.account_id, case.source_id, case.issue_id, label)
            db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
            assert len(db_labels) == labels_count
            await service_dal_fixture.session.refresh(case)
            assert case.labels == label

    async def test_set_labels_label_already_exist(self, service_dal_fixture: ServiceDAL, account_id: str):
        label = "label1"
        db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert not db_labels
        await service_dal_fixture.session.exec(
            text(f"INSERT INTO labels (account_id, name) VALUES ('{account_id}', '{label}')")
        )
        assert len(await service_dal_fixture.labels_dal.get_labels(account_id)) == 1
        case = await create_case(service_dal_fixture, get_issue_id(1), with_analysis=False)
        await service_dal_fixture.set_labels(case.account_id, case.source_id, case.issue_id, [label])
        db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert len(db_labels) == 1

    async def test_set_labels_multiple_accounts(self, service_dal_fixture: ServiceDAL, account_id: str):
        account2 = account_id + "2"
        label = "label1"
        cases = {}
        for account in [account_id, account2]:
            assert not await service_dal_fixture.labels_dal.get_labels(account)
            case = await create_case(service_dal_fixture, get_issue_id(1), account_id=account, with_analysis=False)
            await service_dal_fixture.set_labels(case.account_id, case.source_id, case.issue_id, [label])
            cases[account] = case
        for account in [account_id, account2]:
            db_labels = await service_dal_fixture.labels_dal.get_labels(account)
            assert len(db_labels) == 1
            await service_dal_fixture.session.refresh(cases[account])
            assert cases[account].labels == [label]

    async def test_set_labels_multiple_cases(self, service_dal_fixture: ServiceDAL, account_id: str):
        label = "label1"
        case1 = await create_case(service_dal_fixture, get_issue_id(1), with_analysis=False)
        case2 = await create_case(service_dal_fixture, get_issue_id(2), with_analysis=False)
        await service_dal_fixture.set_labels(case1.account_id, case1.source_id, case1.issue_id, [label])
        await service_dal_fixture.set_labels(case2.account_id, case2.source_id, case2.issue_id, [label])
        db_labels = await service_dal_fixture.labels_dal.get_labels(account_id)
        assert len(db_labels) == 1
        await service_dal_fixture.session.refresh(case1)
        await service_dal_fixture.session.refresh(case2)
        assert case1.labels == case2.labels == [label]

    @pytest.mark.parametrize("label", ["", " ", "\n", "\n\b", "\t", "\t\b", "\t\b\n", "\t\b\n "])
    async def test_set_labels_not_valid_labels(
        self,
        label: str,
        account_id: str,
        service_dal_fixture: ServiceDAL,
    ):
        case = await create_case(service_dal_fixture, get_issue_id(1), with_analysis=False)
        with pytest.raises(sqlalchemy.exc.IntegrityError):
            await service_dal_fixture.set_labels(case.account_id, case.source_id, case.issue_id, [label])
