import pytest
from prime_config_service_client import SecurityFramework
from sqlmodel import and_, select

from service.db import CaseTable, ServiceDAL
from service.db.tables.issues_analysis import IssueAnalysisTable
from service.errors import CaseHasNotBeenProcessedError
from service.k8s_jobs.classification_job.classification_job_logic import ClassificationJobLogicResult
from service.models import Implementation, ImplementationStatus, SecurityControl
from tests.mock_utils import SOURCE_ID, get_assessment_result_output, get_classification_output

from ..case_test_utils import TEST_CONCERNS, create_case

ISSUE_ID = "test_issue"

JSON_AI_RESP = {
    "confidentiality": 1,
    "integrity": 0.4,
    "availability": 0.6,
    "third-party-management": 0.1,
    "compliance": 0.3,
    "severity": 1,
    "scope": 1,
}


def get_classifier_result(case_id: int) -> ClassificationJobLogicResult:
    assessment = get_assessment_result_output(is_dev=True)
    return ClassificationJobLogicResult(
        case_id=case_id,
        classification=True,
        concerns=TEST_CONCERNS.copy(),
        issue_hash="test_ai_response",
        short_assessment=assessment.short_summary,
        long_assessment=assessment.long_summary,
        research_package_version="0.0.0",
        risk_score=10,
        confidence=10,
        controls=[
            SecurityControl(
                name="test_control",
                description="test_description",
                id="test_id",
                control_names=[],
                framework=SecurityFramework.NIST,
            )
        ],
        recommendations=[
            Implementation(
                recommendation="test_recommendation",
                id=0,
                concern_id=1,
                control_id="1",
                status=ImplementationStatus.UNKNOWN,
                controls={"NIST": {"some_control"}},
            )
        ],
        confidentiality_score=10,
        integrity_score=1,
        availability_score=5,
        is_automated=get_classification_output(is_security=True).ticket_automated.is_automated,
        is_security_enhancement=False,
    )


@pytest.fixture
async def case(service_dal_fixture: ServiceDAL) -> CaseTable:
    return await create_case(service_dal_fixture, ISSUE_ID)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestIssuesAnalysisDAL:
    async def test_add_issue_analysis(self, service_dal_fixture: ServiceDAL, account_id: str):
        query = select(IssueAnalysisTable).join(
            CaseTable, and_(IssueAnalysisTable.id == CaseTable.issue_analysis_id, CaseTable.account_id == account_id)
        )
        assert len((await service_dal_fixture._session.exec(query)).all()) == 0
        case = await create_case(service_dal_fixture, ISSUE_ID, with_analysis=False)
        classification = get_classifier_result(case_id=case.id)
        issue_analysis1 = await service_dal_fixture.add_classification_result_to_case(account_id, classification)
        await service_dal_fixture.session.commit()
        service_dal_fixture.session.expunge_all()
        updated_case1 = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case.id, with_analysis=True)
        updated_case1_issue_analysis_id = updated_case1.issue_analysis_id
        assert updated_case1.issue_analysis_id == issue_analysis1.id
        assert updated_case1.issue_analysis.id == updated_case1.issue_analysis_id
        assert len((await service_dal_fixture._session.exec(query)).all()) == 1

        # add another one and check that the old one is deleted
        classification = get_classifier_result(case_id=case.id)
        classification.risk_score = 100
        issue_analysis2 = await service_dal_fixture.add_classification_result_to_case(account_id, classification)
        await service_dal_fixture.session.commit()
        service_dal_fixture.session.expunge_all()
        updated_case2 = await service_dal_fixture.cases_dal.get_case_by_id(account_id, case.id, with_analysis=True)
        assert updated_case2.issue_analysis_id == issue_analysis2.id
        assert updated_case2.issue_analysis_id != updated_case1_issue_analysis_id
        assert updated_case2.issue_analysis.id == updated_case2.issue_analysis_id
        assert len((await service_dal_fixture._session.exec(query)).all()) == 1

    async def test_override_risk_factor(self, service_dal_fixture: ServiceDAL, account_id: str):
        case = await create_case(service_dal_fixture, ISSUE_ID, with_analysis=False)
        with pytest.raises(CaseHasNotBeenProcessedError):
            await service_dal_fixture.cases_dal.override_risk_score(account_id, SOURCE_ID, case.issue_id, 10)
