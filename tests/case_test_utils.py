import copy
import logging
from collections import defaultdict
from functools import lru_cache

from prime_gen_ai_service_client import ContextPipelineOutput
from prime_shared.common_types import AccountIdType
from prime_source_service_client import SourceType
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import flag_modified

from client.prime_rat_logic_service_client.models.concern_type import ConcernType
from service.db import CaseTable, IssueAnalysisTable, ServiceDAL
from service.db.tables.cases import ProviderFieldsColumnType
from service.k8s_jobs.base_job_logic import GenAIBaseJob, SummaryOutput, SummaryOutputData
from service.k8s_jobs.classification_job.task_helpers import (
    ai_output_to_classification_task_results,
    get_recommendations_and_concerns,
)
from service.logic.external_cases import build_external_case, build_external_case_workroom
from service.logic.issue_analysis import get_linddun_options, get_mitre_data_dict
from service.models import ExternalCaseWorkroom, Implementation, IssueAnalysisConcern, SecurityControl
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    FAKE_PROVIDER_FIELDS_DATA,
    SOURCE_ID,
    TESTS_ISSUES,
    _basic_mocks,
    get_classification_output,
    get_prime_issue,
    resources_dir,
)

LOGGER = logging.getLogger("case_test_utils")


async def create_cases(
    service_dal_fixture: ServiceDAL,
    account_id: str | None = None,
    source_id: int = SOURCE_ID,
    count: int | None = None,
    extra: list[dict] = None,
    with_summary: bool = True,
    with_analysis: bool = True,
    source_type: SourceType = SourceType.JIRA,
) -> list[CaseTable]:
    if count is None:
        count = len(FAKE_PROVIDER_FIELDS_DATA)

    extra = extra or defaultdict(dict)
    provider_fields = FAKE_PROVIDER_FIELDS_DATA
    all_issues = TESTS_ISSUES.copy()
    return [
        await create_case(
            service_dal_fixture,
            all_issues[idx],
            account_id,
            source_id,
            with_analysis=with_analysis,
            with_summary=with_summary,
            provider_fields=provider_fields[idx % len(all_issues)],
            **extra[idx],
        )
        for idx in range(0, count)
    ]


async def create_case(
    service_dal_fixture: ServiceDAL,
    issue_id: str,
    account_id: str | None = None,
    source_id: int = SOURCE_ID,
    with_analysis: bool = True,
    with_summary: bool = True,
    classification: bool = True,
    provider_fields: ProviderFieldsColumnType = None,
    **extra,
) -> CaseTable:
    if provider_fields is None:
        provider_fields = FAKE_PROVIDER_FIELDS_DATA[0]
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    parent_id = provider_fields.get("parent_id", None) if provider_fields else None
    case = await service_dal_fixture.cases_dal.add_case(
        account_id,
        source_id,
        issue_id,
        parent_issue_id=parent_id,
        provider_fields=provider_fields,
    )
    await service_dal_fixture.session.commit()
    await service_dal_fixture.session.refresh(case)
    if with_analysis:
        await add_issue_analysis_to_case(
            service_dal_fixture, account_id, case.id, classification=classification, **extra
        )
    await service_dal_fixture.session.commit()
    if with_summary:
        await create_summary(
            service_dal_fixture,
            issue_id,
            account_id,
            source_id,
        )
    return await service_dal_fixture.cases_dal.get_case_by_id(
        account_id, case.id, with_analysis=with_analysis, with_summary=with_summary
    )


async def create_summary(
    service_dal_fixture: ServiceDAL,
    issue_id: str,
    account_id: str | None = None,
    source_id: int | None = None,
) -> None:
    account_id = account_id or ACCOUNT_ID_CONTEXT.get()
    source_id = source_id or SOURCE_ID
    summary = get_summary_output()
    partial_summary = SummaryOutput(
        data=SummaryOutputData(
            questions=summary.context_summary.questions,
            questions_summary="I am partial questions summary",
            short_summary="I am partial short",
        ),
        issue_hash="abc",
        ai_version="1.0.0",
    )
    case = await service_dal_fixture.cases_dal.get_case(account_id=account_id, source_id=source_id, issue_id=issue_id)
    await service_dal_fixture.issue_summary_dal.upsert_summary(case=case, summary=partial_summary)
    await service_dal_fixture.session.refresh(case)


def get_summary_output() -> ContextPipelineOutput:
    output_file = resources_dir / "summaries" / "MKT-1800_context_summary.json"
    summary = output_file.read_text()
    return ContextPipelineOutput.model_validate_json(summary)


@lru_cache
def generate_general_test_recommendation_and_concerns() -> (
    tuple[list[IssueAnalysisConcern], list[SecurityControl], list[Implementation]]
):
    output = get_classification_output(is_security=True, with_recommendations=True)
    return get_recommendations_and_concerns(
        output.personal_recommendations, output.privacy_concerns, output.mitre_attack_concerns
    )


TEST_CONCERNS, TEST_CONTROLS, TEST_RECOMMENDATIONS = generate_general_test_recommendation_and_concerns()


async def add_recommendation_to_case(service_dal_fixture: ServiceDAL, case: CaseTable) -> list[Implementation]:
    case.recommendations = copy.deepcopy(TEST_RECOMMENDATIONS)
    await service_dal_fixture.session.commit()
    return case.recommendations


def calculate_hash(issue_id: str) -> str:
    if issue_id is None or not issue_id.startswith("ISSUE-"):
        return "some_hash"
    try:
        jira_issue = get_prime_issue(issue_id)
    except FileNotFoundError:
        return "some_hash"
    if parent_id := jira_issue.fields.get("parent", {}).get("key"):
        jira_issue.parent = get_prime_issue(parent_id)
    return GenAIBaseJob.get_issue_hash(jira_issue)


async def add_issue_analysis_to_case(
    service_dal_fixture: ServiceDAL,
    account_id: AccountIdType,
    case_id: int,
    confidentiality: int = 10,
    integrity: int = 4,
    availability: int = 6,
    risk_score: int | None = None,
    confidence: int | None = None,
    classification: bool = True,
    with_recommendations: bool = False,
) -> None:
    is_dev = classification
    issue_hash = calculate_hash((await service_dal_fixture.cases_dal.get_case_by_id(account_id, case_id)).issue_id)
    ai_output = get_classification_output(is_security=is_dev, with_recommendations=with_recommendations)
    classification_task_results = ai_output_to_classification_task_results(ai_output, case_id, issue_hash, "0.0.0", [])
    classification_task_results.confidentiality_score = confidentiality
    classification_task_results.integrity_score = integrity
    classification_task_results.availability_score = availability
    if risk_score is not None:
        classification_task_results.risk_score = risk_score
    if confidence is not None:
        classification_task_results.confidence = confidence
    classification_task_results.classification = classification
    await service_dal_fixture.add_classification_result_to_case(account_id, classification_task_results)
    await service_dal_fixture.session.commit()


async def get_external_case_summary(case: CaseTable, service_dal: ServiceDAL) -> ExternalCaseWorkroom:
    issue_analysis = await service_dal.issues_analysis_dal.get(case.account_id, case.source_id, case.issue_id)
    return await build_external_case_workroom(case, issue_analysis, None, service_dal)


async def build_external_case_for_testing(
    case: CaseTable, service_dal_fixture: ServiceDAL, test_issue: str, recommendations=None
):
    if recommendations is not None:
        case.recommendations = recommendations
        await service_dal_fixture.session.commit()

    with _basic_mocks():
        external_case = await build_external_case(case, service_dal_fixture, provider_fields_info=None)

    return case, external_case


async def modify_fire_summary(session: AsyncSession, issue_analysis_id: int, summary: str) -> None:
    result = await session.exec(select(IssueAnalysisTable).where(IssueAnalysisTable.id == issue_analysis_id))
    issue_analysis = result.scalars().first()

    if not issue_analysis:
        LOGGER.info("Issue analysis not found")
        return

    issue_analysis.fire_summary = summary

    await session.commit()


async def _modify_concern_category_for_issue_analysis(
    session: AsyncSession,
    issue_analysis: IssueAnalysisTable,
    category_options: dict[ConcernType, list[str]],
    category_index_tracker: dict[ConcernType, int],
    concern_type: ConcernType | None = None,
    category: str | None = None,
):
    if not issue_analysis.concerns:
        LOGGER.info("No concerns found for issue analysis ID %s", issue_analysis.id)
        return

    def _get_next_category(concern_type: ConcernType) -> str:
        """Returns the next category in a cyclic manner for the given concern type."""
        categories = category_options[concern_type]
        index = category_index_tracker[concern_type]
        next_category = categories[index % len(categories)]
        category_index_tracker[concern_type] = index + 1
        return next_category

    for concern in issue_analysis.concerns:
        concern_methodology = concern.methodology

        # If concern_type and category are specified, update directly
        if concern_type and category:
            if concern_methodology.type.value == concern_type.value:
                concern_methodology.category = category

        # If only concern_type is specified, iterate through categories
        elif concern_type:
            if concern_methodology.type.value == concern_type.value:
                concern_methodology.category = _get_next_category(concern_type)

        # If neither concern_type nor category is specified, infer concern type and iterate categories
        else:
            inferred_type = (
                ConcernType.LINDDUN
                if concern_methodology.type.value == ConcernType.LINDDUN.value
                else ConcernType.MITRE
            )
            concern_methodology.category = _get_next_category(inferred_type)

        flag_modified(issue_analysis, "concerns")
        session.add(issue_analysis)


async def modify_concern_category(
    session: AsyncSession,
    issue_analysis_ids: list[int],
    concern_type: ConcernType | None = None,
    category: str | None = None,
) -> None:
    """
    Modify the concern category for the given issue analyses.
    There are three possible scenarios:
        1. If concern_type and category are provided, update the category directly.
        2. If only concern_type is provided, iterate through the categories cyclically.
        3. If neither concern_type nor category is provided, iterate through the categories
    """
    category_index_tracker = {ConcernType.LINDDUN: 0, ConcernType.MITRE: 0}

    if not concern_type and category:
        LOGGER.info("Please provide concern type, when you want to update category")
        return

    categories = {ConcernType.LINDDUN: get_linddun_options(), ConcernType.MITRE: list(get_mitre_data_dict().keys())}

    for issue_analysis_id in issue_analysis_ids:
        db_result = await session.exec(select(IssueAnalysisTable).where(IssueAnalysisTable.id == issue_analysis_id))
        issue_analysis = db_result.scalars().first()

        if not issue_analysis:
            LOGGER.info("Issue analysis not found")
            continue

        await _modify_concern_category_for_issue_analysis(
            session,
            issue_analysis,
            categories,
            category_index_tracker,
            concern_type,
            category,
        )

    await session.commit()
