[pytest]
filterwarnings = ignore::pytest.PytestUnraisableExceptionWarning
addopts = -n 4 -p no:ddtrace -p no:ddtrace.pytest_bdd -p no:ddtrace.pytest_benchmark --cov=src/service --cov-report=term-missing --cov-report=xml --cov-report=html --cov-fail-under=88 --cov-config=tests/.coveragerc
asyncio_mode = auto
asyncio_default_fixture_loop_scope = session
asyncio_default_test_loop_scope = session
testpaths = tests

