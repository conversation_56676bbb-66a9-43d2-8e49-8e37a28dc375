# service info
service_name=rat-logic-service
service_environment=test
service_port=8084

# logging
json_log_enabled=False

# database
db_name=rat-logic-service-test
db_username=service_user
db_password=jn4rVmxekFgLEXVC
db_hostname=localhost
db_enforce_ssl_mode=False
db_pool_size=0
db_max_overflow=0


# services mapping
file_manager_service_url=http://127.0.0.1:8083
source_service_url=http://127.0.0.1:8081
config_service_url=http://127.0.0.1:8085
notification_service_url=http://127.0.0.1:8086
rat_logic_service_url=http://127.0.0.1:8000
fetcher_service_url=http://127.0.0.1:8082
chatbot_service_url=http://127.0.0.1:8089
policy_service_url=http://127.0.0.1:8090
security_review_service_url=http://127.0.0.1:8091

# redis
redis_hostname=localhost
redis_ssl=False

# redis celery
celery_redis__redis_hostname=localhost
celery_redis__redis_ssl=False

# prime-jobs
jobs_image_url=''
use_spawner=true
use_scheduler=false
use_watchdog=true

# rabbitmq
rabbitmq_hostname=localhost
rabbitmq_port=5672
rabbitmq_use_ssl=false
rabbitmq_username=guest
rabbitmq_password=guest

# disable datadog
datadog_enabled=false
