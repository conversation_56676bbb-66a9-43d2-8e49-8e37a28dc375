import json
import os
from unittest.mock import patch

import pytest
from mock.mock import ANY, AsyncMock
from prime_file_manager_service_client import FileIDData, FileUploadMetadataRequest
from prime_jobs import SchedulerDAL
from prime_tests import EnvSave, MockResponse
from prime_tests.request_mocker import RequestData

from service.db import ServiceDAL
from service.job_type import JobType
from service.k8s_jobs.build_fields_data_job.build_fields_data import BuildFieldsDataJobLogic
from service.k8s_jobs.build_fields_data_job.main import main
from service.k8s_jobs.build_fields_data_job.models import BuildFieldsDataJobArgs
from tests.mock_utils import SOURCE_ID, _basic_mocks, mock_get_issues_files

from ..job_test_utils import create_job


def get_request_data(request: RequestData) -> list[bytes]:
    return [upload_file[1][1] for upload_file in request.args[4]]


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestBuildFieldsData:
    async def test_build_logic(
        self, service_dal_fixture: ServiceDAL, scheduler_dal_fixture: SchedulerDAL, account_id: str
    ):
        with _basic_mocks() as (file_manager_mocker, source_mocker, config_mocker):
            mock_get_issues_files(file_manager_mocker)
            new_job = await create_job(JobType.BUILD_FIELDS_DATA, scheduler_dal_fixture)
            resp_data = FileIDData(origin_id="fields_data.json", id=0, file_path="file_path").model_dump()
            file_manager_mocker.put(f"/files/{account_id}", MockResponse(status_code=200, content=[resp_data]))
            job_logic = await BuildFieldsDataJobLogic.build(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
            )
            assert await job_logic.start() is None
            fields_data_content = []
            for file_upload_request in file_manager_mocker.requests["PUT"][f"files/{account_id}"]:
                metadata = FileUploadMetadataRequest.model_validate_json(get_request_data(file_upload_request)[0])
                if metadata.origin_id == "fields_data.json":
                    fields_data_content.append(get_request_data(file_upload_request)[1])
            assert len(fields_data_content) == 1
            expected_json = {
                "status": ["Open", "In Progress", "Closed"],
                "creator": ["John Smith the 3rd", "Jane Doe the 2nd", "John Doe", "Jane Smith the 4th"],
                "field_2": ["30", "20", "10", "40"],
                "field_3": ["0"],
                "field_4": ["False", "True"],
                "labels": ["label2", "label3", "label1", "label7", "label10"],
                "field_array_optional": ["optional1", "optional2"],
                "issuetype": ["Initiative", "Epic", "Subtask", "Task"],
                "project": ["PORJ", "prim"],
            }
            actual_json = json.loads(fields_data_content[0])
            for key, value in expected_json.items():
                assert set(actual_json[key]) == set(value)
            assert actual_json.keys() == expected_json.keys()

    async def test_main_logic(self, account_id: str):
        with patch.object(BuildFieldsDataJobLogic, "build", new_callable=AsyncMock) as mock_build, EnvSave():
            job_instance_mock = AsyncMock()
            mock_build.return_value = job_instance_mock
            args = BuildFieldsDataJobArgs(account_id=account_id, source_id=SOURCE_ID, job_id=1)
            os.environ.update({key: str(value) for key, value in args.model_dump(exclude_none=True).items()})
            await main()
            mock_build.assert_called_once_with(account_id=account_id, source_id=SOURCE_ID, job_id=1, service_dal=ANY)
            job_instance_mock.start.assert_called_once()
