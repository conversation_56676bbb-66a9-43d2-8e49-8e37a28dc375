import pytest
from prime_jobs import <PERSON><PERSON><PERSON><PERSON>, JobStatus

from service.db import <PERSON>DA<PERSON>
from service.k8s_jobs.job_spawners import (
    ClassificationJobSpawner,
    SummaryJobSpawner,
    UpdateIssuesJobSpawner,
)
from tests.mock_utils import SOURCE_ID


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
@pytest.mark.parametrize(
    "spawner",
    [ClassificationJobSpawner, SummaryJobSpawner, UpdateIssuesJobSpawner],
)
async def test_jira_spawn_jira_job_already_running(service_dal_fixture: ServiceDAL, spawner, account_id: str):
    old_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
        spawner,
        CreateJob(account_id=account_id, created_by="test", job_type=spawner.NAME, job_args={"source_id": SOURCE_ID}),
    )
    old_job.status = JobStatus.RUNNING

    new_job1 = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
        spawner,
        CreateJob(account_id=account_id, created_by="test", job_type=spawner.NAME, job_args={"source_id": SOURCE_ID}),
    )
    await service_dal_fixture.session.commit()
    res = await spawner(account_id, SOURCE_ID, False).spawn_job(new_job1.id)
    assert res == f"job-{new_job1.id}-canceled"
    await service_dal_fixture.session.refresh(new_job1)
    assert new_job1.status == JobStatus.CANCELED
    assert new_job1.cancel_reason == "FLOW_ALREADY_RUNNING"
