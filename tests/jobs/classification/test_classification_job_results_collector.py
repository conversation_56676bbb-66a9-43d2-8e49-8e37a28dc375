import asyncio
from unittest.mock import patch

import pytest
from celery.contrib.pytest import celery_worker
from prime_gen_ai_service_client import AIPipelinesOutput
from prime_redis_utils import AsyncPrefixRedisClient
from prime_tests import service_mocker

from service.db import CaseTable, ServiceDA<PERSON>
from service.k8s_jobs.classification_job.classification_issues_tracker import ClassificationIssuesTracker
from service.k8s_jobs.classification_job.classification_results_collector import (
    ClassificationMetricsReporter,
    accept_result,
    get_issue_links_by_id,
)
from service.k8s_jobs.classification_job.models import GenAITaskMetaData
from service.logic.filters_and_sort import CaseFilters
from service.logic.jira_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from service.models import IssueLinkType
from tests.case_test_utils import create_case
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    JOB_ID,
    SOURCE_ID,
    get_classification_output,
    get_issue_id,
    mock_get_descendants_links,
)

fixture_ = celery_worker  # this avoid ruff to remove the import

TEST_TASK_ID = "test_task_id"


def get_metadata(issue_id: str, case_id: int) -> GenAITaskMetaData:
    metadata = GenAITaskMetaData.model_validate(
        {
            "account_id": ACCOUNT_ID_CONTEXT.get(),
            "source_id": SOURCE_ID,
            "issue_id": "",
            "case_id": 1,
            "job_id": JOB_ID,
            "issue_hash": "test_issue_hash",
            "ai_version": "test_version",
            "results_queue_name": "test_queue_name",
        }
    )
    ret_val = metadata.model_copy()
    ret_val.issue_id = issue_id
    ret_val.case_id = case_id
    return ret_val


async def collector_flow(
    service_dal, gen_ai_response: AIPipelinesOutput, headers: GenAITaskMetaData
) -> list[CaseTable]:
    queue = f"ai_results__{ACCOUNT_ID_CONTEXT.get()}_{SOURCE_ID}"
    result = accept_result.apply_async(args=[gen_ai_response.model_dump()], headers=headers.model_dump(), queue=queue)
    assert result.get(timeout=10) is None
    await asyncio.sleep(0.3)
    service_dal.session.expire_all()
    case_filters = CaseFilters([CaseFilters.IS_SECURITY_FILTER, CaseFilters.STATUS_OPEN_FILTER])
    return await service_dal.cases_dal.get_cases_by(ACCOUNT_ID_CONTEXT.get(), SOURCE_ID, case_filters=case_filters)


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
@pytest.mark.usefixtures("celery_worker")
@patch(
    "service.k8s_jobs.classification_job.classification_results_collector.get_issue_links_by_id",
    return_value=[],
)
class TestClassificationJobResultsCollector:
    async def test_collector_security(
        self,
        _,
        account_redis_client: AsyncPrefixRedisClient,
        service_dal_fixture: ServiceDAL,
        issue_manager: JiraIssuesManager,
    ):
        with patch.object(ClassificationMetricsReporter, "increment_analyzed_items") as mock_increment_analyzed_items:
            test_issue_id = get_issue_id(1)
            case = await create_case(service_dal_fixture, test_issue_id, with_analysis=False)
            tracker = ClassificationIssuesTracker(account_redis_client, SOURCE_ID, JOB_ID)
            await tracker.add_issue_sent_to_ai(test_issue_id, TEST_TASK_ID)
            response = get_classification_output(is_security=True)
            cases = await collector_flow(service_dal_fixture, response, get_metadata(test_issue_id, case.id))
            assert cases[0].issue_analysis_id is not None
            mock_increment_analyzed_items.assert_called_once()

    async def test_collector_non_security(
        self,
        _,
        account_redis_client: AsyncPrefixRedisClient,
        service_dal_fixture: ServiceDAL,
        issue_manager: JiraIssuesManager,
    ):
        with patch.object(ClassificationMetricsReporter, "increment_analyzed_items") as mock_increment_analyzed_items:
            test_issue_id = get_issue_id(1)
            case = await create_case(service_dal_fixture, test_issue_id, with_analysis=False)
            tracker = ClassificationIssuesTracker(account_redis_client, SOURCE_ID, JOB_ID)
            await tracker.add_issue_sent_to_ai(test_issue_id, TEST_TASK_ID)
            cases = await collector_flow(
                service_dal_fixture, get_classification_output(is_security=False), get_metadata(test_issue_id, case.id)
            )
            assert len(cases) == 0
            mock_increment_analyzed_items.assert_called_once()

    async def test_orphan_task(self, _, service_dal_fixture: ServiceDAL, issue_manager: JiraIssuesManager):
        with patch.object(ClassificationMetricsReporter, "increment_analyzed_items") as mock_increment_analyzed_items:
            test_issue_id = get_issue_id(1)
            case = await create_case(service_dal_fixture, test_issue_id, with_analysis=False)
            cases = await collector_flow(
                service_dal_fixture, get_classification_output(is_security=True), get_metadata(test_issue_id, case.id)
            )
            assert len(cases) == 1
            assert cases[0].issue_analysis_id is not None
            mock_increment_analyzed_items.assert_called_once()

    @pytest.mark.parametrize(
        "gen_ai_response, headers",
        [
            (None, {}),
            (get_classification_output().model_dump(), {}),
        ],
    )
    async def test_corrupted_input(
        self, _, account_redis_client: AsyncPrefixRedisClient, gen_ai_response, headers, account_id: str
    ):
        tracker = ClassificationIssuesTracker(account_redis_client, SOURCE_ID, JOB_ID)
        with pytest.raises(Exception) as ex:
            result = accept_result.apply_async(
                args=[gen_ai_response], headers=headers, queue=f"ai_results__{account_id}_{SOURCE_ID}"
            )
            result.get()
        assert ex.value.args == ("<class 'pydantic_core._pydantic_core.ValidationError'>([])",)
        assert await tracker.get_job_collector_errors() == []

    async def test_error_gen_ai_input(
        self, _, issue_manager: JiraIssuesManager, account_redis_client: AsyncPrefixRedisClient, account_id: str
    ):
        headers = get_metadata("issue-1", 1).model_dump()
        tracker = ClassificationIssuesTracker(account_redis_client, SOURCE_ID, JOB_ID)
        gen_ai_response = {"error": "something went bad"}
        result = accept_result.apply_async(
            args=[gen_ai_response], headers=headers, queue=f"ai_results__{account_id}_{SOURCE_ID}"
        )
        result.get()
        assert await tracker.get_gen_ai_errors() == ["issue-1"]

    async def test_get_issue_links_by_id(self, issue_manager: JiraIssuesManager, account_id: str):
        issue_id = "ISSUE-20"
        with service_mocker("file-manager-service") as file_manager_mocker:
            children_links = mock_get_descendants_links(file_manager_mocker, account_id)
            links = await get_issue_links_by_id(issue_id, account_id, SOURCE_ID)
            assert len(links) == 4
            assert {link.url for link in links} == {link.link for link in children_links}
            assert {link.link_type for link in links} == {
                IssueLinkType.document_type_to_link_type(link.link_type) for link in children_links
            }
