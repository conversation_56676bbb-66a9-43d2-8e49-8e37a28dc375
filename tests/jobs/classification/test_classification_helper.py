import pytest
from prime_gen_ai_service_client import (
    MitreAttackConcernsFlowOutput,
    PersonalRecommendationsFlowOutput,
    PrivacyConcernsFlowOutput,
)
from prime_shared.common_dataclasses import SecurityFramework

from service.k8s_jobs.classification_job.task_helpers import cia_level_to_score, get_recommendations_and_concerns
from service.k8s_jobs.job_spawners import ClassificationJobSpawner
from service.models import Implementation, IssueAnalysisConcern
from service.models.concerns import (
    ConcernType,
    IssueAnalysisConcernMethodology,
)
from tests.mock_utils import SOURCE_ID, get_classification_output

test_json = {
    "personal_recommendations": [
        {
            "concern": {
                "group_name": "Concern group name 1",
                "concern_text": "Concern text 1",
                "concern_id": "Mitre-Con-101",
            },
            "recommendations": [
                {
                    "id": 1,
                    "title": "recommendation title 1",
                    "description": "recommendation description 1",
                    "actionable_recommendations": [
                        {
                            "id": 1,
                            "text": "actionable recommendations description 1",
                            "code_snippet": None,
                            "ppt_category": "T",
                            "controls": {
                                "NIST": [
                                    {
                                        "id_": "CA-07(05)",
                                        "actionable_recommendations": [
                                            {
                                                "text": "This is what nist says",
                                                "raci": [
                                                    "GRC department",
                                                ],
                                            },
                                        ],
                                    },
                                ],
                                "HITRUST": [],
                                "PCI": [],
                                "CIS": [],
                            },
                            "raci": [{"name": "GRC department"}],
                        }
                    ],
                    "controls": {
                        "NIST": [
                            {
                                "id_": "CA-07(05)",
                                "actionable_recommendations": [
                                    {
                                        "text": "This is what nist says",
                                        "raci": [
                                            "GRC department",
                                        ],
                                    },
                                ],
                            },
                        ],
                        "HITRUST": [],
                        "PCI": [],
                        "CIS": [],
                    },
                }
            ],
        },
        {
            "concern": {
                "group_name": "linndun privacy group name",
                "concern_text": "linndun privacy text",
                "concern_id": "Con-104",
            },
            "recommendations": [
                {
                    "id": 1,
                    "title": "recommendation title 2",
                    "description": "recommendation title description 2",
                    "actionable_recommendations": [
                        {
                            "id": 1,
                            "text": "actionable recommendations description 2",
                            "code_snippet": None,
                            "ppt_category": "T",
                            "raci": [{"name": "GRC department"}],
                        }
                    ],
                    "controls": {
                        "NIST": [
                            {
                                "id_": "CA-07(05)",
                                "actionable_recommendations": [
                                    {
                                        "text": "This is what nist says",
                                        "raci": [
                                            "GRC department",
                                        ],
                                    },
                                ],
                            },
                        ],
                        "HITRUST": [],
                        "PCI": [],
                        "CIS": [],
                    },
                }
            ],
        },
    ],
    "privacy_concerns": {
        "concerns": [
            {
                "concern_id": "Con-104",
                "group_name": "linndun privacy group name",
                "concern_text": "linndun privacy text",
                "linddun_category": "Disclosure of information",
                "score": "high",
            },
        ],
    },
    "mitre_attack_concerns": {
        "concerns": [
            {
                "concern_id": "Mitre-Con-101",
                "group_name": "Concern group name 1",
                "concern_text": "Concern text 1",
                "tactic_id": "TA0009",
                "score": "high",
            },
        ]
    },
    "concerns_str": ["concern1", "concern2"],
}

expected_recommendations = [
    {
        "id": 1,
        "concern_id": 2,
        "control_id": "CA-07(05)",
        "recommendation": "This is what nist says",
        "status": "unknown",
        "raci": ["GRC department"],
        "code_snippets": {},
        "controls": {},
    },
    {
        "id": 2,
        "concern_id": 1,
        "control_id": "CA-07(05)",
        "recommendation": "This is what nist says",
        "status": "unknown",
        "raci": ["GRC department"],
        "code_snippets": {},
        "controls": {},
    },
    {
        "id": 3,
        "concern_id": 2,
        "control_id": "1",
        "controls": {"NIST": {"CA-07(05)"}, "HITRUST": [], "PCI": [], "CIS": []},
        "recommendation": "actionable recommendations description 1",
        "status": "unknown",
        "raci": ["GRC department"],
        "code_snippets": {},
    },
    {
        "id": 4,
        "concern_id": 1,
        "control_id": "2",
        "recommendation": "actionable recommendations description 2",
        "status": "unknown",
        "raci": ["GRC department"],
        "code_snippets": {},
    },
]


class TestClassificationSpawner:
    def test_classification_job_name(self, account_id: str):
        sanitized_account_id = account_id.replace("_", "-").lower()
        job_name = ClassificationJobSpawner(account_id, SOURCE_ID, False).get_job_name(1)
        assert job_name == f"classification-job-{sanitized_account_id}-{SOURCE_ID}"

    def test_classification_job_name_long_account_id(self):
        account_id = "1234567890123456789012345678901234567890123456789012345678901234"
        job_name = ClassificationJobSpawner(account_id, SOURCE_ID, False).get_job_name(1)
        assert len(job_name) == 53
        assert job_name == f"classification-job-{account_id}"[:53]


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestClassificationLogic:
    def test_classification_get_recommendation(self):
        personal_recommendations = PersonalRecommendationsFlowOutput.model_validate(test_json)
        privacy_concerns = PrivacyConcernsFlowOutput.model_validate(test_json["privacy_concerns"])
        mitre_concerns = MitreAttackConcernsFlowOutput.model_validate(test_json["mitre_attack_concerns"])
        _, _, actual_recommendations = get_recommendations_and_concerns(
            personal_recommendations, privacy_concerns, mitre_concerns
        )

        expected_implementations = [
            Implementation.model_validate(recommendation) for recommendation in expected_recommendations
        ]
        assert actual_recommendations == expected_implementations

    def test_classification_get_concerns(self):
        personal_recommendations = PersonalRecommendationsFlowOutput.model_validate(test_json)
        privacy_concerns = PrivacyConcernsFlowOutput.model_validate(test_json["privacy_concerns"])
        mitre_concerns = MitreAttackConcernsFlowOutput.model_validate(test_json["mitre_attack_concerns"])

        actual_concerns, _, _ = get_recommendations_and_concerns(
            personal_recommendations, privacy_concerns, mitre_concerns
        )
        expected = [
            IssueAnalysisConcern(
                id=1,
                short_description="linndun privacy group name",
                long_description="linndun privacy text",
                methodology=IssueAnalysisConcernMethodology(
                    category="Disclosure of information",
                    type=ConcernType.LINDDUN,
                ),
            ),
            IssueAnalysisConcern(
                id=2,
                short_description="Concern group name 1",
                long_description="Concern text 1",
                methodology=IssueAnalysisConcernMethodology(
                    category="TA0009",
                    type=ConcernType.MITRE,
                ),
            ),
        ]
        assert actual_concerns == expected

    def test_classification_get_controls(self):
        output = get_classification_output(is_security=True, with_recommendations=False)
        actual_concerns, actual_controls, actual_recommendations = get_recommendations_and_concerns(
            output.personal_recommendations, output.privacy_concerns, output.mitre_attack_concerns
        )
        assert len(actual_concerns) > 0
        assert len(actual_controls) == 0
        assert len(actual_recommendations) == 0

        output = get_classification_output(is_security=True, with_recommendations=True)
        actual_concerns, actual_controls, actual_recommendations = get_recommendations_and_concerns(
            output.personal_recommendations, output.privacy_concerns, output.mitre_attack_concerns
        )
        assert len(actual_concerns) > 0
        assert len(actual_controls) > 0
        assert len(actual_recommendations) > 0
        assert all(control.framework == SecurityFramework.PRIME for control in actual_controls)

    def test_cia_level_to_score(self):
        assert cia_level_to_score("HIGH") == 10
        assert cia_level_to_score("High") == 10
        assert cia_level_to_score("Low") == 1
        assert cia_level_to_score("medium") == 5
        assert cia_level_to_score(None) is None
