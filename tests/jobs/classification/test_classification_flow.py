import contextlib
from collections.abc import Iterator
from datetime import UTC, datetime, timedelta
from typing import Any
from unittest.mock import Mock, patch

import pytest
from packaging.version import parse
from prime_file_manager_service_client import DocumentType
from prime_gen_ai_service_client import (
    AIPipelinesInput,
    CeleryHeaders,
)
from prime_notification_service_client import NotifyResponse
from prime_source_service_client import SourceType
from prime_tests import MockR<PERSON>ponse, service_mocker

from service.db import CaseTable, ServiceDAL, get_service_dal_context
from service.job_type import JobType
from service.k8s_jobs.base_job_logic import BaseJobLogic, GenAIBaseJob
from service.k8s_jobs.base_job_logic.gen_ai_celery import (
    GENAI_PIPELINE_RUN_TASK_NAME,
)
from service.k8s_jobs.base_job_logic.job_issue_manager_factory import IssueManagerFactory
from service.k8s_jobs.classification_job.classification_job_logic import ClassificationJobLogic
from service.k8s_jobs.classification_job.models import (
    ClassificationJobSummaryReport,
)
from service.logic.jira_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from tests.case_test_utils import create_cases
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    JIRA_LEAF_ISSUES,
    SOURCE_ID,
    TESTS_ISSUES,
    _basic_mocks,
    get_prime_issue,
    mock_get_descendants_empty,
    mock_get_issues_files,
)

from ..job_test_utils import create_job, mock_version, modify_cases_hash

FILE_TIMESTAMP = datetime.now(UTC) - timedelta(minutes=10)


@contextlib.contextmanager
def _gen_ai_mock(gen_ai_version: str = "0.0.0") -> Iterator[list[tuple[AIPipelinesInput, dict[str, Any]]]]:
    messages: list[Any] = []

    def _side_effect(name: str, kwargs: dict[str, Any] | None = None, **options: Any):
        def _get(timeout: int = 60):
            return "ok"

        if name == GENAI_PIPELINE_RUN_TASK_NAME:
            pipeline_input = AIPipelinesInput.model_validate(kwargs["task_input_dict"])
            request = (pipeline_input, options["headers"])
            messages.append(request)

        return Mock(get=_get)

    with (
        patch("service.k8s_jobs.classification_job.classification_job_logic.get_celery_app") as mock_get_celery_app,
        mock_version(gen_ai_version),
    ):
        mock_celery_app = mock_get_celery_app.return_value
        mock_celery_app.send_task.side_effect = _side_effect
        yield messages


async def _calculate_real_cases_hash(cases: list[CaseTable]):
    async with get_service_dal_context() as service_dal:
        for case in cases:
            jira_issue = await JiraIssuesManager(ACCOUNT_ID_CONTEXT.get(), SOURCE_ID).get_issue(
                case.issue_id, with_parent=True
            )
            issue_analysis = await service_dal.issues_analysis_dal.get(case.account_id, case.source_id, case.issue_id)
            issue_analysis.issue_hash = BaseJobLogic.get_issue_hash(jira_issue)
            await service_dal.session.commit()


@contextlib.contextmanager
def _classification_job_mocks(
    files_timestamp: datetime = FILE_TIMESTAMP,
    gen_ai_version: str = "0.0.0",
    source_type: SourceType = SourceType.JIRA,
):
    document_type = DocumentType.JIRA if source_type == SourceType.JIRA else DocumentType.ISSUE
    IssueManagerFactory._get_source_type.cache_invalidate()
    IssueManagerFactory._get_source_type.cache_clear()
    with (
        _basic_mocks() as (file_manager_mocker, source_mocker, config_mocker),
        _notification_mock(fail_notification=False),
        _mock_wait_for_results(),
        _gen_ai_mock(gen_ai_version) as gen_ai_messages,
    ):
        mock_get_issues_files(file_manager_mocker, ACCOUNT_ID_CONTEXT.get(), files_timestamp, document_type)
        mock_get_descendants_empty(file_manager_mocker, ACCOUNT_ID_CONTEXT.get())
        yield file_manager_mocker, source_mocker, config_mocker, gen_ai_messages


@contextlib.contextmanager
def _mock_wait_for_results():
    with patch("service.k8s_jobs.classification_job.classification_job_logic.TasksWatchdog") as mock_watchdog:
        mock_watchdog_instance = mock_watchdog.return_value

        async def mock_wait_for_results():
            result = ClassificationJobSummaryReport(
                execution_status=True,
                issues_total=4,
                issues_downloaded=4,
                issues_sent_to_ai=4,
                error_issues_not_handled=0,
                duration_in_seconds=0,
                issues_need_processing=0,
                old_issues_total=0,
                old_issues_left=0,
                error_job_collector=0,
                error_gen_ai=0,
                error_classification=0,
            )
            return result

        mock_watchdog_instance.wait_for_results = mock_wait_for_results
        yield


@contextlib.contextmanager
def _notification_mock(fail_notification: bool):
    with service_mocker("notification-service") as notification_mocker:
        good_resp = MockResponse(NotifyResponse(sent=True).to_json())
        bad_resp = MockResponse("test", status_code=500)
        notification_mocker.post(
            f"/scan?account_id={ACCOUNT_ID_CONTEXT.get()}", bad_resp if fail_notification else good_resp
        )
        yield notification_mocker


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestClassificationFlow:
    async def test_classification_job_run(self, service_dal_fixture: ServiceDAL, account_id: str):
        await create_cases(service_dal_fixture, with_analysis=False)
        new_job = await create_job(JobType.CLASSIFICATION, service_dal_fixture.scheduler_dal)
        with _classification_job_mocks(datetime.now(UTC) + timedelta(minutes=10)) as (_, _, _, gen_ai_messages):
            job_logic = ClassificationJobLogic(
                account_id=account_id,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                source_id=SOURCE_ID,
                force=False,
            )
            assert await job_logic.start() is None
            assert len(gen_ai_messages) == len(JIRA_LEAF_ISSUES)
            assert {message[0].context_issue.id for message in gen_ai_messages} == JIRA_LEAF_ISSUES
            test_issue = get_prime_issue(TESTS_ISSUES[3])
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, test_issue.attributes.id_)
            expected_pipeline_input = AIPipelinesInput(
                context_issue=await GenAIBaseJob.get_context_issue_from_db(
                    test_issue, service_dal_fixture, account_id, SOURCE_ID
                ),
                code_snippet_language=None,
                with_recommendations=False,
            )
            expected_headers = CeleryHeaders(
                account_id=account_id,
                source_id=SOURCE_ID,
                issue_id=test_issue.attributes.id_,
                job_id=new_job.id,
                results_queue_name=f"ai_results__{account_id}_{SOURCE_ID}",
                user_id="test",
                additional_properties={
                    "issue_hash": GenAIBaseJob.get_issue_hash(test_issue),
                    "ai_version": "0.0.0",
                    "case_id": case.id,
                },
            )
            assert (expected_pipeline_input, expected_headers.to_dict()) in gen_ai_messages

    async def test_classification_not_send_container_cases_to_ai(
        self, service_dal_fixture: ServiceDAL, account_id: str
    ):
        await create_cases(service_dal_fixture, with_analysis=False)
        new_job = await create_job(JobType.CLASSIFICATION, service_dal_fixture.scheduler_dal)
        with _classification_job_mocks(datetime.now(UTC) + timedelta(minutes=10)) as (_, _, _, gen_ai_messages):
            job_logic = ClassificationJobLogic(
                account_id=account_id,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                source_id=SOURCE_ID,
                force=False,
            )
            assert await job_logic.start() is None
            assert len(gen_ai_messages) == len(JIRA_LEAF_ISSUES)
            assert {message[0].context_issue.id for message in gen_ai_messages} == JIRA_LEAF_ISSUES

    async def test_classification_job_issue_not_changed(self, service_dal_fixture: ServiceDAL, account_id: str):
        new_job = await create_job(JobType.CLASSIFICATION, service_dal_fixture.scheduler_dal)
        await create_cases(service_dal_fixture)
        with _classification_job_mocks() as (_, _, _, gen_ai_messages):
            job_logic = ClassificationJobLogic(
                account_id=account_id,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                source_id=SOURCE_ID,
                force=False,
            )
            assert len(await job_logic._get_issues_to_process()) == 0
            await job_logic.start()
            assert len(gen_ai_messages) == 0

    async def test_classification_job_issue_timestamp_updated(self, service_dal_fixture: ServiceDAL, account_id: str):
        new_job = await create_job(JobType.CLASSIFICATION, service_dal_fixture.scheduler_dal)
        cases = await create_cases(service_dal_fixture)
        with _classification_job_mocks(datetime.now(UTC) + timedelta(minutes=10)) as (_, _, _, gen_ai_messages):
            job_logic = ClassificationJobLogic(
                account_id=account_id,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                source_id=SOURCE_ID,
                force=False,
            )

            assert len(await job_logic._get_issues_to_process()) == len(JIRA_LEAF_ISSUES)
            await job_logic.start()
            assert len(gen_ai_messages) == 0

            await modify_cases_hash(cases)
            await job_logic.start()
            assert len(gen_ai_messages) == len(JIRA_LEAF_ISSUES)
            assert {message[0].context_issue.id for message in gen_ai_messages} == JIRA_LEAF_ISSUES

    @pytest.mark.parametrize(
        "current_version, next_version, expected",
        [
            ("0.0.0", "1.0.0", True),
            ("1.0.0", "0.0.0", False),
            ("0.1.0", "1.0.0", True),
            ("1.0.0", "1.0.0", False),
            ("1.0.1", "1.0.0", False),
            ("1.0.1", "1.1.0", True),
            ("1.0.1", "1.4.0", True),
            ("1.4.0", "2.0.0", True),
        ],
    )
    async def test_gen_ai_version_changed(self, current_version: str, next_version: str, expected: bool):
        assert GenAIBaseJob.version_changed(current_version, parse(next_version)) is expected

    async def test_classification_job_gen_ai_version_changed(self, service_dal_fixture: ServiceDAL, account_id: str):
        new_job = await create_job(JobType.CLASSIFICATION, service_dal_fixture.scheduler_dal)
        GenAIBaseJob.get_ai_version.cache_clear()
        await create_cases(service_dal_fixture)
        with _classification_job_mocks(gen_ai_version="1.0.0") as (_, _, _, gen_ai_messages):
            job_logic = ClassificationJobLogic(
                account_id=account_id,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                source_id=SOURCE_ID,
                force=False,
            )
            assert len(await job_logic._get_issues_to_process()) == len(JIRA_LEAF_ISSUES)
            await job_logic.start()
            assert len(gen_ai_messages) == len(JIRA_LEAF_ISSUES)
            assert {message[0].context_issue.id for message in gen_ai_messages} == JIRA_LEAF_ISSUES

    async def test_classification_job_force(self, service_dal_fixture: ServiceDAL, account_id: str):
        new_job = await create_job(JobType.CLASSIFICATION, service_dal_fixture.scheduler_dal)
        with _classification_job_mocks() as (_, _, _, gen_ai_messages):
            await create_cases(service_dal_fixture)
            job_logic = ClassificationJobLogic(
                account_id=account_id,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                source_id=SOURCE_ID,
                force=True,
            )
            await job_logic.start()
            assert len(gen_ai_messages) == len(JIRA_LEAF_ISSUES)
            assert {message[0].context_issue.id for message in gen_ai_messages} == JIRA_LEAF_ISSUES

    async def test_classification_job_run_parent_id_classification_flow(
        self, service_dal_fixture: ServiceDAL, account_id: str
    ):
        await create_cases(service_dal_fixture, with_analysis=False)
        new_job = await create_job(JobType.CLASSIFICATION, service_dal_fixture.scheduler_dal)
        with _classification_job_mocks() as (file_manager_mocker, _, _, gen_ai_messages):
            job_logic = ClassificationJobLogic(
                account_id=account_id,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                source_id=SOURCE_ID,
                force=False,
                parent_id="ISSUE-1",
            )
            assert await job_logic.start() is None
            assert len(gen_ai_messages) == 3
            expected_issues = {"ISSUE-3", "ISSUE-5", "ISSUE-6"}
            assert {message[0].context_issue.id for message in gen_ai_messages} == expected_issues
