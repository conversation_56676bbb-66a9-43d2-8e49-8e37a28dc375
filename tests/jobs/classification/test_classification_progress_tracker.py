from unittest.mock import AsyncMock, patch

import pytest
from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import SourceIdType

from service.k8s_jobs.classification_job.classification_issues_tracker import ClassificationIssuesTracker
from tests.mock_utils import SOURCE_ID

TEST_TASK_ID = "test_task_id"


@pytest.fixture
def test_params():
    """
    Fixture to provide common test parameters.
    """
    source_id: SourceIdType = SOURCE_ID
    job_id = 123
    issue_id1 = "issue1"
    issue_id2 = "issue2"
    return source_id, job_id, issue_id1, issue_id2


@pytest.fixture
async def tracker(account_redis_client: AsyncPrefixRedisClient, test_params):
    """
    Fixture to create and clean up a ClassificationProgressTracker instance.
    Ensures each test works with a clean Redis state.
    """
    source_id, job_id, _, _ = test_params
    tracker_instance = ClassificationIssuesTracker(account_redis_client, source_id, job_id)
    # Ensure a clean state before the test
    await tracker_instance.cleanup()
    await tracker_instance._setup()
    yield tracker_instance
    # Clean up after the test
    await tracker_instance.cleanup()


async def test_setup(tracker: ClassificationIssuesTracker):
    num_issues = await tracker.get_num_issues_pending()
    assert num_issues == 0, "Initial number of issues should be 0 after setup."


async def test_cleanup(tracker: ClassificationIssuesTracker):
    with patch.object(tracker, "_setup", new_callable=AsyncMock) as mock_setup:
        mock_setup.assert_not_called()
        await tracker.cleanup()
        mock_setup.assert_called_once()


async def test_issues_lifecycle(tracker: ClassificationIssuesTracker, test_params):
    num_issues = await tracker.get_num_issues_pending()
    assert num_issues == 0, "Initial number of issues should be 0."

    issue_id1 = test_params[2]
    await tracker.add_issue_sent_to_ai(issue_id1, TEST_TASK_ID)
    num_issues = await tracker.get_num_issues_pending()
    assert num_issues == 1, "Number of issues should be 1 after adding one issue."
    issue_ids = await tracker.get_pending_issue_ids()
    assert issue_ids == [issue_id1], "Issue IDs should contain the added issue."

    # test add another issue
    issue_id2 = test_params[3]
    await tracker.add_issue_sent_to_ai(issue_id2, TEST_TASK_ID)
    num_issues = await tracker.get_num_issues_pending()
    assert num_issues == 2, "Number of issues should be 2 after adding two issues."
    issue_ids = await tracker.get_pending_issue_ids()
    assert sorted(issue_ids) == sorted([issue_id1, issue_id2]), "Issue IDs should contain all added issues."

    # test remove issue
    await tracker.issue_collected(issue_id1)
    num_issues = await tracker.get_num_issues_pending()
    assert num_issues == 1, "Number of issues should be 1 after removing one issue."
    issue_ids = await tracker.get_pending_issue_ids()
    assert issue_ids == [issue_id2], "Issue IDs should contain only the remaining issue."
