import contextlib
import json
import zipfile
from datetime import UTC, datetime
from io import Bytes<PERSON>
from unittest import mock

from prime_file_manager_service_client import DocumentType, FileInfo
from prime_tests import MockResponse, service_mocker

from service.k8s_jobs.base_job_logic import PageDownloader
from tests.mock_utils import SOURCE_ID, resources_dir


@contextlib.contextmanager
def mock_get_descendants(account_id: str):
    confluence_file = "********"
    google_file_name_pdf = "1a2bRcNSiZEj3kGAxb3JxzATZDMnb9B78jcx6poYLIgc"
    google_file_name_txt = "1BQvoW35g8hvxD3CB-yuWcZjWKKCjKGxC"
    google_empty_file_name_txt = "1BQvoW35uEmptyuuWcZjWKKCjKGxC"
    file_not_found = "********"
    source_for_file_not_found = SOURCE_ID + 1
    response = [
        FileInfo(
            id=0,
            origin_id=google_empty_file_name_txt,
            timestamp=datetime.now(UTC),
            domain="primesec.ai",
            downloadable_link="found",
            document_type=DocumentType.GDRIVE,
            source_id=SOURCE_ID,
        ).model_dump(mode="json"),
        FileInfo(
            id=0,
            origin_id=google_file_name_txt,
            timestamp=datetime.now(UTC),
            domain="primesec.ai",
            downloadable_link="found",
            document_type=DocumentType.GDRIVE,
            source_id=SOURCE_ID,
        ).model_dump(mode="json"),
        FileInfo(
            id=0,
            origin_id=google_file_name_pdf,
            timestamp=datetime.now(UTC),
            domain="primesec.ai",
            downloadable_link="found",
            document_type=DocumentType.GDRIVE,
            source_id=SOURCE_ID,
        ).model_dump(mode="json"),
        FileInfo(
            id=0,
            origin_id=f"{confluence_file}.json",
            timestamp=datetime.now(UTC),
            domain="primesec.ai",
            downloadable_link="found",
            document_type=DocumentType.CONFLUENCE,
            source_id=SOURCE_ID,
        ).model_dump(mode="json"),
        FileInfo(
            id=0,
            origin_id=f"{file_not_found}.json",
            timestamp=datetime.now(UTC),
            domain="primesec.ai",
            downloadable_link="not found",
            document_type=DocumentType.CONFLUENCE,
            source_id=source_for_file_not_found,
        ).model_dump(mode="json"),
        FileInfo(
            id=0,
            origin_id="not confluence",
            timestamp=datetime.now(UTC),
            domain="primesec.ai",
            downloadable_link="not confluence",
            document_type=DocumentType.DOCUMENT,
            source_id=SOURCE_ID,
        ).model_dump(mode="json"),
        FileInfo(
            id=0,
            origin_id="no storage",
            timestamp=None,
            domain="primesec.ai",
            downloadable_link="no storage",
            document_type=DocumentType.CONFLUENCE,
            source_id=None,
        ).model_dump(mode="json"),
    ]
    with service_mocker("file-manager-service") as file_manager_mocker:
        file_manager_mocker.get(
            f"/relationship/{account_id}/source/*/origin_id/*/descendants", MockResponse(json.dumps(response))
        )
        file_manager_mocker.get(
            f"/files/{account_id}/data/source/{source_for_file_not_found}",
            MockResponse(json.dumps({"error": "not found"}), status_code=404),
        )

        def get_zip_files():
            zip_buffer = BytesIO()
            with zipfile.ZipFile(zip_buffer, "a") as zip_file:
                zip_file.writestr(
                    google_file_name_pdf, (resources_dir / f"descendants_info/{google_file_name_pdf}").read_bytes()
                )

                zip_file.writestr(
                    f"{confluence_file}.json", (resources_dir / f"descendants_info/{confluence_file}.json").read_bytes()
                )

                zip_file.writestr(
                    google_file_name_txt, (resources_dir / f"descendants_info/{google_file_name_txt}").read_bytes()
                )

                zip_file.writestr(
                    google_empty_file_name_txt,
                    (resources_dir / f"descendants_info/{google_empty_file_name_txt}").read_bytes(),
                )

            zip_buffer.seek(0)
            return zip_buffer.read()

        file_manager_mocker.put(
            f"/files/{account_id}/data/source/{SOURCE_ID}",
            MockResponse(get_zip_files()),
        )
        yield


@contextlib.contextmanager
def mock_download_extra_content_page_errors(fail_count: dict[str, int]):
    def _mock(self, *args, **kwargs):
        fail_count["failed"] += 1

    with mock.patch(
        "service.k8s_jobs.base_job_logic.page_downloader.PageDownloaderMetricsReporter.increment_download_extra_content_page_errors",
        _mock,
    ):
        yield


class TestExtraContentDownloader:
    async def test_extra_content_downloader(self, account_id: str):
        fail_count = {"failed": 0}

        with mock_get_descendants(account_id), mock_download_extra_content_page_errors(fail_count):
            page_downloader = PageDownloader(account_id=account_id, source_id=SOURCE_ID)
            pages = await page_downloader.get_pages("fake_issue")
        assert len(pages) == 3
        assert fail_count["failed"] == 1

        # google pdf
        assert pages[0].file_format == "pdf"
        assert pages[0].file_name == "1a2bRcNSiZEj3kGAxb3JxzATZDMnb9B78jcx6poYLIgc"

        # confluence
        assert pages[1].issue_id == "fake_issue"
        assert pages[1].title == "Project Kickoff: New Customer Portal"

        # google txt
        assert pages[2].file_format == "txt"
        assert pages[2].file_name == "1BQvoW35g8hvxD3CB-yuWcZjWKKCjKGxC"
