import os
from unittest.mock import ANY, AsyncMock, patch

import pytest
from prime_tests import EnvSave

from service.k8s_jobs.classification_job.main import main
from service.k8s_jobs.classification_job.models import ClassificationJobArgs
from tests.mock_utils import SOURCE_ID


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestClassificationMain:
    async def test_main_logic(self, account_id: str):
        with (
            patch(
                "service.k8s_jobs.classification_job.main.ClassificationJobLogic", autospec=True
            ) as mock_classification,
            EnvSave(),
        ):
            job_instance_mock = AsyncMock()
            mock_classification.return_value = job_instance_mock
            args = ClassificationJobArgs(account_id=account_id, source_id=SOURCE_ID, job_id=1, force=False)
            os.environ.update({key: str(value) for key, value in args.model_dump(exclude_none=True).items()})
            await main()
            mock_classification.assert_called_once_with(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=1,
                service_dal=ANY,
                force=False,
                last_run=None,
                parent_id=None,
            )
            job_instance_mock.start.assert_called_once()
