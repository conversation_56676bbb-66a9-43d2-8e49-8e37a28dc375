[{"id": "statuscategorychangedate", "key": "statuscategorychangedate", "name": "Status Category Changed", "custom": false, "orderable": false, "navigable": true, "searchable": true, "clauseNames": ["statusCategoryChangedDate"], "schema": {"type": "datetime", "system": "statuscategorychangedate"}}, {"id": "issuetype", "key": "issuetype", "name": "Issue Type", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["issuetype", "type"], "schema": {"type": "issuetype", "system": "issuetype"}}, {"id": "parent", "key": "parent", "name": "Parent", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": ["parent"]}, {"id": "project", "key": "project", "name": "Project", "custom": false, "orderable": false, "navigable": true, "searchable": true, "clauseNames": ["project"], "schema": {"type": "project", "system": "project"}}, {"id": "customfield_10032", "key": "customfield_10032", "name": "Vulnerability", "untranslatedName": "Vulnerability", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10032]", "Vulnerability"], "schema": {"type": "any", "custom": "com.atlassian.jira.plugins.jira-development-integration-plugin:vulnerabilitycf", "customId": 10032}}, {"id": "customfield_10033", "key": "customfield_10033", "name": "re<PERSON><PERSON><PERSON>-field", "untranslatedName": "re<PERSON><PERSON><PERSON>-field", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10033]", "re<PERSON><PERSON><PERSON>-field", "reubinoff-field[Short text]"], "scope": {"type": "PROJECT", "project": {"id": "10000"}}, "schema": {"type": "string", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield", "customId": 10033}}, {"id": "fixVersions", "key": "fixVersions", "name": "Fix versions", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["fixVersion"], "schema": {"type": "array", "items": "version", "system": "fixVersions"}}, {"id": "customfield_10034", "key": "customfield_10034", "name": "re<PERSON><PERSON><PERSON>-field", "untranslatedName": "re<PERSON><PERSON><PERSON>-field", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10034]", "re<PERSON><PERSON><PERSON>-field", "reubinoff-field[Short text]"], "scope": {"type": "PROJECT", "project": {"id": "10000"}}, "schema": {"type": "string", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield", "customId": 10034}}, {"id": "statusCategory", "key": "statusCategory", "name": "Status Category", "custom": false, "orderable": false, "navigable": true, "searchable": true, "clauseNames": ["statusCategory"]}, {"id": "resolution", "key": "resolution", "name": "Resolution", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["resolution"], "schema": {"type": "resolution", "system": "resolution"}}, {"id": "customfield_99999", "key": "customfield_99999", "name": "security_field", "untranslatedName": "Security Field", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[99999]", "security_field"], "schema": {"type": "boolean", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes", "customId": 99999}}, {"id": "customfield_99998", "key": "customfield_99998", "name": "Important Field", "untranslatedName": "Important Field", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[99999]", "Important Field"], "schema": {"type": "string", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield", "customId": 99998}}, {"id": "lastViewed", "key": "lastViewed", "name": "Last Viewed", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": ["lastViewed"], "schema": {"type": "datetime", "system": "lastViewed"}}, {"id": "watches", "key": "watches", "name": "Watchers", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": ["watchers"], "schema": {"type": "watches", "system": "watches"}}, {"id": "thumbnail", "key": "thumbnail", "name": "Images", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": []}, {"id": "created", "key": "created", "name": "Created", "custom": false, "orderable": false, "navigable": true, "searchable": true, "clauseNames": ["created", "createdDate"], "schema": {"type": "datetime", "system": "created"}}, {"id": "customfield_10020", "key": "customfield_10020", "name": "Sprint", "untranslatedName": "Sprint", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10020]", "Sprint"], "schema": {"type": "array", "items": "json", "custom": "com.pyxis.greenhopper.jira:gh-sprint", "customId": 10020}}, {"id": "customfield_10021", "key": "customfield_10021", "name": "Flagged", "untranslatedName": "Flagged", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10021]", "Flagged", "Flagged[Checkboxes]"], "schema": {"type": "array", "items": "option", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes", "customId": 10021}}, {"id": "customfield_10022", "key": "customfield_10022", "name": "Target start", "untranslatedName": "Target start", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10022]", "Target start"], "schema": {"type": "date", "custom": "com.atlassian.jpo:jpo-custom-field-baseline-start", "customId": 10022}}, {"id": "customfield_10023", "key": "customfield_10023", "name": "Target end", "untranslatedName": "Target end", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10023]", "Target end"], "schema": {"type": "date", "custom": "com.atlassian.jpo:jpo-custom-field-baseline-end", "customId": 10023}}, {"id": "priority", "key": "priority", "name": "Priority", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["priority"], "schema": {"type": "priority", "system": "priority"}}, {"id": "customfield_10024", "key": "customfield_10024", "name": "[CHART] Date of First Response", "untranslatedName": "[CHART] Date of First Response", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["[CHART] Date of First Response", "[CHART] Date of First Response[Date of first response]", "cf[10024]"], "schema": {"type": "datetime", "custom": "com.atlassian.jira.ext.charting:firstresponsedate", "customId": 10024}}, {"id": "customfield_10025", "key": "customfield_10025", "name": "[CHART] Time in Status", "untranslatedName": "[CHART] Time in Status", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["[CHART] Time in Status", "[CHART] Time in Status[Time in Status]", "cf[10025]"], "schema": {"type": "any", "custom": "com.atlassian.jira.ext.charting:timeinstatus", "customId": 10025}}, {"id": "customfield_10026", "key": "customfield_10026", "name": "Locked forms", "untranslatedName": "Locked forms", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10026]", "Locked forms"], "schema": {"type": "number", "custom": "com.atlassian.jira.plugins.proforma-managed-fields:forms-locked-field-cftype", "customId": 10026}}, {"id": "labels", "key": "labels", "name": "Labels", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["labels"], "schema": {"type": "array", "items": "string", "system": "labels"}}, {"id": "customfield_10016", "key": "customfield_10016", "name": "Story point estimate", "untranslatedName": "Story point estimate", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10016]", "Story point estimate"], "schema": {"type": "number", "custom": "com.pyxis.greenhopper.jira:jsw-story-points", "customId": 10016}}, {"id": "customfield_10017", "key": "customfield_10017", "name": "Issue color", "untranslatedName": "Issue color", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10017]", "Issue color"], "schema": {"type": "string", "custom": "com.pyxis.greenhopper.jira:jsw-issue-color", "customId": 10017}}, {"id": "customfield_10018", "key": "customfield_10018", "name": "Parent Link", "untranslatedName": "Parent Link", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10018]", "Parent Link"], "schema": {"type": "any", "custom": "com.atlassian.jpo:jpo-custom-field-parent", "customId": 10018}}, {"id": "customfield_10019", "key": "customfield_10019", "name": "Rank", "untranslatedName": "Rank", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10019]", "Rank"], "schema": {"type": "any", "custom": "com.pyxis.greenhopper.jira:gh-lexo-rank", "customId": 10019}}, {"id": "timeestimate", "key": "timeestimate", "name": "Remaining Estimate", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": ["remainingEstimate", "timeestimate"], "schema": {"type": "number", "system": "timeestimate"}}, {"id": "versions", "key": "versions", "name": "Affects versions", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["affectedVersion"], "schema": {"type": "array", "items": "version", "system": "versions"}}, {"id": "issuelinks", "key": "issuelinks", "name": "Linked Issues", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["issueLink"], "schema": {"type": "array", "items": "issuelinks", "system": "issuelinks"}}, {"id": "assignee", "key": "assignee", "name": "Assignee", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["assignee"], "schema": {"type": "user", "system": "assignee"}}, {"id": "updated", "key": "updated", "name": "Updated", "custom": false, "orderable": false, "navigable": true, "searchable": true, "clauseNames": ["updated", "updatedDate"], "schema": {"type": "datetime", "system": "updated"}}, {"id": "status", "key": "status", "name": "Status", "custom": false, "orderable": false, "navigable": true, "searchable": true, "clauseNames": ["status"], "schema": {"type": "status", "system": "status"}}, {"id": "components", "key": "components", "name": "Components", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["component"], "schema": {"type": "array", "items": "component", "system": "components"}}, {"id": "issuekey", "key": "issuekey", "name": "Key", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": ["id", "issue", "issuekey", "key"]}, {"id": "timeoriginalestimate", "key": "timeoriginalestimate", "name": "Original estimate", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": ["originalEstimate", "timeoriginalestimate"], "schema": {"type": "number", "system": "timeoriginalestimate"}}, {"id": "description", "key": "description", "name": "Description", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["description"], "schema": {"type": "string", "system": "description"}}, {"id": "customfield_10010", "key": "customfield_10010", "name": "Request Type", "untranslatedName": "Request Type", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10010]", "Request Type"], "schema": {"type": "sd-customerrequesttype", "custom": "com.atlassian.servicedesk:vp-origin", "customId": 10010}}, {"id": "customfield_10011", "key": "customfield_10011", "name": "Epic Name", "untranslatedName": "Epic Name", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10011]", "Epic Name"], "schema": {"type": "string", "custom": "com.pyxis.greenhopper.jira:gh-epic-label", "customId": 10011}}, {"id": "customfield_10012", "key": "customfield_10012", "name": "Epic Status", "untranslatedName": "Epic Status", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10012]", "Epic Status"], "schema": {"type": "option", "custom": "com.pyxis.greenhopper.jira:gh-epic-status", "customId": 10012}}, {"id": "customfield_10013", "key": "customfield_10013", "name": "Epic Color", "untranslatedName": "Epic Color", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10013]", "Epic Color"], "schema": {"type": "string", "custom": "com.pyxis.greenhopper.jira:gh-epic-color", "customId": 10013}}, {"id": "customfield_10014", "key": "customfield_10014", "name": "Epic Link", "untranslatedName": "Epic Link", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10014]", "Epic Link"], "schema": {"type": "any", "custom": "com.pyxis.greenhopper.jira:gh-epic-link", "customId": 10014}}, {"id": "timetracking", "key": "timetracking", "name": "Time tracking", "custom": false, "orderable": true, "navigable": false, "searchable": true, "clauseNames": [], "schema": {"type": "timetracking", "system": "timetracking"}}, {"id": "customfield_10015", "key": "customfield_10015", "name": "Start date", "untranslatedName": "Start date", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10015]", "Start date", "Start date[Date]"], "schema": {"type": "date", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker", "customId": 10015}}, {"id": "customfield_10005", "key": "customfield_10005", "name": "Change type", "untranslatedName": "Change type", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10005]", "Change type", "Change type[Dropdown]"], "schema": {"type": "option", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select", "customId": 10005}}, {"id": "customfield_10006", "key": "customfield_10006", "name": "Change risk", "untranslatedName": "Change risk", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10006]", "Change risk", "Change risk[Dropdown]"], "schema": {"type": "option", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select", "customId": 10006}}, {"id": "customfield_10007", "key": "customfield_10007", "name": "Change reason", "untranslatedName": "Change reason", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10007]", "Change reason", "Change reason[Dropdown]"], "schema": {"type": "option", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select", "customId": 10007}}, {"id": "security", "key": "security", "name": "Security Level", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["level"], "schema": {"type": "securitylevel", "system": "security"}}, {"id": "customfield_10008", "key": "customfield_10008", "name": "Actual start", "untranslatedName": "Actual start", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["Actual start", "Actual start[Time stamp]", "cf[10008]"], "schema": {"type": "datetime", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datetime", "customId": 10008}}, {"id": "attachment", "key": "attachment", "name": "Attachment", "custom": false, "orderable": true, "navigable": false, "searchable": true, "clauseNames": ["attachments"], "schema": {"type": "array", "items": "attachment", "system": "attachment"}}, {"id": "customfield_10009", "key": "customfield_10009", "name": "Actual end", "untranslatedName": "Actual end", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["Actual end", "Actual end[Time stamp]", "cf[10009]"], "schema": {"type": "datetime", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datetime", "customId": 10009}}, {"id": "aggregatetimeestimate", "key": "aggregatetimeestimate", "name": "Σ Remaining Estimate", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": [], "schema": {"type": "number", "system": "aggregatetimeestimate"}}, {"id": "summary", "key": "summary", "name": "Summary", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["summary"], "schema": {"type": "string", "system": "summary"}}, {"id": "creator", "key": "creator", "name": "Creator", "custom": false, "orderable": false, "navigable": true, "searchable": true, "clauseNames": ["creator"], "schema": {"type": "user", "system": "creator"}}, {"id": "subtasks", "key": "subtasks", "name": "Sub-tasks", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": ["subtasks"], "schema": {"type": "array", "items": "issuelinks", "system": "subtasks"}}, {"id": "reporter", "key": "reporter", "name": "Reporter", "custom": false, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["reporter"], "schema": {"type": "user", "system": "reporter"}}, {"id": "customfield_10000", "key": "customfield_10000", "name": "Development", "untranslatedName": "development", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10000]", "development"], "schema": {"type": "any", "custom": "com.atlassian.jira.plugins.jira-development-integration-plugin:devsummarycf", "customId": 10000}}, {"id": "aggregateprogress", "key": "aggregateprogress", "name": "Σ Progress", "custom": false, "orderable": false, "navigable": true, "searchable": false, "clauseNames": [], "schema": {"type": "progress", "system": "aggregateprogress"}}, {"id": "customfield_10001", "key": "customfield_10001", "name": "Team", "untranslatedName": "Team", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10001]", "Team", "Team[Team]"], "schema": {"type": "team", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:atlassian-team", "customId": 10001, "configuration": {"com.atlassian.jira.plugin.system.customfieldtypes:atlassian-team": true}}}, {"id": "customfield_10002", "key": "customfield_10002", "name": "Organizations", "untranslatedName": "Organizations", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10002]", "Organizations"], "schema": {"type": "array", "items": "sd-customerorganization", "custom": "com.atlassian.servicedesk:sd-customer-organizations", "customId": 10002}}, {"id": "customfield_10003", "key": "customfield_10003", "name": "Approvers", "untranslatedName": "Approvers", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["Approvers", "Approvers[User Picker (multiple users)]", "cf[10003]"], "schema": {"type": "array", "items": "user", "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker", "customId": 10003}}, {"id": "comment", "key": "comment", "name": "Comment", "custom": false, "orderable": true, "navigable": false, "searchable": true, "clauseNames": ["comment"], "schema": {"type": "comments-page", "system": "comment"}}, {"id": "customfield_10031", "key": "customfield_10031", "name": "Sprint", "untranslatedName": "Sprint", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": ["cf[10020]", "Sprint"], "schema": {"type": "array", "items": "json", "custom": "com.pyxis.greenhopper.jira:gh-sprint", "customId": 10031}}, {"id": "customfield_19350", "key": "customfield_19350", "name": "security_field", "untranslatedName": "Flagged", "custom": true, "orderable": true, "navigable": true, "searchable": true, "clauseNames": [], "schema": {"type": "string", "system": "summary"}}]