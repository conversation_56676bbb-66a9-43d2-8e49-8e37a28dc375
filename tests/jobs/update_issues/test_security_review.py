from datetime import UTC, datetime, timedelta

from prime_redis_utils import AsyncPrefixRedisClient
from prime_security_review_service_client import PaginationResponseDesignDocResponse
from prime_tests import EMPTY_RESP, MockResponse, service_mocker

from service.db import CaseTable, ServiceDAL
from service.k8s_jobs.update_issues_job.update_design_review_links import (
    DATADOG_ACCOUNT_ID,
    DATADOG_CUSTOM_FIELD_ID,
    DATADOG_ISSUE_TYPE,
    UpdateDesignReviewLinks,
)
from service.logic.jira_manager import JiraFieldInfo, JiraFieldSchema, JiraPrimeIssue
from tests.case_test_utils import create_case, create_cases
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    DESIGN_REVIEW_PROJECT_KEY,
    FAKE_JIRA_FIELDS_SCHEMA,
    SOURCE_ID,
    get_issue_data,
    mock_jira_client,
)

TEST_URL = "https://docs.google.com/document/d/1a2b3c4d5e6f7g8h9i0j/edit?usp=sharing"


async def update_cases_created_at(cases: list[CaseTable]) -> None:
    for case_db in cases:
        provider_fields = case_db.provider_fields.copy()
        provider_fields["created"] = (datetime.now(UTC) - timedelta(days=1)).isoformat()
        case_db.provider_fields = provider_fields


async def _create_datadog_issue(
    issue_id: int, account_id: str, service_dal: ServiceDAL
) -> tuple[JiraPrimeIssue, CaseTable]:
    issue_data = get_issue_data(issue=issue_id)
    issue_data["fields"]["project"]["key"] = DESIGN_REVIEW_PROJECT_KEY
    issue_data["fields"]["issuetype"]["name"] = DATADOG_ISSUE_TYPE
    issue_data["fields"][DATADOG_CUSTOM_FIELD_ID] = TEST_URL
    prime_issue = JiraPrimeIssue.from_dict(issue_data)
    dd_case = await create_case(
        service_dal,
        prime_issue.key,
        account_id,
        SOURCE_ID,
        with_analysis=False,
        with_summary=False,
        provider_fields=prime_issue.get_attributes_fields(),
    )
    return prime_issue, dd_case


DATADOG_FIELD_INFO = JiraFieldInfo(
    id=DATADOG_CUSTOM_FIELD_ID,
    key=DATADOG_CUSTOM_FIELD_ID,
    name="security_field",
    schema=JiraFieldSchema(type="string"),
)


class TestSecurityReview:
    async def test_design_docs_check_success(
        self, service_dal_fixture: ServiceDAL, account_redis_client: AsyncPrefixRedisClient
    ):
        account_id = DATADOG_ACCOUNT_ID
        ACCOUNT_ID_CONTEXT.set(DATADOG_ACCOUNT_ID)
        design_response = PaginationResponseDesignDocResponse(results=[], size=0, limit=1, start=0)
        all_cases = await create_cases(service_dal_fixture, account_id=account_id, count=7)
        prime_issue1, dd_case1 = await _create_datadog_issue(8, account_id, service_dal_fixture)
        prime_issue2, dd_case2 = await _create_datadog_issue(
            9, account_id, service_dal_fixture
        )  # old creation date issue
        await update_cases_created_at(all_cases)
        await update_cases_created_at([dd_case1])
        all_cases.extend([dd_case1, dd_case2])
        await service_dal_fixture.session.commit()
        service_dal_fixture.session.expunge_all()
        provider_fields_override = [item.model_dump(by_alias=True) for item in FAKE_JIRA_FIELDS_SCHEMA]
        provider_fields_override.append(DATADOG_FIELD_INFO.model_dump(by_alias=True))
        with (
            mock_jira_client(provider_fields_override, account_id=account_id),
            service_mocker("file-manager-service") as file_manager_mocker,
            service_mocker("security-review-service") as security_review_mocker,
        ):
            download_origin_url = f"files/{account_id}/data/source/{SOURCE_ID}/origin_id/{dd_case1.issue_id}.json"
            file_manager_mocker.get(download_origin_url, MockResponse(prime_issue1.raw_data))
            security_review_mocker.get(f"/design-docs/{account_id}", MockResponse(design_response.model_dump()))
            security_review_mocker.post(f"design-docs/{account_id}/reference*", EMPTY_RESP)
            update_security_review = UpdateDesignReviewLinks(
                account_id, SOURCE_ID, service_dal_fixture, account_redis_client
            )
            issues_ids = [case.issue_id for case in all_cases]
            await update_security_review.update_design_review_links(issues_ids)
            assert len(security_review_mocker.requests["POST"][f"design-docs/{account_id}/reference*"]) == 1

    async def test_design_docs_url_already_exists(
        self, service_dal_fixture: ServiceDAL, account_redis_client: AsyncPrefixRedisClient
    ):
        pass
