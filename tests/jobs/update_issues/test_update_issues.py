import asyncio
import contextlib
import logging
import os
from datetime import UTC, datetime
from unittest.mock import ANY, AsyncMock, patch

import pytest
from prime_jobs import <PERSON><PERSON><PERSON>ob, JobStatus
from prime_redis_utils import get_redis
from prime_source_service_client import SourceModel, SourceType
from prime_tests import EnvSave, MockResponse

from client.prime_rat_logic_service_client.models.case_status import CaseStatus
from service.config import get_config
from service.db import ServiceDAL
from service.job_type import JobType
from service.k8s_jobs.job_spawners import UpdateIssuesJobSpawner
from service.k8s_jobs.update_issues_job.main import main
from service.k8s_jobs.update_issues_job.models import UpdateIssuesJobArgs
from service.k8s_jobs.update_issues_job.update_issues import UpdateIssuesJobLogic
from service.logic.issues_graph import GraphGenerator
from service.models import JobPsvCreateArgs, JobSummaryCreateArgs
from tests.case_test_utils import create_cases
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    SOURCE_ID,
    _basic_mocks,
    generate_test_tree,
    mock_get_descendants_links,
    mock_get_issues_files,
)
from tests.psv_utils import create_psv

LOGGER = logging.Logger("test")


@contextlib.contextmanager
def mock_generator():
    with patch.object(GraphGenerator, "update_since", return_value=generate_test_tree()) as graph_generator_mock:
        yield graph_generator_mock


@contextlib.contextmanager
def update_issues_mocks():
    with _basic_mocks() as (file_manager_mocker, source_mocker, _), mock_generator():
        yield file_manager_mocker, source_mocker


@contextlib.asynccontextmanager
async def _prepare_for_update_issues(service_dal: ServiceDAL):
    account_id = ACCOUNT_ID_CONTEXT.get()
    cases = await create_cases(service_dal, count=9)
    psv = await create_psv(service_dal, "ISSUE-10")
    psv_case = await service_dal.cases_dal.get_case(account_id, SOURCE_ID, psv.issue_id)
    org_provider_data = {c.id: c.provider_fields for c in cases + [psv_case]}
    with update_issues_mocks() as (file_manager_mocker, source_mocker):
        mock_get_issues_files(file_manager_mocker)
        source = SourceModel(id=int(SOURCE_ID), source_type=SourceType.JIRA, created_at=datetime.now(UTC))
        source_mocker.get(f"/sources/{account_id}/{SOURCE_ID}", MockResponse(source.model_dump()))
        new_job = await service_dal.scheduler_dal.jobs_dal.add_new_job(
            UpdateIssuesJobSpawner,
            CreateJob(account_id=account_id, created_by="test", job_type=JobType.UPDATE_ISSUES.value),
        )
        mock_get_descendants_links(file_manager_mocker, account_id)
        yield new_job, org_provider_data, cases + [psv_case]


async def _modify_cases_for_testing(service_dal: ServiceDAL):
    # container case
    deleted_case = await service_dal.get_case_container_view(ACCOUNT_ID_CONTEXT.get(), SOURCE_ID, "ISSUE-1")
    deleted_case.status = CaseStatus.DONE
    # no security case
    no_sec_case = await service_dal.get_case_container_view(ACCOUNT_ID_CONTEXT.get(), SOURCE_ID, "ISSUE-3")
    no_sec_case.issue_analysis.classification = False
    await service_dal.session.commit()
    return [deleted_case, no_sec_case]


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestUpdateIssuesJobLogic:
    async def test_main_logic(self, account_id: str):
        created_by = "someuser"
        with (
            patch.object(UpdateIssuesJobLogic, "build", new_callable=AsyncMock) as mock_build,
            EnvSave(),
            patch("service.k8s_jobs.update_issues_job.main.invoke_job", return_value=AsyncMock()) as mock_invoke,
            _basic_mocks(),
        ):
            job_instance_mock = AsyncMock(created_by=created_by)
            mock_build.return_value = job_instance_mock
            args = UpdateIssuesJobArgs(
                account_id=account_id,
                job_id=1,
                source_id=SOURCE_ID,
                last_update_at=None,
                force=False,
                update_fields_only=False,
            )
            os.environ.update({key: str(value) for key, value in args.model_dump(exclude_none=True).items()})
            await main()
            mock_build.assert_called_once_with(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=1,
                service_dal=ANY,
                last_update_at=None,
                force=False,
                update_fields_only=False,
            )
            job_instance_mock.start.assert_called_once()
            create_job_args = JobSummaryCreateArgs(job=JobType.SUMMARY, created_by=created_by, source_id=SOURCE_ID)
            mock_invoke.assert_any_call(account_id, create_job_args)
            create_job_args = JobPsvCreateArgs(
                job=JobType.SECURITY_VIOLATION, created_by=created_by, source_id=SOURCE_ID
            )
            mock_invoke.assert_any_call(account_id, create_job_args)

    async def test_run_update_issues_job_logic(self, service_dal_fixture: ServiceDAL, account_id: str):
        async with _prepare_for_update_issues(service_dal_fixture) as (new_job, org_provider_data, all_cases):
            with mock_generator() as graph_generator_mock:
                await _modify_cases_for_testing(service_dal_fixture)
                job_logic = await UpdateIssuesJobLogic.build(
                    account_id=account_id,
                    source_id=SOURCE_ID,
                    job_id=new_job.id,
                    service_dal=service_dal_fixture,
                    force=False,
                    update_fields_only=False,
                    last_update_at=None,
                )
                await job_logic.start()
                graph_generator_mock.assert_awaited_once()
        assert job_logic.status == JobStatus.COMPLETED
        [await service_dal_fixture.session.refresh(case) for case in all_cases]
        for case in all_cases:
            assert case.provider_fields != org_provider_data[case.id]
        graph_generator = GraphGenerator(service_dal_fixture, account_id, SOURCE_ID)
        await asyncio.sleep(0.05)
        assert await get_redis(get_config()).get(f"{account_id}:{graph_generator._get_redis_key()}")

    async def test_run_update_progress(self, service_dal_fixture: ServiceDAL, account_id: str):
        async with _prepare_for_update_issues(service_dal_fixture) as (new_job, org_provider_data, all_cases):
            issue_id_1 = await service_dal_fixture.cases_dal.get_case(ACCOUNT_ID_CONTEXT.get(), SOURCE_ID, "ISSUE-1")
            assert issue_id_1.progress_percentage == 0
            with mock_generator() as graph_generator_mock:
                await _modify_cases_for_testing(service_dal_fixture)
                job_logic = await UpdateIssuesJobLogic.build(
                    account_id=account_id,
                    source_id=SOURCE_ID,
                    job_id=new_job.id,
                    service_dal=service_dal_fixture,
                    force=False,
                    update_fields_only=False,
                    last_update_at=None,
                )
                await job_logic.start()
                graph_generator_mock.assert_awaited_once()
        assert job_logic.status == JobStatus.COMPLETED
        [await service_dal_fixture.session.refresh(case) for case in all_cases]
        assert issue_id_1.progress_percentage == 25

    async def test_run_update_issues_job_update_fields_false(self, service_dal_fixture: ServiceDAL, account_id: str):
        async with _prepare_for_update_issues(service_dal_fixture) as (new_job, org_provider_data, all_cases):
            modified_cases = await _modify_cases_for_testing(service_dal_fixture)
            job_logic = await UpdateIssuesJobLogic.build(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                last_update_at=None,
                force=False,
                update_fields_only=True,
            )
            await job_logic.start()
        assert job_logic.status == JobStatus.COMPLETED
        [await service_dal_fixture.session.refresh(case) for case in all_cases]
        for case in all_cases:
            if case in modified_cases:
                assert case.provider_fields == org_provider_data[case.id]
            else:
                assert case.provider_fields != org_provider_data[case.id]

    async def test_run_update_issues_job_logic_last_updated(self, service_dal_fixture: ServiceDAL, account_id: str):
        async with _prepare_for_update_issues(service_dal_fixture) as (new_job, org_provider_data, all_cases):
            last_update_at = datetime.now(UTC)
            job_logic = await UpdateIssuesJobLogic.build(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                last_update_at=last_update_at,
                force=False,
                update_fields_only=False,
            )
            await job_logic.start()
        [await service_dal_fixture.session.refresh(case) for case in all_cases]
        # make sure nothing has changed due to last_update_at
        for case in all_cases:
            assert case.provider_fields == org_provider_data[case.id]

    async def test_run_update_issues_job_logic_force(self, service_dal_fixture: ServiceDAL, account_id: str):
        async with _prepare_for_update_issues(service_dal_fixture) as (new_job, org_provider_data, all_cases):
            last_update_at = datetime.now(UTC)
            job_logic = await UpdateIssuesJobLogic.build(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                last_update_at=last_update_at,
                force=True,
                update_fields_only=False,
            )
            await job_logic.start()
        [await service_dal_fixture.session.refresh(case) for case in all_cases]
        # make sure it has changed despite last_update_at
        for case in all_cases:
            assert case.provider_fields != org_provider_data[case.id]

    async def test_run_update_issues_job_logic_no_new_cases(self, service_dal_fixture: ServiceDAL, account_id: str):
        async with _prepare_for_update_issues(service_dal_fixture) as (new_job, org_provider_data, all_cases):
            last_update_at = datetime.now(UTC)
            job_logic = await UpdateIssuesJobLogic.build(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
                last_update_at=last_update_at,
                force=False,
                update_fields_only=False,
            )
            await job_logic.start()
        assert job_logic.status == JobStatus.COMPLETED
