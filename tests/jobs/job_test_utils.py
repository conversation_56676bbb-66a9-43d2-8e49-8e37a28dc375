import contextlib
from collections.abc import Generator
from typing import Any
from unittest.mock import patch

from packaging.version import Version
from prime_jobs import C<PERSON><PERSON><PERSON>, Job, SchedulerDAL

from service.db import CaseTable, get_service_dal_context
from service.job_type import JobType
from service.k8s_jobs import RatJobSpawnerFactory
from tests.mock_utils import ACCOUNT_ID_CONTEXT

CELERY_MANAGER_RESULT_TARGET = (
    "service.k8s_jobs.base_job_logic.gen_ai_celery.celery_manager.CeleryManager.send_and_wait_for_result"
)


async def create_job(job_type: JobType, scheduler_dal_fixture: SchedulerDAL) -> Job:
    created_job = CreateJob(
        account_id=ACCOUNT_ID_CONTEXT.get(), job_type=job_type.value, job_args={}, created_by="test"
    )
    spawner_type = RatJobSpawnerFactory.JOB_SPAWNERS[job_type]
    new_job = await scheduler_dal_fixture.jobs_dal.add_new_job(spawner_type, created_job)
    return new_job


@contextlib.contextmanager
def generic_mock_celery_manager(responses: dict[str, dict[str, Any]]) -> Generator[None]:
    messages: list[Any] = []

    def _side_effect(*args, **kwargs: dict[str, Any]) -> dict[str, Any]:
        task_name = kwargs["task_name"]
        messages.append((args, kwargs))
        return responses.get(task_name, {})

    with patch(CELERY_MANAGER_RESULT_TARGET, side_effect=_side_effect):
        yield messages


@contextlib.contextmanager
def mock_version(gen_ai_version: str = "1.0.0") -> Generator[None]:
    gen_ai_version_target = "service.k8s_jobs.base_job_logic.gen_ai_base_job.GenAIBaseJob.get_ai_version"
    with patch(gen_ai_version_target) as base_mock_get_celery_app:
        base_mock_get_celery_app.return_value = Version(gen_ai_version)
        yield


async def modify_cases_hash(cases: list[CaseTable]):
    async with get_service_dal_context() as service_dal:
        for case in cases:
            issue_analysis = await service_dal.issues_analysis_dal.get(case.account_id, case.source_id, case.issue_id)
            issue_analysis.issue_hash = "asdasd"
            await service_dal.session.commit()
