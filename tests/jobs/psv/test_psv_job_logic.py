import json
from contextlib import contextmanager
from datetime import datetime, timed<PERSON>ta
from unittest.mock import patch

import pytest
from mock.mock import AsyncMock, MagicMock
from packaging.version import Version
from prime_db_utils import utcnow
from prime_gen_ai_service_client import PSITaskOutput
from prime_jobs import <PERSON><PERSON><PERSON><PERSON>, JobStatus
from prime_tests import service_mocker

from service.db import ServiceDAL
from service.errors import JobFailedError
from service.job_type import JobType
from service.k8s_jobs.base_job_logic.gen_ai_celery import GENAI_PIPELINE_PSV_TASK_NAME
from service.k8s_jobs.job_spawners import PsvJobSpawner
from service.k8s_jobs.security_violation_job.psv_job_logic import Psv<PERSON>ob<PERSON>ogic
from tests.case_test_utils import create_case
from tests.jobs.job_test_utils import generic_mock_celery_manager, mock_version
from tests.mock_utils import (
    ACCOUNT_ID_CONTEXT,
    SOURCE_ID,
    _set_files_mock,
    get_issue_data,
    get_issue_id,
    mock_source_service,
)
from tests.psv_utils import create_psv, get_psv_output


@contextmanager
def mock_celery_manager(psv_output: PSITaskOutput | None = None):
    psv_output = psv_output or get_psv_output(is_psv=True)
    with generic_mock_celery_manager({GENAI_PIPELINE_PSV_TASK_NAME: psv_output.model_dump()}):
        yield


@contextmanager
def mock_get_psv_issues_files(psv_str: str | None, timestamp: datetime | None = None):
    with service_mocker("file-manager-service") as file_manager_mocker:
        issue_json = get_issue_data(1)
        issue_key = issue_json["key"]
        if psv_str:
            issue_json["fields"]["description"] = psv_str
        issues_data = [(issue_key, json.dumps(issue_json).encode("utf-8"))]
        _set_files_mock(issues_data, file_manager_mocker, ACCOUNT_ID_CONTEXT.get(), timestamp or utcnow())
        yield issue_key, file_manager_mocker


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestPsvJobLogic:
    @pytest.mark.parametrize(
        "violation_type,violation_text",
        [
            ("aws_key", "AKIAIOSFODNN7EXAMPLE"),
            ("github_token", "****************************************"),
            (
                "private_key",
                """-----BEGIN RSA PRIVATE KEY-----
            MIIEpAIBAAKCAQEA7bq3FARgD+XuGK6DmgBxH3m7aOPpF5RrL0VU6rWje6TGhyPh
            MIIEpAIBAAKCAQEA7bq3FARgD+XuGK6DmgBxH3m7aOPpF5RrL0VU6rWje6TGhyPh
            KmB5PJ4RQmtLKSH9MQDi15PwDgF9CJJHyqHJ7F7zNeM4NjCpJnvZK6A8KQhT+SlF
            -----END RSA PRIVATE KEY-----""",
            ),
            (
                "aws_key",
                """ghu_1234567890abcdefABCDEF1234567890ABCD blablabla AKIAIOSFODNN7EXAMPLE blablabla -----BEGIN RSA PRIVATE KEY-----
            MIIEpAIBAAKCAQEA7bq3FARgD+XuGK6DmgBxH3m7aOPpF5RrL0VU6rWje6TGhyPh
            MIIEpAIBAAKCAQEA7bq3FARgD+XuGK6DmgBxH3m7aOPpF5RrL0VU6rWje6TGhyPh
            KmB5PJ4RQmtLKSH9MQDi15PwDgF9CJJHyqHJ7F7zNeM4NjCpJnvZK6A8KQhT+SlF
            -----END RSA PRIVATE KEY-----""",
            ),
        ],
    )
    async def test_classification_job_psv_from_regex(
        self, service_dal_fixture: ServiceDAL, violation_type: str, violation_text: str, account_id: str
    ):
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(violation_text) as (issue_key, _),
            patch(
                "service.k8s_jobs.security_violation_job.psv_job_logic.PsvJobLogic._get_gen_ai_psv", return_value=None
            ) as mock_get_gen_ai_psv,
        ):
            await create_case(service_dal_fixture, issue_key, with_analysis=False)
            assert len(await service_dal_fixture.psv_dal.get_psvs_by(account_id)) == 0
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            with patch.object(job_logic, "report", wraps=job_logic.report) as spy_report:
                await job_logic.start()
            assert spy_report.await_args.args[0] == True  # noqa: E712
            assert job_logic.status == JobStatus.COMPLETED
            assert not mock_get_gen_ai_psv.called
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert psv[0].type == violation_type
            assert psv[0].description in violation_text
            assert psv[0].has_psv
            assert psv[0].issue_hash is None

    async def test_psv_job_gen_ai(self, service_dal_fixture: ServiceDAL, account_id: str):
        gen_ai_output = get_psv_output(is_psv=True)
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None) as (issue_key, _),
            mock_celery_manager(),
        ):
            await create_case(service_dal_fixture, issue_key, with_analysis=False)
            assert len(await service_dal_fixture.psv_dal.get_psvs_by(account_id)) == 0
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert psv[0].type == gen_ai_output.results.results[0].incident_type
            assert psv[0].description == gen_ai_output.results.results[0].incident_value
            assert psv[0].has_psv
            assert psv[0].issue_hash

    @pytest.mark.parametrize("kwargs", [{"is_error": True}, {"is_null_results": True}])
    async def test_psv_job_gen_ai_error(self, service_dal_fixture: ServiceDAL, account_id: str, kwargs):
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None) as (issue_key, _),
            mock_celery_manager(get_psv_output(**kwargs)),
        ):
            _ = await create_case(service_dal_fixture, issue_key, with_analysis=False)
            assert len(await service_dal_fixture.psv_dal.get_psvs_by(account_id)) == 0
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 0

    async def test_psv_job_no_psv(self, service_dal_fixture: ServiceDAL, account_id: str):
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None) as (issue_key, _),
            mock_celery_manager(get_psv_output(False)),
        ):
            _ = await create_case(service_dal_fixture, issue_key, with_analysis=False)
            assert len(await service_dal_fixture.psv_dal.get_psvs_by(account_id)) == 0
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert not psv[0].type
            assert not psv[0].description
            assert not psv[0].has_psv
            assert psv[0].issue_hash

    async def test_psv_job_source_missing(self, service_dal_fixture: ServiceDAL, account_id: str):
        new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
            PsvJobSpawner,
            CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
        )
        with mock_version(), pytest.raises(JobFailedError):
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()

    async def test_psv_job_get_jira_issues_failed(self, service_dal_fixture: ServiceDAL, account_id: str):
        with (
            mock_version(),
            mock_source_service(account_id),
            mock_get_psv_issues_files(None) as (issue_key, _),
            patch(
                "service.k8s_jobs.security_violation_job.psv_job_logic.PsvJobLogic.get_jira_issues",
                side_effect=Exception("Test error"),
            ) as mock_get_jira_issues,
        ):
            _ = await create_case(service_dal_fixture, issue_key, with_analysis=False)
            assert len(await service_dal_fixture.psv_dal.get_psvs_by(account_id)) == 0
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED
            # assert no psv was created
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 0
            assert mock_get_jira_issues.called

    async def test_psv_job_start_fail(self, account_id: str):
        service_dal_mock = MagicMock()
        service_dal_mock.scheduler_dal.jobs_dal.update_job = AsyncMock()
        job_logic = PsvJobLogic(
            account_id=account_id,
            job_id=1,
            source_id=SOURCE_ID,
            service_dal=service_dal_mock,
            force=False,
            last_psv_at=None,
            ai_version=Version("0.0.0"),
        )
        with (
            patch.object(job_logic, "report", wraps=job_logic.report) as spy_report,
            patch.object(job_logic, "_init_job"),
            patch.object(job_logic, "_get_issues_to_process", side_effect=ValueError("Test error")),
        ):
            job_logic._init = True
            with pytest.raises(JobFailedError):
                await job_logic.start()
            assert spy_report.await_args.args[0] == False  # noqa: E712
        assert job_logic.status == JobStatus.FAILED


class TestPsvAlreadyExist:
    async def test_psv_job_replace_existing_psv_with_new(self, service_dal_fixture: ServiceDAL, account_id: str):
        gen_ai_output = get_psv_output(is_psv=True)
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None, utcnow() + timedelta(minutes=10)) as (issue_key, _),
            mock_celery_manager(),
        ):
            old_psv = await create_psv(service_dal_fixture, get_issue_id(1), description="test description")
            psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psvs) == 1
            assert psvs[0].id == old_psv.id
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=new_job.id,
                service_dal=service_dal_fixture,
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED
            # assert job completed and new psv was created
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert psv[0].id == old_psv.id + 1
            assert psv[0].type == gen_ai_output.results.results[0].incident_type
            assert psv[0].description == gen_ai_output.results.results[0].incident_value
            assert psv[0].has_psv
            assert psv[0].issue_hash

    async def test_psv_job_replace_existing_psv_with_no_psv(self, service_dal_fixture: ServiceDAL, account_id: str):
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None, utcnow() + timedelta(minutes=10)) as (issue_key, _),
            mock_celery_manager(get_psv_output(False)),
        ):
            old_psv = await create_psv(service_dal_fixture, get_issue_id(1), description="test description")
            psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psvs) == 1
            assert psvs[0].id == old_psv.id

            # creating new psv row with has_psv is false
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert psv[0].id == old_psv.id + 1
            assert not psv[0].type
            assert not psv[0].description
            assert not psv[0].has_psv
            assert psv[0].issue_hash

    async def test_psv_job_psv_exist_gen_ai_error(self, service_dal_fixture: ServiceDAL, account_id: str):
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None) as (issue_key, _),
            mock_celery_manager(),
        ):
            old_psv = await create_psv(service_dal_fixture, get_issue_id(1), description="test description")
            psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psvs) == 1
            assert psvs[0].id == old_psv.id

            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED

            # assert job completed but new psv was not created
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert psv[0].id == old_psv.id

    async def test_psv_job_should_not_replace(self, service_dal_fixture: ServiceDAL, account_id: str):
        PsvJobLogic.get_ai_version.cache_clear()
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None) as (issue_key, _),
            patch(
                "service.k8s_jobs.security_violation_job.psv_job_logic.PsvJobLogic.process_issues_parallel"
            ) as mock_process_issues_parallel,
        ):
            old_psv = await create_psv(service_dal_fixture, get_issue_id(1), description="test description")
            psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psvs) == 1
            assert psvs[0].id == old_psv.id
            # creating new psv
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED

            # assert job completed but new psv was not created
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert psv[0].id == old_psv.id
            assert not mock_process_issues_parallel.called

    async def test_psv_job_timestamp_changed_hash_remain_the_same(
        self, service_dal_fixture: ServiceDAL, account_id: str
    ):
        PsvJobLogic.get_ai_version.cache_clear()
        old_psv = await create_psv(service_dal_fixture, get_issue_id(1), description="test description")
        with (
            mock_version(),
            mock_source_service(),
            mock_get_psv_issues_files(None, utcnow() + timedelta(minutes=10)) as (issue_key, _),
            patch(
                "service.k8s_jobs.security_violation_job.psv_job_logic.PsvJobLogic.get_issue_hash",
                return_value=old_psv.issue_hash,
            ) as mock_get_issue_hash,
        ):
            psvs = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psvs) == 1
            assert psvs[0].id == old_psv.id
            # creating new psv
            new_job = await service_dal_fixture.scheduler_dal.jobs_dal.add_new_job(
                PsvJobSpawner,
                CreateJob(account_id=account_id, created_by="test", job_type=JobType.SECURITY_VIOLATION.value),
            )
            job_logic = PsvJobLogic(
                account_id=account_id, source_id=SOURCE_ID, job_id=new_job.id, service_dal=service_dal_fixture
            )
            await job_logic.start()
            assert job_logic.status == JobStatus.COMPLETED

            # assert job completed but new psv was not created
            psv = await service_dal_fixture.psv_dal.get_psvs_by(account_id)
            assert len(psv) == 1
            assert psv[0].id == old_psv.id
            assert mock_get_issue_hash.called
