import functools
from unittest.mock import Mock, patch

import pytest
from celery.result import AsyncResult

from service.errors.errors import AIGenerationTimeoutError
from service.k8s_jobs.base_job_logic.gen_ai_celery import GET_RESULT_TIMEOUT_SEC, CeleryManager

ISSUE_ID = "ISSUE-4"


def with_short_timeout(func):
    @functools.wraps(func)
    @patch("service.k8s_jobs.base_job_logic.gen_ai_celery.celery_manager.TASK_TIMEOUT_SEC", 0.05)
    @patch("service.k8s_jobs.base_job_logic.gen_ai_celery.celery_manager.TASK_SLEEP_PERIOD_SEC", 0.01)
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)

    return wrapper


class TestGenAICelery:
    async def test_wait_for_summary_result_success(self):
        mock_result = Mock(spec=AsyncResult)
        expected_data = {"summary": "Test summary"}

        # Mock the ready() method to return True immediately
        mock_result.ready.return_value = True
        mock_result.get.return_value = expected_data
        celery_manager = CeleryManager(celery_app_name="test")
        result = await celery_manager._wait_for_result(ISSUE_ID, mock_result)

        assert result == expected_data
        mock_result.ready.assert_called_once()
        mock_result.get.assert_called_once_with(timeout=GET_RESULT_TIMEOUT_SEC)

    @with_short_timeout
    async def test_wait_for_summary_result_timeout(self):
        mock_result = Mock(spec=AsyncResult)

        mock_result.ready.return_value = False
        celery_manager = CeleryManager(celery_app_name="test")

        with pytest.raises(AIGenerationTimeoutError) as exc_info:
            await celery_manager._wait_for_result(ISSUE_ID, mock_result)

        assert ISSUE_ID in str(exc_info.value)
        assert mock_result.ready.call_count > 1  # Should be called multiple times

    @with_short_timeout
    async def test_wait_for_summary_result_eventually_succeeds(self):
        mock_result = Mock(spec=AsyncResult)
        expected_data = {"summary": "Test summary"}

        ready_values = [False, False, True]
        mock_result.ready.side_effect = ready_values
        mock_result.get.return_value = expected_data

        celery_manager = CeleryManager(celery_app_name="test")
        result = await celery_manager._wait_for_result(ISSUE_ID, mock_result)

        assert result == expected_data
        assert mock_result.ready.call_count == 3
        mock_result.get.assert_called_once_with(timeout=GET_RESULT_TIMEOUT_SEC)

    async def test_wait_for_summary_result_get_timeout(self):
        mock_result = Mock(spec=AsyncResult)

        mock_result.ready.return_value = True
        mock_result.get.side_effect = TimeoutError()

        celery_manager = CeleryManager(celery_app_name="test")
        with pytest.raises(TimeoutError):
            await celery_manager._wait_for_result(ISSUE_ID, mock_result)
