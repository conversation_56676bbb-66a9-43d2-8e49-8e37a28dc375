from datetime import UTC, datetime

import pytest
from prime_gen_ai_service_client import ContextPipelineOutput, QuestionsInput

from service.db import ServiceDAL
from service.k8s_jobs.base_job_logic import SummaryOutput
from service.k8s_jobs.summary_job import FullSummaryGeneration, SummariesTree, SummaryInfo
from service.k8s_jobs.summary_job.flows import BaseSummaryFlow
from service.logic.issues_graph import IssuesGraph
from tests.case_test_utils import get_summary_output
from tests.mock_utils import JIRA_STRUCTURE

from .summary_mocks import summary_generation

test = summary_generation  # dont remove. for ruff


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestSummaryGenerationComponents:
    async def test_execute_parallel_tasks(self, summary_generation: FullSummaryGeneration):
        actual_processed_nodes = []

        expected_needs_processing = {"ISSUE-8", "ISSUE-9", "ISSUE-2"}

        class TestFlow(BaseSummaryFlow):
            async def build(self) -> None:
                if await self.needs_processing():
                    actual_processed_nodes.append(self.issue_id)

            async def _save_results(self, service_dal: ServiceDAL, results: SummaryOutput) -> None:
                pass

            async def _clear(self) -> None:
                pass

            async def _generate_summary(self) -> ContextPipelineOutput:
                pass

            def _get_summary_info(self) -> SummaryOutput | None:
                pass

            def get_relevant_issues(self) -> list[str]:
                return []

            async def needs_processing(self) -> bool:
                return self.issue_id in expected_needs_processing

        now = datetime.min.replace(tzinfo=UTC)
        graph = IssuesGraph.build_from_tuples(JIRA_STRUCTURE)
        summaries = {issue_id: SummaryInfo(issue_id, timestamp=now) for issue_id in graph.iterator()}
        summaries_tree = SummariesTree(issues_graph=graph, summaries=summaries)
        await summary_generation._execute(graph.iterator_from_top(), TestFlow, summaries_tree)
        assert set(actual_processed_nodes) == expected_needs_processing

    def test_ai_summary_input_output_model(self):
        # note: we want to make sure that the output of the model_dump is a valid input for the input model
        summary_question = get_summary_output().context_summary.questions
        question_input = QuestionsInput.model_validate(summary_question.model_dump(), strict=True)
        assert question_input.model_dump_json().replace("Input", "Output") == summary_question.model_dump_json()

    # TODO: implement this test
    def test_get_relevant_issues(self):
        pass
