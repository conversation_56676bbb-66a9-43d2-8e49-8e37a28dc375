import os
from unittest.mock import ANY, AsyncMock, patch

import pytest
from prime_security_review_service_client import JobDesignDocsCreateArgs
from prime_tests import EnvSave
from pydantic import ValidationError

from service.k8s_jobs.summary_job import FullSummaryGeneration, SummaryJobArgs
from service.k8s_jobs.summary_job.main import main
from service.models import JobClassificationCreateArgs
from tests.mock_utils import SOURCE_ID


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestFinalSummaryGenerationMain:
    async def test_main_logic(self, account_id: str):
        created_by = "some_user"
        with (
            patch("service.k8s_jobs.summary_job.main.FullSummaryGeneration", autospec=True) as mock_summary,
            EnvSave(),
            patch("service.k8s_jobs.summary_job.main.invoke_job", return_value=AsyncMock()) as mock_invoke,
            patch(
                "service.k8s_jobs.summary_job.main.invoke_security_review_job", return_value=AsyncMock()
            ) as mock_invoke_security,
        ):
            mock_instance = mock_summary.return_value
            mock_instance.start = AsyncMock()
            mock_instance.created_by = created_by
            args = SummaryJobArgs(account_id=account_id, source_id=SOURCE_ID, job_id=1, force=True)
            os.environ.update({key: str(value) for key, value in args.model_dump(exclude_none=True).items()})
            await main()
            mock_summary.assert_called_once_with(
                account_id=account_id,
                source_id=SOURCE_ID,
                job_id=1,
                service_dal=ANY,
                force=True,
                last_run=ANY,
                parent_id=None,
            )
            mock_instance.start.assert_called_once_with()
            create_job_args = (
                account_id,
                JobClassificationCreateArgs(created_by=created_by, force=False, source_id=SOURCE_ID, parent_id=None),
            )
            assert create_job_args == mock_invoke.call_args_list[0].args
            create_job_args = (
                account_id,
                JobDesignDocsCreateArgs(created_by=created_by, force=False, design_doc_ids=None),
            )
            assert create_job_args == mock_invoke_security.call_args_list[0].args

    async def test_main_logic_no_args(self):
        with (
            patch.object(FullSummaryGeneration, "start", new_callable=AsyncMock),
            EnvSave(),
            pytest.raises(ValidationError),
        ):
            await main()
