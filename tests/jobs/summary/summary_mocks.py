import contextlib
from collections.abc import Generator
from contextlib import contextmanager
from datetime import UTC, datetime, timedelta
from unittest.mock import AsyncMock

import pytest
from prime_gen_ai_service_client import ContextPipelineOutput
from prime_tests import service_mocker

from service.db.service_dal import ServiceDAL
from service.k8s_jobs.base_job_logic.gen_ai_celery import GENAI_PARTIALLY_SUMMARY_TASK_NAME
from service.k8s_jobs.summary_job import FullSummaryGeneration
from service.logic.jira_manager import JiraIssuesManager
from tests.case_test_utils import get_summary_output
from tests.jobs.job_test_utils import generic_mock_celery_manager, mock_version
from tests.mock_utils import ACCOUNT_ID_CONTEXT, SOURCE_ID, mock_get_descendants_empty, mock_get_issues_files

ISSUE_ID = "ISSUE-4"
CHILD_ISSUE_ID = "ISSUE-2"
PARENT_ISSUE_ID = "ISSUE-3"
CHILD_SUMMARY_TEXT = "THIS IS A TEST CHILD SUMMARY"
PARENT_SUMMARY_TEXT = "THIS IS A TEST PARENT SUMMARY"

FILE_TIMESTAMP = datetime.now(UTC) - timedelta(minutes=10)


@contextmanager
def summary_generation_context(
    service_dal_fixture: ServiceDAL,
    timestamp: datetime | dict[str, datetime] | None = None,
    summaries: ContextPipelineOutput | None = None,
    version: str = "1.0.0",
) -> Generator[FullSummaryGeneration]:
    with (
        service_mocker("file-manager-service") as file_manager_mocker,
        mock_celery_manager(summaries) as messages,
        mock_version(version),
    ):
        mock_get_descendants_empty(file_manager_mocker, ACCOUNT_ID_CONTEXT.get())
        mock_get_issues_files(file_manager_mocker, timestamp=timestamp or FILE_TIMESTAMP)
        mock_get_descendants_empty(file_manager_mocker, ACCOUNT_ID_CONTEXT.get())
        summary_gen = FullSummaryGeneration(
            account_id=ACCOUNT_ID_CONTEXT.get(), source_id=SOURCE_ID, job_id=1, service_dal=service_dal_fixture
        )
        summary_gen._get_jira_fields = AsyncMock(return_value=[])
        summary_gen._test_messages = messages
        yield summary_gen


@pytest.fixture
def summary_generation(
    issue_manager: JiraIssuesManager, service_dal_fixture: ServiceDAL
) -> Generator[FullSummaryGeneration]:
    with summary_generation_context(service_dal_fixture) as summary_gen:
        yield summary_gen


@contextlib.contextmanager
def mock_celery_manager(summaries: ContextPipelineOutput | None = None):
    summaries = summaries or get_summary_output()
    with generic_mock_celery_manager({GENAI_PARTIALLY_SUMMARY_TASK_NAME: summaries.model_dump()}) as messages:
        yield messages
