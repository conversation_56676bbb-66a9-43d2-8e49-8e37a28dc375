from datetime import UTC, datetime, timed<PERSON>ta
from unittest import mock

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from service.db import CaseTable, ServiceDAL
from service.k8s_jobs.base_job_logic import GenAIBaseJob
from service.k8s_jobs.summary_job.flows import PartialSummaryFlow
from service.k8s_jobs.summary_job.summary_generation import FullSummaryGeneration
from tests.case_test_utils import create_cases, get_summary_output
from tests.mock_utils import JIRA_CONTAINERS_ISSUES, SOURCE_ID, generate_test_tree

from .summary_mocks import mock_celery_manager, summary_generation, summary_generation_context

test = summary_generation  # dont remove. for ruff
ISSUES_WITH_SUMMARY = JIRA_CONTAINERS_ISSUES


@pytest.fixture()
def clear_gen_ai_version():
    GenAIBaseJob.get_ai_version.cache_clear()


async def _run_summary(service_dal_fixture: ServiceDAL) -> list[CaseTable]:
    cases = await create_cases(service_dal_fixture, with_summary=False)
    with summary_generation_context(service_dal_fixture) as summary_gen:
        await summary_gen._generate_summary()
    service_dal_fixture.session.expunge_all()
    return cases


def _check_issues_in_gen_ai_call(summary_gen):
    summary_gen._test_messages[0][1]["input_args"]


@pytest.mark.usefixtures("clear_gen_ai_version")
@pytest.mark.usefixtures("issue_manager")
@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestSummaryGenerationFlow:
    async def test_success_flow(
        self, summary_generation: FullSummaryGeneration, service_dal_fixture: ServiceDAL, account_id: str
    ):
        cases = await create_cases(service_dal_fixture, with_summary=False)
        await summary_generation._generate_summary()
        service_dal_fixture.session.expunge_all()

        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            assert case.partial is not None
            if issue_id in ISSUES_WITH_SUMMARY:
                assert case.partial.short is not None
            else:
                assert case.partial.short is None
            assert case.final is None

    async def test_second_flow(self, service_dal_fixture: ServiceDAL, account_id: str, mocker: MockerFixture):
        cases = await _run_summary(service_dal_fixture)

        summary_output = get_summary_output()
        summary_output.context_summary.short_summary = "new short summary"
        needs_processing_spy = mocker.spy(PartialSummaryFlow, "needs_processing")
        summary_creation_spy = mocker.spy(PartialSummaryFlow, "_needs_summary_creation")
        with summary_generation_context(service_dal_fixture, summaries=summary_output) as new_summary_gen:
            new_summary_gen._last_run = datetime.now(UTC) - timedelta(seconds=20)
            await new_summary_gen._generate_summary()
        assert all(value is False for value in needs_processing_spy.spy_return_list)
        assert len(summary_creation_spy.spy_return_list) == 0
        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            if case.issue_id in ISSUES_WITH_SUMMARY:
                assert case.partial.ai_version == "1.0.0"
                assert case.partial.short != "new short summary"
            else:
                assert case.partial.short is None
            assert case.final is None

    async def test_container_changed(self, service_dal_fixture: ServiceDAL, account_id: str, mocker: MockerFixture):
        last_run_time = datetime.now(UTC)
        cases = await _run_summary(service_dal_fixture)

        modified_container_id = "ISSUE-1"
        summary_output = get_summary_output()
        summary_output.context_summary.short_summary = "new short summary"

        summary = await service_dal_fixture.issue_summary_dal.get_summary(account_id, SOURCE_ID, modified_container_id)
        summary.issue_hash = "changed"
        await service_dal_fixture.session.commit()

        timestamp = {f"{case.issue_id}.json": last_run_time for case in cases}
        timestamp[f"{modified_container_id}.json"] = datetime.now(UTC) + timedelta(days=1)

        needs_processing_spy = mocker.spy(PartialSummaryFlow, "needs_processing")
        summary_creation_spy = mocker.spy(PartialSummaryFlow, "_needs_summary_creation")

        await service_dal_fixture.session.reset()
        with summary_generation_context(
            service_dal_fixture, summaries=summary_output, timestamp=timestamp
        ) as summary_gen:
            summary_gen._last_run = last_run_time
            await summary_gen._generate_summary()
        for pair in zip(needs_processing_spy.call_args_list, needs_processing_spy.spy_return_list, strict=False):
            issue_id = pair[0].args[0].issue_id
            assert pair[1] is (True if issue_id == modified_container_id else False)  # noqa: SIM210
        for pair in zip(summary_creation_spy.call_args_list, summary_creation_spy.spy_return_list, strict=False):
            issue_id = pair[0].args[0].issue_id
            assert pair[1] is (True if issue_id == modified_container_id else False)  # noqa: SIM210
        service_dal_fixture.session.expunge_all()
        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            if case.issue_id == modified_container_id:
                assert case.partial.ai_version == "1.0.0"
                assert case.partial.short == "new short summary"
            elif case.issue_id in set(ISSUES_WITH_SUMMARY) - {modified_container_id}:
                assert case.partial.ai_version == "1.0.0"
                assert case.partial.short != "new short summary"
            else:
                assert case.partial.short is None

    async def test_children_change(self, service_dal_fixture: ServiceDAL, account_id: str, mocker: MockerFixture):
        last_run_time = datetime.now(UTC)
        cases = await _run_summary(service_dal_fixture)

        modified_issue_id = "ISSUE-3"
        modified_parent_ids = ["ISSUE-2", "ISSUE-1"]
        summary_output = get_summary_output()
        summary_output.context_summary.short_summary = "new short summary"

        summary = await service_dal_fixture.issue_summary_dal.get_summary(account_id, SOURCE_ID, modified_issue_id)
        summary.issue_hash = "changed"
        await service_dal_fixture.session.commit()

        timestamp = {f"{case.issue_id}.json": last_run_time for case in cases}
        timestamp[f"{modified_issue_id}.json"] = datetime.now(UTC) + timedelta(days=1)

        needs_processing_spy = mocker.spy(PartialSummaryFlow, "needs_processing")
        summary_creation_spy = mocker.spy(PartialSummaryFlow, "_needs_summary_creation")

        await service_dal_fixture.session.reset()
        with summary_generation_context(
            service_dal_fixture, summaries=summary_output, timestamp=timestamp
        ) as new_summary_gen:
            new_summary_gen._last_run = last_run_time
            await new_summary_gen._generate_summary()
        to_process_ids = modified_parent_ids + [modified_issue_id]
        for pair in zip(needs_processing_spy.call_args_list, needs_processing_spy.spy_return_list, strict=False):
            issue_id = pair[0].args[0].issue_id
            assert pair[1] is (True if issue_id in to_process_ids else False)  # noqa: SIM210
        assert len(summary_creation_spy.spy_return_list) == len(to_process_ids)
        for pair in zip(summary_creation_spy.call_args_list, summary_creation_spy.spy_return_list, strict=False):
            issue_id = pair[0].args[0].issue_id
            assert pair[1] is (True if issue_id in modified_parent_ids else False)  # noqa: SIM210
        service_dal_fixture.session.expunge_all()
        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            if case.issue_id in modified_parent_ids:
                assert case.partial.ai_version == "1.0.0"
                assert case.partial.short == "new short summary"
            elif case.issue_id in set(ISSUES_WITH_SUMMARY) - set(modified_parent_ids):
                assert case.partial.ai_version == "1.0.0"
                assert case.partial.short != "new short summary"
            else:
                assert case.partial.short is None

    async def test_leaf_without_links(self):
        pass

    async def test_failure_flow(
        self, summary_generation: FullSummaryGeneration, service_dal_fixture: ServiceDAL, account_id: str
    ):
        async def patched_call_flow(flow):
            return await flow.build()

        with mock.patch.object(summary_generation, "call_flow_with_retries", patched_call_flow):
            cases = await create_cases(service_dal_fixture, with_summary=False)
            summary_output = get_summary_output()
            summary_output.error = "test error"
            with mock_celery_manager(summary_output):
                await summary_generation._generate_summary()
            err_msg = f"Failed to generate summary for account: '{account_id}', source: '{SOURCE_ID}', issue: '{{0}}'. Error: test error"
            assert summary_generation._errors == {
                issue_id: err_msg.format(issue_id) for issue_id in ISSUES_WITH_SUMMARY
            }
            for issue_id in [case.issue_id for case in cases]:
                case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
                assert case.partial is None

    async def test_version_update_flow(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await _run_summary(service_dal_fixture)
        summary_output = get_summary_output()
        summary_output.context_summary.short_summary = "new short summary"
        GenAIBaseJob.get_ai_version.cache_clear()
        ver = "2.0.0"
        with summary_generation_context(service_dal_fixture, summaries=summary_output, version=ver) as summary_gen:
            await summary_gen._generate_summary()
        service_dal_fixture.session.expunge_all()
        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            if case.issue_id in ISSUES_WITH_SUMMARY:
                assert case.partial.ai_version == ver
                assert case.partial.short == "new short summary"
            else:
                assert case.partial.short is None

    async def test_force_flow(self, service_dal_fixture: ServiceDAL, account_id: str):
        cases = await _run_summary(service_dal_fixture)

        summary_output = get_summary_output()
        summary_output.context_summary.short_summary = "new short summary"
        with summary_generation_context(service_dal_fixture, summaries=summary_output) as summary_gen:
            summary_gen._force = True
            await summary_gen._generate_summary()
        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            if case.issue_id in ISSUES_WITH_SUMMARY:
                assert case.partial.ai_version == "1.0.0"
                assert case.partial.short == "new short summary"
            else:
                assert case.partial.short is None

    async def test_timestamp_update_flow(self, service_dal_fixture: ServiceDAL, account_id: str, mocker: MockerFixture):
        cases = await _run_summary(service_dal_fixture)

        original_short_summary = get_summary_output().context_summary.short_summary
        new_short_summary = "new short summary"

        summary_output = get_summary_output()
        summary_output.context_summary.short_summary = new_short_summary

        # timestamp without hash change should not update the case
        needs_processing_spy = mocker.spy(PartialSummaryFlow, "needs_processing")
        summary_creation_spy = mocker.spy(PartialSummaryFlow, "_needs_summary_creation")
        now = datetime.now(UTC)
        with summary_generation_context(service_dal_fixture, timestamp=now, summaries=summary_output) as summary_gen:
            await summary_gen._generate_summary()
        for pair in zip(needs_processing_spy.call_args_list, needs_processing_spy.spy_return_list, strict=False):
            assert pair[1] is True
        for pair in zip(summary_creation_spy.call_args_list, summary_creation_spy.spy_return_list, strict=False):
            assert pair[1] is False
        service_dal_fixture.session.expunge_all()
        for issue_id in ISSUES_WITH_SUMMARY:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            assert case.partial.ai_version == "1.0.0"
            assert case.partial.short == original_short_summary

        # timestamp with hash change should update the case
        for issue_id in ISSUES_WITH_SUMMARY:
            summary = await service_dal_fixture.issue_summary_dal.get_summary(account_id, SOURCE_ID, issue_id)
            summary.issue_hash = "changed"
        await service_dal_fixture.session.commit()
        now = datetime.now(UTC)
        with summary_generation_context(service_dal_fixture, timestamp=now, summaries=summary_output) as summary_gen:
            await summary_gen._generate_summary()
        service_dal_fixture.session.expunge_all()

        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            if case.issue_id in ISSUES_WITH_SUMMARY:
                assert case.partial.ai_version == "1.0.0"
                assert case.partial.short == new_short_summary
            else:
                assert case.partial.short is None

    async def test_parent_id_flow(
        self, summary_generation: FullSummaryGeneration, service_dal_fixture: ServiceDAL, account_id: str
    ):
        parent_issue_id = "ISSUE-8"
        subtree_issues = list(generate_test_tree().create_subgraph(parent_issue_id).iterator())
        cases = await create_cases(service_dal_fixture, with_summary=False)
        summary_generation._parent_id = parent_issue_id
        await summary_generation._generate_summary()
        service_dal_fixture.session.expunge_all()
        expected_summary_issues = set(ISSUES_WITH_SUMMARY).intersection(set(subtree_issues))
        for issue_id in [case.issue_id for case in cases]:
            case = await service_dal_fixture.cases_dal.get_case(account_id, SOURCE_ID, issue_id, with_summary=True)
            if issue_id in expected_summary_issues:
                assert case.partial.short is not None
            elif issue_id in subtree_issues:
                assert case.partial.short is None
            else:
                assert case.partial is None
