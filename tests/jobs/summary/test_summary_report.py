from datetime import UTC, datetime
from unittest.mock import AsyncMock, Mock, patch

import pytest

from service.k8s_jobs.summary_job import FullSummaryGeneration
from service.k8s_jobs.summary_job.models import FullSummaryJobReport
from service.k8s_jobs.summary_job.summary_reporter import SummaryMetricsReporter
from tests.mock_utils import SOURCE_ID


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestSummaryReport:
    async def test_report_job_metrics(self, account_id: str):
        reporter = SummaryMetricsReporter(account_id=account_id, source_id=SOURCE_ID, job_id=1)
        summary_report = FullSummaryJobReport(
            issues_total=10,
            issues_need_processing_partial_summary=5,
            issues_finished_partial_summary=3,
            new_cases_created=0,
            errors=0,
            duration_in_seconds=10,
            execution_status=True,
        )
        reporter.report_job_metrics(summary_report)
        assert True

    async def test_termination_signal_handler(self, account_id: str):
        summary_gen = FullSummaryGeneration(
            account_id=account_id, source_id=SOURCE_ID, job_id=1, service_dal=AsyncMock()
        )

        mock_report = AsyncMock()
        summary_gen.report = mock_report
        await summary_gen.termination_signal_handler()

        assert mock_report.call_count == 1
        mock_report.assert_awaited_with(False)

    async def test_report(self, account_id: str):
        service_dal_mock = AsyncMock()

        summary_gen = FullSummaryGeneration(
            account_id=account_id, source_id=SOURCE_ID, job_id=1, service_dal=service_dal_mock
        )

        summary_gen._job_statistics_results.total_issues_in_account = 10
        summary_gen._job_statistics_results.total_issues_required_partial_summary = 5
        summary_gen._job_statistics_results.total_new_cases_created = 2
        summary_gen._job_statistics_results.total_issues_with_errors = 1
        summary_gen._start_time = datetime.now(UTC)

        mock_get_job_id = Mock()
        mock_get_job_id.return_value = 1
        summary_gen.get_job_id = mock_get_job_id

        # Mock SummaryMetricsReporter
        with patch.object(SummaryMetricsReporter, "report_job_metrics") as mock_report:
            # Call the report method
            await summary_gen.report(execution_status=True)
            mock_report.assert_called_once()
            actual_report = mock_report.call_args[0][0]
            assert isinstance(actual_report, FullSummaryJobReport)
            assert actual_report.issues_total == 10
            assert actual_report.issues_need_processing_partial_summary == 5
            assert actual_report.new_cases_created == 2
            assert actual_report.errors == 1
            assert actual_report.execution_status is True
            assert isinstance(actual_report.duration_in_seconds, int)
