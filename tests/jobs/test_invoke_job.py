from aioresponses import aioresponses

from service.config import get_config
from service.job_type import JobType
from service.k8s_jobs.base_job_logic import invoke_job
from service.models import JobCreatedResponse, JobSummaryCreateArgs
from service.service_app import app

from ..mock_utils import SOURCE_ID


async def test_invoke_job(account_id: str):
    url = [a for a in app.routes if a.name == "add_job"][0].path.format(account_id=account_id)
    with aioresponses() as rat_mock:
        rat_mock.post(
            f"{get_config().rat_logic_service_url}{url}",
            status=200,
            payload=JobCreatedResponse(job_id=1, status="PENDING").model_dump(),
        )
        create_job_args = JobSummaryCreateArgs(job=JobType.SUMMARY, source_id=SOURCE_ID, created_by="test", force=True)
        r = await invoke_job(account_id, create_job_args)
    assert r.status == 200
    result = await r.json()
    assert result["job_id"] == 1
    assert result["status"] == "PENDING"
