import pytest
from pydantic import ValidationError

from service.models import ExternalImplementation, ImplementationStatus


@pytest.mark.filterwarnings("ignore::pytest.PytestUnraisableExceptionWarning")
class TestExternalRecommendations:
    def test_recommendations_validation(self) -> None:
        ExternalImplementation(
            id=1,
            concern_id=1,
            control_id="1",
            recommendation="recommendation1",
            status=ImplementationStatus.APPROVED,
            controls={"NIST": {"some_control"}},
        )

        ExternalImplementation.model_validate(
            {
                "id": 1,
                "concern_id": 1,
                "control_id": "1",
                "recommendation": "recommendation1",
                "status": ImplementationStatus.APPROVED,
                "controls": {"NIST": {"some_control"}},
            }
        )

    def test_recommendations_validation_errors(self) -> None:
        with pytest.raises(ValidationError):
            ExternalImplementation(
                control_id="control_id_not_found",
                recommendation="recommendation1",
                status=ImplementationStatus.APPROVED,
            )

        with pytest.raises(ValidationError):
            ExternalImplementation(
                control_id="control_id1",
                recommendation="recommendation1",
                status=ImplementationStatus.APPROVED,
            )
