#!/usr/bin/env python3
"""
Simple test script to verify the new security review endpoint
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_endpoint_definition():
    """Test that the endpoint is properly defined"""
    try:
        # Set required environment variables for testing
        os.environ.setdefault('SERVICE_NAME', 'test-service')
        os.environ.setdefault('SERVICE_ENVIRONMENT', 'test')
        os.environ.setdefault('JSON_LOG_ENABLED', 'false')
        os.environ.setdefault('DB_CONNECTION_STRING', 'postgresql://test:test@localhost:5432/test')
        os.environ.setdefault('REDIS_CONNECTION_STRING', 'redis://localhost:6379')
        os.environ.setdefault('RABBITMQ_CONNECTION_STRING', 'amqp://localhost:5672')
        # Additional required environment variables
        os.environ.setdefault('REDIS_HOSTNAME', 'localhost')
        os.environ.setdefault('RABBITMQ_HOSTNAME', 'localhost')
        os.environ.setdefault('RABBITMQ_PASSWORD', 'test')
        os.environ.setdefault('DB_HOSTNAME', 'localhost')
        os.environ.setdefault('DB_PASSWORD', 'test')
        os.environ.setdefault('DB_USERNAME', 'test')
        os.environ.setdefault('DB_NAME', 'test')
        os.environ.setdefault('JOBS_IMAGE_URL', 'test-image')
        
        from service.routers.cases import cases_api
        
        # Get all routes from the API router
        routes = [route for route in cases_api.routes]
        
        # Find our new endpoint
        security_review_routes = [
            route for route in routes 
            if hasattr(route, 'path') and '/{account_id}/case-id/{case_id}' in route.path
        ]
        
        if security_review_routes:
            route = security_review_routes[0]
            print("✅ Security review endpoint found!")
            print(f"   Path: {route.path}")
            print(f"   Methods: {route.methods}")
            print(f"   Name: {route.name}")
            return True
        else:
            print("❌ Security review endpoint not found")
            print("Available routes:")
            for route in routes:
                if hasattr(route, 'path'):
                    print(f"   {route.path} - {route.methods}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

if __name__ == "__main__":
    success = test_endpoint_definition()
    sys.exit(0 if success else 1)
