# service info
service_name=rat-logic-service
service_environment=dev
service_port=8084

# logging
json_log_enabled=False

# database
#db_name=rat-logic-service-dev-cicd
db_name=rat-logic-service-dev
db_password=jn4rVmxekFgLEXVC
#db_password="BS8oi5AbXNLLQq*d3"
db_hostname=localhost
#db_hostname=eu-dev-01-aurora-postgres.cluster-cxkullzcpkcw.eu-central-1.rds.amazonaws.com
db_username=service_user
db_port=5432
db_enforce_ssl_mode=False

# services mapping
file_manager_service_url=http://127.0.0.1:8083
source_service_url=http://127.0.0.1:8081
config_service_url=http://127.0.0.1:8085
notification_service_url=http://127.0.0.1:8086
fetcher_service_url=http://127.0.0.1:8082
chatbot_service_url=http://127.0.0.1:8089
policy_service_url=http://127.0.0.1:8090
security_review_service_url=http://127.0.0.1:8091


# rabbitmq
rabbitmq_hostname=localhost
rabbitmq_port=5772
rabbitmq_use_ssl=false
rabbitmq_username=guest
rabbitmq_password=guest

# redis
redis_hostname=localhost
redis_ssl=False

# redis celery
celery_redis__redis_hostname=localhost
celery_redis__redis_ssl=False

# prime-jobs:
jobs_image_url=''
use_spawner=true
use_scheduler=false
use_watchdog=true

# disable datadog
datadog_enabled=false
