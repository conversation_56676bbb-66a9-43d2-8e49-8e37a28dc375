import logging
from collections.abc import As<PERSON><PERSON>terator
from typing import Annotated

from fastapi import APIRouter, BackgroundTasks, HTTPException, Query, Request, status
from fastapi.responses import StreamingResponse
from prime_security_review_service_client import DesignDocResponse
from prime_service_kit.fastapi_utils import PaginationResponse, pagination_args_type
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath
from prime_shared.common_dataclasses import PaginationArgs
from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import EmailStr

from service.config import get_config
from service.db import PROVIDERS_FIELDS_COLUMN_NAME, get_service_dal_context, service_dal_depends
from service.dependencies import filtering_type, parse_filter_string, parse_sort_string, redis_type, sorting_type
from service.errors import CaseRecommendationNotFound
from service.logic import (
    JiraWriteBackHandler,
    get_custom_recommendations,
    get_external_case_by_issue_id,
    get_workroom_cases,
    get_workroom_cases_paginated,
    handle_recommendations_status_change,
    select_fields_from_cases,
    stream_export_to_csv,
)
from service.logic.external_cases import build_external_case
from service.logic.filters_and_sort import CaseFilters
from service.logic.issues.utils import get_provider_field_info, get_provider_fields_info_stored
from service.logic.jira_manager import JiraIssuesManager
from service.logic.recommendation_on_demand import generate_recommendation_on_demand
from service.models import (
    BulkUpdateCasesRequest,
    CaseStatus,
    CreateCaseComment,
    ExternalCase,
    ExternalCaseWorkroom,
    ImplementationStatusUpdate,
    NoWhitespaceStr,
    ProviderFieldType,
    RiskScoreCategory,
    risk_category_to_score,
)
from service.models.cases import GenerateRecommendationsForConcernIdsRequest, SearchResponse
from service.models.filters_and_sort import Filter, SortField
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("cases")
cases_api = APIRouter(prefix="/cases", tags=["cases"])
CommentRefId = Annotated[str, "comment_ref_id"]

selected_columns_t = Annotated[list[str], Query(default_factory=list, title="Selected Columns")]


async def export_content_stream(
    account_id: AccountIdType,
    case_filters: CaseFilters,
    sort: list[SortField],
    selected_columns: selected_columns_t,
    pagination_args: PaginationArgs,
) -> AsyncIterator[str]:
    async with get_service_dal_context() as service_dal:
        total = await service_dal.cases_dal.get_cases_count_by(account_id, case_filters=case_filters)
        external_cases = await get_workroom_cases(account_id, service_dal, pagination_args, case_filters, sort)
        output_data = select_fields_from_cases(selected_columns, external_cases)
        pagination_args.offset = len(output_data)
        yield stream_export_to_csv(output_data, fields=selected_columns, write_header=True)
        while len(external_cases) > 0 and pagination_args.offset < total:
            LOGGER.info("Getting next page of cases from %s", pagination_args.offset)
            external_cases = await get_workroom_cases(account_id, service_dal, pagination_args, case_filters, sort)
            output_data = select_fields_from_cases(selected_columns, external_cases)
            yield stream_export_to_csv(output_data, fields=selected_columns, write_header=False)
            pagination_args.offset += len(output_data)
        return


@cases_api.get("/{account_id}", status_code=status.HTTP_200_OK, description="Get all open security cases for account")
async def get_cases_for_account(  # noqa: PLR0913
    request: Request,
    account_id: AccountArgumentType,
    service_dal: service_dal_depends,
    pagination_args: pagination_args_type,
    filter_args: filtering_type,
    sort: sorting_type,
    redis_client: redis_type,
    query_cases_view_id: str | None = None,
) -> PaginationResponse[ExternalCaseWorkroom]:
    LOGGER.info("Getting cases for account %s", account_id)
    if query_cases_view_id:
        query_view_api = ServicesClients.query_view_api()
        query_view = await query_view_api.get_query_view_by_id(account_id, query_id=query_cases_view_id)
        view_filters = await parse_filter_string(
            request=request,
            account_id=account_id,
            filter_str_query=query_view.query_list or [],
            redis_client=redis_client,
        )
        filter_args.extend(view_filters)
        view_sorts = await parse_sort_string(
            request=request,
            account_id=account_id,
            sort_str_query=query_view.sort_list or [],
            redis_client=redis_client,
        )
        sort.extend(view_sorts)

    return await get_workroom_cases_paginated(account_id, service_dal, pagination_args, filter_args, sort)


@cases_api.get("/{account_id}/export", status_code=status.HTTP_200_OK, description="Export to CSV file")
async def export_for_account(
    account_id: AccountArgumentType,
    filter_args: filtering_type,
    sort: sorting_type,
    selected_columns: selected_columns_t,
) -> StreamingResponse:
    LOGGER.info("Getting export cases for account %s", account_id)
    pagination_args = PaginationArgs(limit=get_config().export_limit, offset=0)
    case_filters = CaseFilters(filter_args)
    # we don't use service_dal_depends as its context is closed before the stream is consumed
    stream = export_content_stream(account_id, case_filters, sort, selected_columns, pagination_args)
    return StreamingResponse(stream, media_type="text/csv")


@cases_api.get(
    "/{account_id}/source/{source_id}",
    status_code=status.HTTP_200_OK,
    description="Get all open security cases for source",
)
async def get_cases_for_account_and_source(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    service_dal: service_dal_depends,
    pagination_args: pagination_args_type,
    filter_args: filtering_type,
    sort: sorting_type,
) -> PaginationResponse[ExternalCaseWorkroom]:
    LOGGER.info("Getting cases for account %s and source %s", account_id, source_id)
    filter_args.append(Filter(field="source_id", value=str(source_id)))
    return await get_workroom_cases_paginated(account_id, service_dal, pagination_args, filter_args, sort)


@cases_api.get(
    "/{account_id}/source/{source_id}/issue/{issue_id}",
)
async def get_case(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    service_dal: service_dal_depends,
    redis_client: redis_type,
) -> ExternalCase:
    LOGGER.info("Getting case for account %s, source %s and issue %s", account_id, source_id, issue_id)
    provider_fields_info = await get_provider_fields_info_stored(account_id, source_id, redis_client)
    external_case = await get_external_case_by_issue_id(
        account_id=account_id,
        source_id=source_id,
        issue_id=issue_id,
        service_dal=service_dal,
        provider_fields_info=provider_fields_info,
    )
    return external_case


@cases_api.get("/{account_id}/case/{case_id}")
async def get_case_by_id(
    account_id: AccountArgumentType,
    case_id: int,
    service_dal: service_dal_depends,
    redis_client: redis_type,
) -> ExternalCase:
    LOGGER.info("Getting case for account %s, case %s", account_id, case_id)
    case = await service_dal.cases_dal.get_case_by_id(account_id, case_id, with_recommendations=True, with_summary=True)
    provider_fields_info = await get_provider_fields_info_stored(account_id, case.source_id, redis_client)
    return await build_external_case(case, service_dal, provider_fields_info)


@cases_api.put(
    "/{account_id}/source/{source_id}/issue/{issue_id}/comment",
    status_code=status.HTTP_202_ACCEPTED,
    description="Add comment to case",
)
async def add_comment(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    comment: CreateCaseComment,
    service_dal: service_dal_depends,
) -> None:
    await service_dal.cases_dal.add_comment(
        account_id=account_id, source_id=source_id, issue_id=issue_id, comment=comment
    )


@cases_api.put(
    "/{account_id}/source/{source_id}/issue/{issue_id}/recommendations",
    status_code=status.HTTP_204_NO_CONTENT,
    description="Update recommendations for case",
)
async def update_recommendations(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    recommendations_update: list[ImplementationStatusUpdate],
    service_dal: service_dal_depends,
) -> None:
    LOGGER.info(
        "Updating recommendations for case account_id=%s source_id=%s issue_id=%s to %s",
        account_id,
        source_id,
        issue_id,
        {rec.id: rec.status for rec in recommendations_update},
    )
    case_recommendations = await service_dal.cases_dal.get_case_recommendations(account_id, source_id, issue_id)
    custom_recommendations = await get_custom_recommendations(account_id)
    try:
        for rec_to_update in recommendations_update:
            handle_recommendations_status_change(case_recommendations, rec_to_update, custom_recommendations)
    except StopIteration:
        raise CaseRecommendationNotFound(
            account_id, source_id, issue_id, recommendation_ids=[rec.id for rec in recommendations_update]
        ) from None
    await service_dal.cases_dal.update_case_recommendations(
        account_id=account_id, source_id=source_id, issue_id=issue_id, recommendations=case_recommendations
    )


@cases_api.put(
    "/{account_id}/source/{source_id}/issue/{issue_id}/status",
    status_code=status.HTTP_204_NO_CONTENT,
    description="Update status for case",
)
async def update_status(
    account_id: AccountArgumentType,
    source_id: SourceIdType,
    issue_id: str,
    status: CaseStatus,
    service_dal: service_dal_depends,
    dismissed_reason: str | None = None,
) -> None:
    await service_dal.cases_dal.update_case_status(
        account_id=account_id, source_id=source_id, issue_id=issue_id, status=status, dismissed_reason=dismissed_reason
    )


@cases_api.put(
    "/{account_id}/source/{source_id}/issue/{issue_id}/risk-score-category",
    status_code=status.HTTP_204_NO_CONTENT,
    description="Update risk score category for case",
)
async def update_risk_score_category(
    account_id: AccountArgumentType,
    source_id: SourceIdType,
    issue_id: str,
    risk_score_category: RiskScoreCategory,
    service_dal: service_dal_depends,
) -> None:
    score_range = risk_category_to_score(risk_score_category)
    await service_dal.cases_dal.override_risk_score(
        account_id=account_id, source_id=source_id, issue_id=issue_id, new_risk_score=score_range.start
    )


@cases_api.post(
    "/{account_id}/source/{source_id}/issue/{issue_id}/write-back",
    status_code=status.HTTP_201_CREATED,
    description="Write back the recommendations to source",
)
async def write_back(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    service_dal: service_dal_depends,
) -> CommentRefId | None:
    write_back_handler = JiraWriteBackHandler(account_id, source_id, issue_id, service_dal)
    return await write_back_handler.write_back()


@cases_api.delete(
    "/{account_id}/source/{source_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_cases_for_source(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    service_dal: service_dal_depends,
) -> None:
    LOGGER.info("Deleting cases for source %s", source_id)
    await service_dal.cases_dal.delete_cases_by_source(account_id, source_id)


@cases_api.post(
    "/{account_id}/source/{source_id}/issue/{issue_id}/watcher",
    status_code=status.HTTP_201_CREATED,
    description="Add watcher to case",
)
async def add_watcher(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    watcher_email: EmailStr,
) -> None:
    LOGGER.info(
        "Adding watcher [%s] to issue [%s] with account id [%s] and source id [%s]",
        watcher_email,
        issue_id,
        account_id,
        source_id,
    )
    await JiraIssuesManager.add_watcher(account_id, source_id, issue_id, watcher_email)


@cases_api.get(
    "/{account_id}/search/{field}/{value}",
    description="Get possible values for a field",
)
async def autocomplete(
    account_id: AccountArgumentType, field: str, value: str, service_dal: service_dal_depends, redis_client: redis_type
) -> list[str]:
    LOGGER.info("Getting autocomplete values for field %s and value %s", field, value)
    field_id = field.replace(f"{PROVIDERS_FIELDS_COLUMN_NAME}.", "")
    provider_fields_info = await get_provider_field_info(account_id, None, field_id, redis_client)
    result = []
    if provider_fields_info.type in [ProviderFieldType.ARRAY]:
        result = await service_dal.autocomplete_dal.get_field_autocomplete_for_array(account_id, field_id, value)
    elif provider_fields_info.type in [ProviderFieldType.ENUM]:
        result = await service_dal.autocomplete_dal.get_field_autocomplete_for_string(account_id, field_id, value)
    return result


@cases_api.get(
    "/{account_id}/search/{value}",
)
async def autocomplete_search_global_cases(
    account_id: AccountArgumentType, value: str, limit: int, service_dal: service_dal_depends
) -> list[SearchResponse]:
    LOGGER.info("Getting search for title or issue id for value [%s]", value)
    search_data = await service_dal.autocomplete_dal.get_field_autocomplete_for_title_or_issue_id(
        account_id, value, limit
    )

    search_response = []
    for case_id, issue_id, source_id, provider_fields, is_container in search_data:
        title = provider_fields.get("summary", "")

        search_response.append(
            SearchResponse(
                id=case_id,
                issue_id=issue_id,
                title=title,
                issue_type=provider_fields["issuetype"],
                is_container=is_container,
                source_id=source_id,
            )
        )

    return search_response


@cases_api.put(
    "/{account_id}/source/{source_id}/issue/{issue_id}/label",
    status_code=status.HTTP_201_CREATED,
    description="Add label to case",
)
async def set_labels(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    service_dal: service_dal_depends,
    labels: list[NoWhitespaceStr],
) -> None:
    await service_dal.set_labels(account_id=account_id, source_id=source_id, issue_id=issue_id, labels=labels)


@cases_api.get("/{account_id}/labels", status_code=status.HTTP_202_ACCEPTED)
async def get_labels(account_id: AccountArgumentType, service_dal: service_dal_depends) -> list[NoWhitespaceStr]:
    res_labels = await service_dal.labels_dal.get_labels(account_id)
    return [label.name for label in res_labels]


@cases_api.post("/{account_id}/bulk-update/{source_id}", status_code=status.HTTP_202_ACCEPTED)
async def bulk_update_cases(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    bulk_update_cases_request: BulkUpdateCasesRequest,
    service_dal: service_dal_depends,
) -> None:
    LOGGER.info("Bulk updating %s", bulk_update_cases_request)
    await service_dal.dal_bulk_ops.bulk_update_cases(account_id, source_id, bulk_update_cases_request)


@cases_api.post(
    "/{account_id}/{source_id}/{issue_id}/recommendations/generate",
    status_code=status.HTTP_202_ACCEPTED,
    description="Generate recommendations for list of concern ids - recommendations on demand",
)
async def generate_recommendations_for_concern_ids(  # noqa: PLR0913
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    created_by: str,
    request: GenerateRecommendationsForConcernIdsRequest,
    service_dal: service_dal_depends,
    redis_client: redis_type,
    background_tasks: BackgroundTasks,
) -> bool:
    LOGGER.info(
        "Generating recommendation on demand for account: [%s], source id: [%s], issue id: [%s],"
        " concern ids: [%s], user id: [%s]",
        account_id,
        source_id,
        issue_id,
        request.concern_ids,
        created_by,
    )
    return await generate_recommendation_on_demand(
        service_dal, account_id, source_id, issue_id, request.concern_ids, created_by, background_tasks, redis_client
    )


@cases_api.get(
    "/{account_id}/{source_id}/{issue_id}/case_id",
    status_code=status.HTTP_202_ACCEPTED,
    description="Get case id for issue id and source id",
)
async def get_case_id(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: str,
    service_dal: service_dal_depends,
) -> int:
    LOGGER.info("Getting case id for account: [%s], source id: [%s], issue id: [%s]", account_id, source_id, issue_id)
    case = await service_dal.get_case_view(account_id=account_id, source_id=source_id, issue_id=issue_id)

    return case.id  # type: ignore[return-value]


@cases_api.get(
    "/{account_id}/case-id/{case_id}",
    status_code=status.HTTP_200_OK,
    description="Get security review by container case ID",
    name="get_security_review_by_container",
)
async def get_security_review_by_container(
    account_id: AccountArgumentType,
    case_id: int,
) -> DesignDocResponse:
    """
    Get security review (design document) for a container by case ID.
    Returns 404 if no security review exists for the container.
    """
    LOGGER.info("Getting security review for account: [%s], case_id: [%s]", account_id, case_id)

    try:
        # Get all design documents for the account
        design_docs_api = ServicesClients.design_docs()
        design_docs_response = await design_docs_api.get_design_docs(account_id)

        # Find the design document with matching case_id
        for design_doc in design_docs_response.results:
            if hasattr(design_doc, 'case_id') and design_doc.case_id == case_id:
                return design_doc

        # If no design document found for the case_id, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No security review found for case_id {case_id}"
        )

    except Exception as e:
        LOGGER.error("Error retrieving security review for case_id %s: %s", case_id, str(e))
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving security review"
        )
