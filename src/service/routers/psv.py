import logging
from collections.abc import AsyncIterator
from typing import Any

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from prime_service_kit.fastapi_utils.pagination import PaginationArgs, PaginationResponse, pagination_args_type
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath
from prime_shared.common_types import AccountIdType
from starlette import status as httpstatus

from service.config import get_config
from service.db import get_service_dal_context, service_dal_depends
from service.dependencies import filtering_type, sorting_type
from service.logic import external_psv_from_tables, get_external_psvs, stream_export_to_csv
from service.logic.filters_and_sort import PSVFilters
from service.models import (
    BulkUpdatePsvRequest,
    PotentialSecurityViolation,
    SinglePsvUpdateRequest,
)

psv_api = APIRouter(prefix="/psv", tags=["psv"])
LOGGER = logging.getLogger("psv")
EXCLUDED_PSV_FIELDS = ["psv_id", "description"]
SELECTED_PSV_FIELDS = [k for k in PotentialSecurityViolation.model_fields if k not in EXCLUDED_PSV_FIELDS]


def _data_to_dict(data: list[PotentialSecurityViolation]) -> list[dict[str, Any]]:
    return [d.model_dump(exclude=dict.fromkeys(EXCLUDED_PSV_FIELDS, True)) for d in data]


async def export_content_stream(
    account_id: AccountIdType,
    psv_filters: PSVFilters,
    pagination_args: PaginationArgs,
) -> AsyncIterator[str]:
    async with get_service_dal_context() as service_dal:
        psv_count = await service_dal.psv_dal.get_psv_count(account_id, psv_filters=psv_filters)
        ret_psvs = await get_external_psvs(account_id, service_dal, psv_filters, pagination_args)
        pagination_args.offset = len(ret_psvs)
        yield stream_export_to_csv(_data_to_dict(ret_psvs), fields=SELECTED_PSV_FIELDS, write_header=True)
        while len(ret_psvs) > 0 and pagination_args.offset < psv_count:
            LOGGER.info("Getting next page of psv from %s", pagination_args.offset)
            ret_psvs = await get_external_psvs(account_id, service_dal, psv_filters, pagination_args)
            yield stream_export_to_csv(_data_to_dict(ret_psvs), fields=SELECTED_PSV_FIELDS, write_header=False)
            pagination_args.offset += len(ret_psvs)
        return


@psv_api.get(
    "/{account_id}",
    status_code=httpstatus.HTTP_200_OK,
)
async def get_psv(
    account_id: AccountArgumentType,
    pagination_args: pagination_args_type,
    service_dal: service_dal_depends,
    filter_args: filtering_type,
    sort: sorting_type,
) -> PaginationResponse[PotentialSecurityViolation]:
    psv_filters = PSVFilters.base_filter(filter_args)
    psv_count = await service_dal.psv_dal.get_psv_count(account_id, psv_filters=psv_filters)
    ret_psvs = await get_external_psvs(account_id, service_dal, psv_filters, pagination_args, sort)
    results = PaginationResponse[PotentialSecurityViolation].from_results(
        results=ret_psvs, pagination_args=pagination_args, total=psv_count
    )
    return results


@psv_api.get("/{account_id}/export", status_code=httpstatus.HTTP_200_OK, description="Export to CSV file")
async def export_psv_for_account(
    account_id: AccountArgumentType,
    filter_args: filtering_type,
) -> StreamingResponse:
    LOGGER.info("Export psv for account %s", account_id)
    pagination_args = PaginationArgs(limit=get_config().export_limit, offset=0)
    stream = export_content_stream(account_id, PSVFilters.base_filter(filter_args), pagination_args)
    return StreamingResponse(stream, media_type="text/csv")


@psv_api.put(
    "/{account_id}/{psv_id}",
    status_code=httpstatus.HTTP_202_ACCEPTED,
    description="Update the status of PSV",
    name="update_psv_status",
)
async def update_psv_status(
    account_id: AccountArgumentType,
    service_dal: service_dal_depends,
    psv_id: int,
    psv_to_update: SinglePsvUpdateRequest,
) -> PotentialSecurityViolation:
    psv_table = await service_dal.psv_dal.update_psv_status(
        account_id, psv_id=psv_id, status=psv_to_update.new_status, dismissed_reason=psv_to_update.dismissed_reason
    )
    return (await external_psv_from_tables(service_dal, account_id, [psv_table]))[0]


@psv_api.post(
    "/{account_id}/bulk-update",
    status_code=httpstatus.HTTP_202_ACCEPTED,
    description="Bulk update the status of PSV's",
    name="bulk_update_psv_status",
)
async def bulk_update_psv_status(
    account_id: AccountArgumentType,
    service_dal: service_dal_depends,
    bulk_update_request: BulkUpdatePsvRequest,
) -> list[PotentialSecurityViolation]:
    LOGGER.info("Bulk updating psv's %s", bulk_update_request.violations)
    psv_tables = await service_dal.psv_dal.update_psv_status_bulk(
        account_id=account_id, psvs_to_update=bulk_update_request.violations
    )
    return await external_psv_from_tables(service_dal, account_id, psv_tables)


@psv_api.delete(
    "/{account_id}/source/{source_id}",
    status_code=httpstatus.HTTP_204_NO_CONTENT,
    description="Delete all PSV's for a source",
    name="delete_source_psv",
)
async def delete_source_psv(
    account_id: AccountArgumentType,
    service_dal: service_dal_depends,
    source_id: SourceIdPath,
) -> None:
    await service_dal.psv_dal.delete_psv_for_source(account_id, source_id)
