import logging
from datetime import datetime

from fastapi import APIRouter
from prime_gen_ai_service_client import Issue as GenAIIssue
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath
from starlette import status

from service.db import service_dal_depends
from service.k8s_jobs.base_job_logic import Gen<PERSON><PERSON><PERSON><PERSON><PERSON>
from service.logic.filters_and_sort import CaseFilters
from service.logic.jira_manager import Jira<PERSON><PERSON>uesManager
from service.models.filters_and_sort import Filter, Operator

issues_api = APIRouter(prefix="/issues", tags=["issues"])
LOGGER = logging.getLogger("router")


@issues_api.get(
    "/{account_id}/{source_id}",
    status_code=status.HTTP_200_OK,
    description="Get all issues keys that scanned",
    name="get-issues-keys",
)
async def get_issues_keys(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    service_dal: service_dal_depends,
    since: datetime | None = None,
) -> list[str]:
    LOGGER.info("Getting all issues keys for account %s, source %s since %s", account_id, source_id, since)
    case_filters = CaseFilters()
    if since:
        case_filters.add_filter(Filter(field="since_date", value=str(since), op=Operator.GT))
    cases_key = await service_dal.cases_dal.get_issue_ids_by(
        account_id=account_id, source_id=source_id, case_filters=case_filters
    )
    return list(cases_key)


@issues_api.get(
    "/{account_id}/children/{case_id}",
    status_code=status.HTTP_200_OK,
)
async def get_container_children_data(
    account_id: AccountArgumentType,
    case_id: int,
    service_dal: service_dal_depends,
) -> list[GenAIIssue]:
    LOGGER.info("Getting issues for account %s, case %s", account_id, case_id)
    container_case = await service_dal.cases_dal.get_case_by_id(account_id, case_id)
    jira_manager = JiraIssuesManager(account_id, container_case.source_id)
    filters = CaseFilters().parent_id(container_case.issue_id)
    all_cases = await service_dal.cases_dal.get_cases_by(
        account_id=account_id, source_id=container_case.source_id, case_filters=filters
    )
    issues_ids = [case.issue_id for case in all_cases] + [container_case.issue_id]
    issues = await jira_manager.get_issues(issues_ids)
    result = [GenAIBaseJob.to_ai_issue(issue) for issue in issues.values()]
    return result
