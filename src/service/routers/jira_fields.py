import json
import logging
from typing import cast

from fastapi import APIRouter
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType
from starlette import status

from service.config import get_config
from service.db import PROVIDERS_FIELDS_COLUMN_NAME, service_dal_depends
from service.dependencies import redis_type
from service.logic.issues.utils import get_provider_fields_info_selected, get_provider_fields_info_stored
from service.logic.jira_manager import JiraFieldsManager
from service.models import ProviderFieldInfo, ProviderFieldInfoOptions, ProviderFieldsOptionsList, ProviderFieldType
from service.services_clients import ServicesClients

jira_fields = APIRouter(prefix="/jira-fields", tags=["jira-fields"])
LOGGER = logging.getLogger("jira_fields")
HIDDEN_FIELDS = ["self", "id", "key"]


async def get_fields_data(account_id: AccountIdType, source_id: SourceIdType) -> dict[str, list[str]]:
    try:
        files_data = await ServicesClients.files_api().download_origin_file_for_source(
            account_id, source_id, origin_id="fields_data.json"
        )
        return cast(dict[str, list[str]], json.loads(files_data))
    except Exception:
        LOGGER.exception("Error downloading fields data")
        return {}


@jira_fields.get(
    "/{account_id}/all/{source_id}",
    status_code=status.HTTP_200_OK,
    response_model=ProviderFieldsOptionsList,
)
async def get_all_provider_fields(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    redis_client: redis_type,
) -> ProviderFieldsOptionsList:
    source_data = await ServicesClients.source_api().get_source(account_id, source_id)
    if source_data.source_type != SourceType.JIRA:
        return ProviderFieldsOptionsList(fields=[], total=0)

    files_data = await get_fields_data(account_id, source_id)
    jira_fields = await JiraFieldsManager.get_fields(account_id, source_id, redis_client)
    return_fields = []
    for jira_field in jira_fields:
        options: list[str] | None = files_data.get(jira_field.id, None)
        options = [str(value) for value in options] if options else None
        provider_field_info = jira_field.generate_provider_field_info()
        workroom_field_info = ProviderFieldInfoOptions(options=options, **provider_field_info.model_dump())
        return_fields.append(workroom_field_info)
    return ProviderFieldsOptionsList(fields=return_fields, total=len(return_fields))


@jira_fields.get(
    "/{account_id}/selected/{source_id}",
    status_code=status.HTTP_200_OK,
    response_model=list[ProviderFieldInfo],
)
async def get_selected_provider_fields(
    account_id: AccountArgumentType,
    redis_client: redis_type,
    source_id: SourceIdPath,
) -> list[ProviderFieldInfo]:
    LOGGER.info("Getting selected provider fields")
    provider_fields_info = await get_provider_fields_info_selected(account_id, source_id, redis_client)
    return list(provider_fields_info.values())


@jira_fields.get(
    "/{account_id}/workroom",
    status_code=status.HTTP_200_OK,
    response_model=list[ProviderFieldInfoOptions],
)
async def get_workroom_fields(
    account_id: AccountArgumentType,
    redis_client: redis_type,
    service_dal: service_dal_depends,
    is_container_view: bool | None = None,
) -> list[ProviderFieldInfoOptions]:
    fields_info = await get_provider_fields_info_stored(account_id, None, redis_client)
    fields_info = {field.id: field for field in fields_info.values() if field.id.lower() not in HIDDEN_FIELDS}
    LOGGER.info("Getting workroom fields: %s", list(fields_info.keys()))

    fields_to_match = [ProviderFieldType.ENUM, ProviderFieldType.ARRAY]
    # TODO: Exclude sprints there is a problem with the query for inner items, fix this when u want filter by sprint
    fields_to_scan = [
        field_id for field_id, field in fields_info.items() if field.type in fields_to_match and field_id != "sprint"
    ]
    limit = get_config().max_field_options_to_return
    fields_options = await service_dal.autocomplete_dal.get_fields_options(
        account_id, fields_to_scan, limit, is_container_view
    )

    ret_val: list[ProviderFieldInfoOptions] = []
    for field in fields_info.values():
        options = fields_options.get(field.id, [])
        workroom_field_info = ProviderFieldInfoOptions(**field.model_dump(), options=options)
        workroom_field_info.id = f"{PROVIDERS_FIELDS_COLUMN_NAME}.{field.id}"
        ret_val.append(workroom_field_info)
    return ret_val
