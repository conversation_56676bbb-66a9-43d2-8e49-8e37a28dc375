import logging
from datetime import UTC, datetime, timedelta
from typing import Annotated, Self

from fastapi import APIRouter, Depends, Query, Request
from fastapi import status as https_status
from prime_service_kit.shared_types import AccountArgumentType
from pydantic import BaseModel, Field, model_validator

from service.db import service_dal_depends
from service.dependencies import parse_filter_string, redis_type
from service.logic.filters_and_sort import CaseFilters, PSVFilters
from service.logic.statistics import (
    CaseByStatus,
    CasesByRiskCategory,
    SecurityLinddunMethodologyStats,
    SecurityMitreMethodologyStats,
)
from service.models import CasesByRiskCategoryStats, CasesByStatusStats, MethodologyStats, PsvCount, PsvStatus
from service.models.filters_and_sort import Filter, Operator
from service.services_clients import ServicesClients

DEFAULT_DAYS_SINCE = 14
LOGGER = logging.getLogger("trends")
trends_api = APIRouter(prefix="/trends", tags=["trends"])


async def get_case_filters_by_view_id(
    request: Request, account_id: AccountArgumentType, redis_client: redis_type, query_cases_view_id: str | None = None
) -> CaseFilters | None:
    if query_cases_view_id is None:
        return None
    query_view_api = ServicesClients.query_view_api()
    query_cases_view = await query_view_api.get_query_view_by_id(
        account_id,
        query_id=query_cases_view_id,
    )
    view_filters = await parse_filter_string(
        request=request,
        account_id=account_id,
        filter_str_query=query_cases_view.query_list or [],
        redis_client=redis_client,
    )
    return CaseFilters(view_filters)


view_id_case_filters = Annotated[CaseFilters | None, Depends(get_case_filters_by_view_id)]


class GetStatsParams(BaseModel):
    account_id: AccountArgumentType
    start: datetime = Field(
        Query(
            description="Start date of the stats",
            default_factory=lambda: datetime.now(UTC) - timedelta(days=DEFAULT_DAYS_SINCE),
        )
    )
    end: datetime = Field(Query(description="End date of the stats", default_factory=lambda: datetime.now(UTC)))

    @model_validator(mode="after")
    def validate_dates(self) -> Self:  # noqa: N804
        if self.start > self.end:
            raise ValueError("Start date should be less than end date")
        return self


@trends_api.get(
    "/{account_id}/cases-by-status",
    status_code=https_status.HTTP_200_OK,
)
async def get_cases_by_status(
    service_dal: service_dal_depends,
    case_filters: view_id_case_filters,
    params: GetStatsParams = Depends(),
) -> CasesByStatusStats:
    handler = CaseByStatus(service_dal, params.account_id, params.start, params.end)
    return await handler.get_stats(case_filters=case_filters)


@trends_api.get(
    "/{account_id}/cases-by-risk-category",
    status_code=https_status.HTTP_200_OK,
)
async def get_cases_by_risk_category(
    service_dal: service_dal_depends,
    case_filters: view_id_case_filters,
    params: GetStatsParams = Depends(),
) -> CasesByRiskCategoryStats:
    start = datetime.combine(params.start, datetime.min.time())
    end = datetime.combine(params.end, datetime.max.time())
    handler = CasesByRiskCategory(service_dal, params.account_id, start, end)
    return await handler.get_stats(case_filters=case_filters)


@trends_api.get(
    "/{account_id}/count-psv",
    status_code=https_status.HTTP_200_OK,
)
async def get_count_psv(service_dal: service_dal_depends, account_id: AccountArgumentType) -> PsvCount:
    psv_filters = PSVFilters.base_filter([Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN)])
    psv_count = await service_dal.psv_dal.get_psv_count_by_types(account_id, psv_filters=psv_filters)
    total = sum(psv_count.values())
    return PsvCount(count=total, by_type=psv_count)


@trends_api.get(
    "/{account_id}/mitre",
    status_code=https_status.HTTP_200_OK,
)
async def get_mitre(
    account_id: AccountArgumentType, service_dal: service_dal_depends, case_filters: view_id_case_filters
) -> MethodologyStats:
    handler = SecurityMitreMethodologyStats(service_dal, account_id, datetime.min, datetime.max)
    return await handler.get_stats(case_filters=case_filters)


@trends_api.get(
    "/{account_id}/linddun",
    status_code=https_status.HTTP_200_OK,
)
async def get_linddun(
    account_id: AccountArgumentType, service_dal: service_dal_depends, case_filters: view_id_case_filters
) -> MethodologyStats:
    handler = SecurityLinddunMethodologyStats(service_dal, account_id, datetime.min, datetime.max)
    return await handler.get_stats(case_filters=case_filters)
