import logging
from typing import Annotated

from fastapi import APIRouter, Path, status
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath

from service.db import service_dal_depends
from service.logic.external_container import get_external_container
from service.models import ExternalContainer

LOGGER = logging.getLogger("containers")
containers_api = APIRouter(prefix="/containers", tags=["containers"])


@containers_api.get(
    "/{account_id}/source/{source_id}/issue/{issue_id}",
    status_code=status.HTTP_200_OK,
)
async def get_container(
    account_id: AccountArgumentType,
    source_id: SourceIdPath,
    issue_id: Annotated[str, Path(description="Issue ID")],
    service_dal: service_dal_depends,
) -> ExternalContainer:
    LOGGER.info("Getting container for issue %s", issue_id)
    return await get_external_container(
        account_id=account_id,
        service_dal=service_dal,
        issue_id=issue_id,
        source_id=source_id,
    )
