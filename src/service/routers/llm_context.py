import logging
from typing import Annotated

from fastapi import APIRouter, Path
from prime_service_kit.fastapi_utils import PaginationResponse, pagination_args_type
from prime_service_kit.shared_types import AccountArgumentType
from starlette import status as httpstatus

from service.db import service_dal_depends
from service.dependencies import redis_type
from service.logic.llm_context import DataExporter
from service.logic.llm_context.models import ExportedRecord

llm_context_api = APIRouter(prefix="/llm-context", tags=["llm-context"])
LOGGER = logging.getLogger("llm-context")


@llm_context_api.get(
    "/{account_id}/cases/{container_id}",
    status_code=httpstatus.HTTP_200_OK,
    description="LLM context on container and sorted by provider_fields.updated",
    name="cases",
)
async def cases(
    account_id: AccountArgumentType,
    redis_client: redis_type,
    service_dal: service_dal_depends,
    pagination_args: pagination_args_type,
    container_id: Annotated[int, Path(description="Container case ID to filter by")],
) -> PaginationResponse[ExportedRecord]:
    LOGGER.info("Getting cases for account %s and container id %s", account_id, container_id)
    llm_context = DataExporter(
        account_id=account_id,
        service_dal=service_dal,
        by_container_id=container_id,
        redis_client=redis_client,
    )
    data = await llm_context.export_data(pagination_args)
    total = await llm_context.get_total_records()
    return PaginationResponse[ExportedRecord].from_results(data, pagination_args, total)
