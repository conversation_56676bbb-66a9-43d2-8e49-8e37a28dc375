{"TA0009": {"STIX ID": "x-mitre-tactic--d108ce10-2419-4cf9-a774-46161d6c6cfe", "name": "Collection", "description": "The adversary is trying to gather data of interest to their goal.\n\nCollection consists of techniques adversaries may use to gather information and the sources information is collected from that are relevant to following through on the adversary's objectives. Frequently, the next goal after collecting data is to steal (exfiltrate) the data. Common target sources include various drive types, browsers, audio, video, and email. Common collection methods include capturing screenshots and keyboard input.", "url": "https://attack.mitre.org/tactics/TA0009", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0011": {"STIX ID": "x-mitre-tactic--f72804c5-f15a-449e-a5da-2eecd181f813", "name": "Command and Control", "description": "The adversary is trying to communicate with compromised systems to control them.\n\nCommand and Control consists of techniques that adversaries may use to communicate with systems under their control within a victim network. Adversaries commonly attempt to mimic normal, expected traffic to avoid detection. There are many ways an adversary can establish command and control with various levels of stealth depending on the victim's network structure and defenses.", "url": "https://attack.mitre.org/tactics/TA0011", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0006": {"STIX ID": "x-mitre-tactic--2558fd61-8c75-4730-94c4-11926db2a263", "name": "Credential Access", "description": "The adversary is trying to steal account names and passwords.\n\nCredential Access consists of techniques for stealing credentials like account names and passwords. Techniques used to get credentials include keylogging or credential dumping. Using legitimate credentials can give adversaries access to systems, make them harder to detect, and provide the opportunity to create more accounts to help achieve their goals.", "url": "https://attack.mitre.org/tactics/TA0006", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0005": {"STIX ID": "x-mitre-tactic--78b23412-0651-46d7-a540-170a1ce8bd5a", "name": "Defense Evasion", "description": "The adversary is trying to avoid being detected.\n\nDefense Evasion consists of techniques that adversaries use to avoid detection throughout their compromise. Techniques used for defense evasion include uninstalling/disabling security software or obfuscating/encrypting data and scripts. Adversaries also leverage and abuse trusted processes to hide and masquerade their malware. Other tactics' techniques are cross-listed here when those techniques include the added benefit of subverting defenses. ", "url": "https://attack.mitre.org/tactics/TA0005", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0007": {"STIX ID": "x-mitre-tactic--c17c5845-175e-4421-9713-829d0573dbc9", "name": "Discovery", "description": "The adversary is trying to figure out your environment.\n\nDiscovery consists of techniques an adversary may use to gain knowledge about the system and internal network. These techniques help adversaries observe the environment and orient themselves before deciding how to act. They also allow adversaries to explore what they can control and what's around their entry point in order to discover how it could benefit their current objective. Native operating system tools are often used toward this post-compromise information-gathering objective. ", "url": "https://attack.mitre.org/tactics/TA0007", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0002": {"STIX ID": "x-mitre-tactic--4ca45d45-df4d-4613-8980-bac22d278fa5", "name": "Execution", "description": "The adversary is trying to run malicious code.\n\nExecution consists of techniques that result in adversary-controlled code running on a local or remote system. Techniques that run malicious code are often paired with techniques from all other tactics to achieve broader goals, like exploring a network or stealing data. For example, an adversary might use a remote access tool to run a PowerShell script that does Remote System Discovery. ", "url": "https://attack.mitre.org/tactics/TA0002", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0010": {"STIX ID": "x-mitre-tactic--9a4e74ab-5008-408c-84bf-a10dfbc53462", "name": "Exfiltration", "description": "The adversary is trying to steal data.\n\nExfiltration consists of techniques that adversaries may use to steal data from your network. Once they've collected data, adversaries often package it to avoid detection while removing it. This can include compression and encryption. Techniques for getting data out of a target network typically include transferring it over their command and control channel or an alternate channel and may also include putting size limits on the transmission.", "url": "https://attack.mitre.org/tactics/TA0010", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0040": {"STIX ID": "x-mitre-tactic--5569339b-94c2-49ee-afb3-2222936582c8", "name": "Impact", "description": "The adversary is trying to manipulate, interrupt, or destroy your systems and data.\n \nImpact consists of techniques that adversaries use to disrupt availability or compromise integrity by manipulating business and operational processes. Techniques used for impact can include destroying or tampering with data. In some cases, business processes can look fine, but may have been altered to benefit the adversaries' goals. These techniques might be used by adversaries to follow through on their end goal or to provide cover for a confidentiality breach.", "url": "https://attack.mitre.org/tactics/TA0040", "created": "14 March 2019", "last modified": "25 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0001": {"STIX ID": "x-mitre-tactic--ffd5bcee-6e16-4dd2-8eca-7b3beedf33ca", "name": "Initial Access", "description": "The adversary is trying to get into your network.\n\nInitial Access consists of techniques that use various entry vectors to gain their initial foothold within a network. Techniques used to gain a foothold include targeted spearphishing and exploiting weaknesses on public-facing web servers. Footholds gained through initial access may allow for continued access, like valid accounts and use of external remote services, or may be limited-use due to changing passwords.", "url": "https://attack.mitre.org/tactics/TA0001", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0008": {"STIX ID": "x-mitre-tactic--7141578b-e50b-4dcc-bfa4-08a8dd689e9e", "name": "Lateral Movement", "description": "The adversary is trying to move through your environment.\n\nLateral Movement consists of techniques that adversaries use to enter and control remote systems on a network. Following through on their primary objective often requires exploring the network to find their target and subsequently gaining access to it. Reaching their objective often involves pivoting through multiple systems and accounts to gain. Adversaries might install their own remote access tools to accomplish Lateral Movement or use legitimate credentials with native network and operating system tools, which may be stealthier. ", "url": "https://attack.mitre.org/tactics/TA0008", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0003": {"STIX ID": "x-mitre-tactic--5bc1d813-693e-4823-9961-abf9af4b0e92", "name": "Persistence", "description": "The adversary is trying to maintain their foothold.\n\nPersistence consists of techniques that adversaries use to keep access to systems across restarts, changed credentials, and other interruptions that could cut off their access. Techniques used for persistence include any access, action, or configuration changes that let them maintain their foothold on systems, such as replacing or hijacking legitimate code or adding startup code. ", "url": "https://attack.mitre.org/tactics/TA0003", "created": "17 October 2018", "last modified": "19 July 2019", "domain": "enterprise-attack", "version": 1}, "TA0004": {"STIX ID": "x-mitre-tactic--5e29b093-294e-49e9-a803-dab3d73b77dd", "name": "Privilege Escalation", "description": "The adversary is trying to gain higher-level permissions.\n\nPrivilege Escalation consists of techniques that adversaries use to gain higher-level permissions on a system or network. Adversaries can often enter and explore a network with unprivileged access but require elevated permissions to follow through on their objectives. Common approaches are to take advantage of system weaknesses, misconfigurations, and vulnerabilities. Examples of elevated access include: \n\n* SYSTEM/root level\n* local administrator\n* user account with admin-like access \n* user accounts with access to specific system or perform specific function\n\nThese techniques often overlap with Persistence techniques, as OS features that let an adversary persist can execute in an elevated context.  ", "url": "https://attack.mitre.org/tactics/TA0004", "created": "17 October 2018", "last modified": "06 January 2021", "domain": "enterprise-attack", "version": 1}, "TA0043": {"STIX ID": "x-mitre-tactic--daa4cbb1-b4f4-4723-a824-7f1efd6e0592", "name": "Reconnaissance", "description": "The adversary is trying to gather information they can use to plan future operations.\n\nReconnaissance consists of techniques that involve adversaries actively or passively gathering information that can be used to support targeting. Such information may include details of the victim organization, infrastructure, or staff/personnel. This information can be leveraged by the adversary to aid in other phases of the adversary lifecycle, such as using gathered information to plan and execute Initial Access, to scope and prioritize post-compromise objectives, or to drive and lead further Reconnaissance efforts.", "url": "https://attack.mitre.org/tactics/TA0043", "created": "02 October 2020", "last modified": "18 October 2020", "domain": "enterprise-attack", "version": 1}, "TA0042": {"STIX ID": "x-mitre-tactic--d679bca2-e57d-4935-8650-8031c87a4400", "name": "Resource Development", "description": "The adversary is trying to establish resources they can use to support operations.\n\nResource Development consists of techniques that involve adversaries creating, purchasing, or compromising/stealing resources that can be used to support targeting. Such resources include infrastructure, accounts, or capabilities. These resources can be leveraged by the adversary to aid in other phases of the adversary lifecycle, such as using purchased domains to support Command and Control, email accounts for phishing as a part of Initial Access, or stealing code signing certificates to help with Defense Evasion.", "url": "https://attack.mitre.org/tactics/TA0042", "created": "30 September 2020", "last modified": "30 September 2020", "domain": "enterprise-attack", "version": 1}}