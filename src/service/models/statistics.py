from datetime import datetime
from enum import Enum
from typing import Annotated

from pydantic import BaseModel, Field

from .levels import RiskScoreCategory


class QueriesName(Enum):
    CASES_BY_RISK_CATEGORY = "CasesByRiskCategory"
    CASES_BY_STATUS = "CasesByStatus"
    LINDDUN = "Linddun"
    MITRE = "Mitre"


class BaseCustomerTrends(BaseModel):
    start: datetime
    end: datetime
    query_name: str


class Point(BaseModel):
    x: str
    y: int

    def __str__(self) -> str:
        return f"({self.x}, {self.y})"


class DatePoint(BaseModel):
    x: datetime
    y: int

    def __str__(self) -> str:
        return f"({self.x}, {self.y})"


PointList = Annotated[list[DatePoint], Field(..., min_length=1)]


################ Stats objecs ################


class CasesByRiskCategoryStats(BaseCustomerTrends):
    intervene: PointList
    analyze: PointList
    monitor: PointList


class CasesByStatusStats(BaseCustomerTrends):
    scanned: PointList
    identified: PointList
    close: PointList


class CasesPerCategoryCount(BaseModel):
    risk_scores: dict[RiskScoreCategory, int]


class MethodologyStats(BaseCustomerTrends):
    categories: dict[str, CasesPerCategoryCount]
