from __future__ import annotations

from prime_gen_ai_service_client import HowOutput, QuestionsOutput, WhatOutput, WhereOutput, WhoOutput, WhyOutput
from prime_shared.common_types import SourceIdType
from pydantic import BaseModel, Field

from .concerns import ExternalPrimeConcern

SUMMARY_NO_VALUE = "Insufficient Information"


class ExternalContainerRisk(BaseModel):
    monitor: int
    analyze: int
    intervene: int


class ExternalContainerWorkroom(BaseModel):
    id_: int = Field(default=-1, alias="id")
    source_id: SourceIdType
    issue_id: str
    risk_score: int
    title: str
    provider_fields: dict[str, str]


class ExternalContainer(ExternalContainerWorkroom):
    issue_summary_short: str
    issue_summary_5w: Summary5W
    risk: ExternalContainerRisk
    concerns: list[ExternalPrimeConcern]


class _What(BaseModel):
    summary: str
    description: str

    @classmethod
    def from_what(cls, what: WhatOutput) -> _What:
        return cls(
            summary=what.summary.text if what.summary and what.summary.text else SUMMARY_NO_VALUE,
            description=what.description.text if what.description and what.description.text else SUMMARY_NO_VALUE,
        )

    @classmethod
    def empty(cls) -> _What:
        return cls(summary=SUMMARY_NO_VALUE, description=SUMMARY_NO_VALUE)


class _Where(BaseModel):
    environment: str
    components: str
    products: str

    @classmethod
    def from_where(cls, where: WhereOutput) -> _Where:
        return cls(
            environment=where.environment.text if where.environment and where.environment.text else SUMMARY_NO_VALUE,
            components=where.components.text if where.components and where.components.text else SUMMARY_NO_VALUE,
            products=where.products.text if where.products and where.products.text else SUMMARY_NO_VALUE,
        )

    @classmethod
    def empty(cls) -> _Where:
        return cls(environment=SUMMARY_NO_VALUE, components=SUMMARY_NO_VALUE, products=SUMMARY_NO_VALUE)


class _Who(BaseModel):
    stakeholders: str
    affected: str

    @classmethod
    def from_who(cls, who: WhoOutput) -> _Who:
        return cls(
            stakeholders=who.stakeholders.text if who.stakeholders and who.stakeholders.text else SUMMARY_NO_VALUE,
            affected=who.affected.text if who.affected and who.affected.text else SUMMARY_NO_VALUE,
        )

    @classmethod
    def empty(cls) -> _Who:
        return cls(stakeholders=SUMMARY_NO_VALUE, affected=SUMMARY_NO_VALUE)


class _Why(BaseModel):
    purpose: str
    impact: str

    @classmethod
    def from_why(cls, why: WhyOutput) -> _Why:
        return cls(
            purpose=why.purpose.text if why.purpose and why.purpose.text else SUMMARY_NO_VALUE,
            impact=why.impact.text if why.impact and why.impact.text else SUMMARY_NO_VALUE,
        )

    @classmethod
    def empty(cls) -> _Why:
        return cls(purpose=SUMMARY_NO_VALUE, impact=SUMMARY_NO_VALUE)


class _How(BaseModel):
    approach: str
    acceptance: str

    @classmethod
    def from_how(cls, how: HowOutput) -> _How:
        return cls(
            approach=how.approach.text if how.approach and how.approach.text else SUMMARY_NO_VALUE,
            acceptance=how.acceptance.text if how.acceptance and how.acceptance.text else SUMMARY_NO_VALUE,
        )

    @classmethod
    def empty(cls) -> _How:
        return cls(approach=SUMMARY_NO_VALUE, acceptance=SUMMARY_NO_VALUE)


class Summary5W(BaseModel):
    what: _What
    where: _Where
    who: _Who
    why: _Why
    how: _How

    @classmethod
    def from_question(cls, question: QuestionsOutput | None) -> Summary5W:
        if question is None:
            return cls.empty()
        return cls(
            what=_What.from_what(question.what),
            where=_Where.from_where(question.where),
            who=_Who.from_who(question.who),
            why=_Why.from_why(question.why),
            how=_How.from_how(question.how),
        )

    @classmethod
    def empty(cls) -> Summary5W:
        return cls(what=_What.empty(), where=_Where.empty(), who=_Who.empty(), why=_Why.empty(), how=_How.empty())
