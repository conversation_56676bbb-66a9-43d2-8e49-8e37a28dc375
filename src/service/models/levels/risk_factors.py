import enum
from typing import Annotated

from pydantic import BaseModel, Field, computed_field

RiskFactorScoreField = Annotated[int, Field(strict=True, ge=0, le=10)]
ScopeScoreField = Annotated[int, Field(strict=True, ge=1, le=3)]
SeverityScoreField = Annotated[int, Field(strict=True, ge=1, le=3)]


class RiskFactorLevel(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


RISK_FACTOR_RANGES = {
    RiskFactorLevel.LOW: range(1, 3),
    RiskFactorLevel.MEDIUM: range(3, 7),
    RiskFactorLevel.HIGH: range(7, 10 + 1),
}


def risk_factor_score_to_level(risk_factor_score: RiskFactorScoreField | None) -> RiskFactorLevel | None:
    if not risk_factor_score:
        return None
    return next(level for level, level_range in RISK_FACTOR_RANGES.items() if risk_factor_score in level_range)


def risk_factor_level_to_score(level: RiskFactorLevel) -> range:
    return RISK_FACTOR_RANGES[level]


class RiskFactors(BaseModel):
    confidentiality: RiskFactorScoreField | None = None
    confidentiality_explanation: str | None = None
    integrity: RiskFactorScoreField | None = None
    integrity_explanation: str | None = None
    availability: RiskFactorScoreField | None = None
    availability_explanation: str | None = None
    third_party_management: RiskFactorScoreField | None = None
    third_party_management_explanation: str | None = None
    compliance: RiskFactorScoreField | None = None
    compliance_explanation: str | None = None
    severity: SeverityScoreField | None = None
    severity_explanation: str | None = None
    scope: ScopeScoreField | None = None
    scope_explanation: str | None = None

    @computed_field(repr=False)
    def confidentiality_level(self) -> RiskFactorLevel | None:
        return risk_factor_score_to_level(self.confidentiality)

    @computed_field(repr=False)
    def integrity_level(self) -> RiskFactorLevel | None:
        return risk_factor_score_to_level(self.integrity)

    @computed_field(repr=False)
    def availability_level(self) -> RiskFactorLevel | None:
        return risk_factor_score_to_level(self.availability)

    @computed_field(repr=False)
    def third_party_management_level(self) -> RiskFactorLevel | None:
        return risk_factor_score_to_level(self.third_party_management)

    @computed_field(repr=False)
    def compliance_level(self) -> RiskFactorLevel | None:
        return risk_factor_score_to_level(self.compliance)
