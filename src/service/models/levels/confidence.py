import enum
from typing import Annotated

from pydantic import Field

ConfidenceField = Annotated[int, Field(strict=True, ge=0, le=100)]


class ConfidenceScoreLevel(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


CONFIDENCE_RANGES = {
    ConfidenceScoreLevel.LOW: range(1, 30),
    ConfidenceScoreLevel.MEDIUM: range(30, 70),
    ConfidenceScoreLevel.HIGH: range(70, 100 + 1),
}


def confidence_score_to_level(confidence: ConfidenceField | None) -> ConfidenceScoreLevel:
    if not confidence:
        return ConfidenceScoreLevel.LOW
    return next(level for level, level_range in CONFIDENCE_RANGES.items() if confidence in level_range)


def confidence_level_to_score(level: ConfidenceScoreLevel) -> range:
    return CONFIDENCE_RANGES[level]
