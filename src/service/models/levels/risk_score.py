import enum
import logging
from typing import Annotated

from pydantic import Field

LOGGER = logging.getLogger(__name__)

RiskScoreField = Annotated[int, Field(strict=True, ge=0, le=100)]


class RiskScoreCategory(str, enum.Enum):
    INTERVENE = "intervene"
    ANALYZE = "analyze"
    MONITOR = "monitor"
    NONE = "None"


RISK_SCORE_RANGES = {
    RiskScoreCategory.NONE: range(0, 1),
    RiskScoreCategory.MONITOR: range(1, 30),
    RiskScoreCategory.ANALYZE: range(30, 70),
    RiskScoreCategory.INTERVENE: range(70, 100 + 1),
}


def risk_score_to_category(score: RiskScoreField | None) -> RiskScoreCategory:
    LOGGER.debug("risk_score_to_category: score=%s", score)
    if score is None:
        return RiskScoreCategory.NONE
    if isinstance(score, float):
        LOGGER.error("risk_score_to_category: score is a float, converting to int")
        score = int(round(score))
    try:
        return next(category for category, category_range in RISK_SCORE_RANGES.items() if score in category_range)
    except Exception:
        LOGGER.exception("risk_score_to_category: the value of score is %s and its type is %s", score, type(score))
    return RiskScoreCategory.NONE


def risk_category_to_score(category: RiskScoreCategory) -> range:
    return RISK_SCORE_RANGES[category]
