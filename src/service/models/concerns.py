import enum
from enum import Enum

from pydantic import BaseModel, Field

from .controls import SecurityControl


class CodeType(enum.Enum):
    PYTHON = "python"
    SHELL = "shell"
    POWERSHELL = "powershell"
    GO = "go"
    JAVASCRIPT = "javascript"
    JAVA = "java"
    GENERIC = "generic"


class ConcernType(enum.Enum):
    GENERIC = "Generic"
    LINDDUN = "Linddun"
    MITRE = "Mitre"


class IssueAnalysisConcernMethodology(BaseModel):
    category: str
    type: ConcernType


class IssueAnalysisConcern(BaseModel):
    id: int
    short_description: str
    long_description: str
    methodology: IssueAnalysisConcernMethodology


class ImplementationStatus(str, Enum):
    UNKNOWN = "unknown"
    APPROVED = "approved"
    DISMISSED = "dismissed"


class Implementation(BaseModel):
    id: int
    concern_id: int
    recommendation: str
    status: ImplementationStatus
    raci: list[str] = Field(default_factory=list)
    code_snippets: dict[CodeType, str] = Field(default_factory=dict)
    control_id: str
    controls: dict[str, set[str]] = Field(default_factory=dict)

    def __str__(self) -> str:
        return f"{self.id}: {self.recommendation}"


class UserImplementationStatus(str, Enum):
    APPROVED = ImplementationStatus.APPROVED.value
    DISMISS = ImplementationStatus.DISMISSED.value


class ImplementationStatusUpdate(BaseModel):
    id: int
    status: UserImplementationStatus
    concern_id: int | None = None
    control_id: str | None = None


class ExternalImplementation(Implementation):
    pass


class ExternalControl(SecurityControl):
    implementations: list[ExternalImplementation]


class ExternalFrameworkConcern(IssueAnalysisConcern):
    controls: list[ExternalControl]


class PrimeRecommendation(BaseModel):
    id: str
    name: str
    description: str
    implementations: list[ExternalImplementation]


class ExternalPrimeConcern(IssueAnalysisConcern):
    recommendations: list[PrimeRecommendation]
