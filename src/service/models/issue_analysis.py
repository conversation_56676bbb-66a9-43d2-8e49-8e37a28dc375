from __future__ import annotations

import enum
import logging
from typing import Optional

from prime_file_manager_service_client import DocumentType
from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import BaseModel, ConfigDict, Field, computed_field

from .containers import Summary5W
from .levels import (
    ConfidenceField,
    ConfidenceScoreLevel,
    RiskFactors,
    RiskScoreCategory,
    RiskScoreField,
    confidence_score_to_level,
    risk_score_to_category,
)

LOGGER = logging.getLogger(__name__)


class ExternalIssueAnalysisWorkroom(BaseModel):
    account_id: AccountIdType
    source_id: SourceIdType
    issue_id: str
    risk_factors: RiskFactors
    issue_hash: str | None
    confidence: ConfidenceField | None = None
    is_automated: bool | None
    risk_score: RiskScoreField | None = None
    mitre_categories: list[str] = Field(default_factory=list)
    linddun_categories: list[str] = Field(default_factory=list)
    fire_summary: str | None = None

    @computed_field(repr=False)  # type: ignore[prop-decorator]
    @property
    def risk_score_category(self) -> RiskScoreCategory:
        return risk_score_to_category(self.risk_score)

    @computed_field(repr=False)  # type: ignore[prop-decorator]
    @property
    def confidence_level(self) -> ConfidenceScoreLevel:
        return confidence_score_to_level(self.confidence)

    def to_string(self) -> str:
        return self.model_dump_json(indent=2, by_alias=True, exclude={"issue_analysis_id"})


class IssueLinkType(enum.StrEnum):
    CONFLUENCE = enum.auto()
    GDRIVE = enum.auto()
    UNKNOWN = enum.auto()
    JIRA = enum.auto()

    @classmethod
    def document_type_to_link_type(cls, document_type: Optional[DocumentType]) -> IssueLinkType:
        if document_type is None:
            return IssueLinkType.UNKNOWN
        if document_type == DocumentType.CONFLUENCE:
            return IssueLinkType.CONFLUENCE
        if document_type == DocumentType.GDRIVE:
            return IssueLinkType.GDRIVE
        if document_type == DocumentType.JIRA:
            return IssueLinkType.JIRA
        return IssueLinkType.UNKNOWN


class IssueLinks(BaseModel):
    url: str
    link_type: IssueLinkType | None

    model_config = ConfigDict(extra="ignore")


class ExternalIssueAnalysis(ExternalIssueAnalysisWorkroom):
    long_ai_summary_5w: Summary5W
    short_ai_summary: str
    short_assessment: str
    long_assessment: str
    issue_links: list[IssueLinks] = Field(default_factory=list)
    keywords: list[str] = Field(default_factory=list)
