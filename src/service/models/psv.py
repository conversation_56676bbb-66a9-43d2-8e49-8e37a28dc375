from __future__ import annotations

from datetime import datetime
from enum import StrEnum, auto
from typing import Any

from prime_shared.common_types import SourceIdType
from pydantic import BaseModel


class PsvStatus(StrEnum):
    OPEN = auto()
    DONE = auto()
    DISMISSED = auto()

    @classmethod
    def _missing_(cls, value: Any) -> PsvStatus | None:
        if not isinstance(value, str):
            return None
        return next((member for member in cls if member.lower() == value.lower()), None)


class PotentialSecurityViolation(BaseModel):
    psv_id: int
    created_at: datetime
    title: str
    description: str
    type: str
    source_id: SourceIdType
    issue_id: str
    status: PsvStatus
    dismissed_reason: str | None
    project: str | None
    reporter: str | None
    detection_date: datetime
    issue_link: str


class SinglePsvUpdateRequest(BaseModel):
    new_status: PsvStatus
    dismissed_reason: str | None = None


BulkUpdatePsvDict = dict[int, SinglePsvUpdateRequest]


class BulkUpdatePsvRequest(BaseModel):
    violations: BulkUpdatePsvDict


class PsvCount(BaseModel):
    count: int
    by_type: dict[str, int]
