from datetime import datetime
from enum import StrEnum, auto

from prime_jobs import JobStatus
from prime_shared.common_types import SourceIdType
from pydantic import BaseModel, Field

from service.job_type import JobType


class JobCreatedResponse(BaseModel):
    job_id: int
    status: str


class JobStatusResponse(BaseModel):
    name: JobType
    status: JobStatus
    error: str | None
    job_id: int = Field(..., alias="id")
    created_at: datetime
    progress: int
    source_id: SourceIdType
    created_by: str


class JobCreateArg(BaseModel):
    job: JobType
    created_by: str


class JobSourceArg(JobCreateArg):
    source_id: SourceIdType


class JobSupportForceArgs(BaseModel):
    force: bool = False


class JobSupportParentIDArgs(BaseModel):
    parent_id: str | None = None


class JobBuildFieldsDataCreateArgs(JobSourceArg, JobCreateArg):
    job: JobType = JobType.BUILD_FIELDS_DATA


class JobUpdateIssuesCreateArgs(JobSupportForceArgs, <PERSON>SourceArg, JobCreateArg):
    job: JobType = JobType.UPDATE_ISSUES
    update_fields_only: bool = False


class JobPsvCreateArgs(JobSupportForceArgs, JobSourceArg, JobCreateArg):
    job: JobType = JobType.SECURITY_VIOLATION


class JobClassificationCreateArgs(JobSupportParentIDArgs, JobSupportForceArgs, JobSourceArg, JobCreateArg):
    job: JobType = JobType.CLASSIFICATION


class JobSummaryCreateArgs(JobSupportParentIDArgs, JobSupportForceArgs, JobSourceArg, JobCreateArg):
    job: JobType = JobType.SUMMARY


class JobState(StrEnum):
    TERMINATED = auto()
    COMPLETED = auto()
    FAILED = auto()
    JOB_TIMEOUT = auto()
    COMPLETED_WITH_ERRORS = auto()


job_create_args_t = (
    JobCreateArg
    | JobPsvCreateArgs
    | JobUpdateIssuesCreateArgs
    | JobBuildFieldsDataCreateArgs
    | JobSummaryCreateArgs
    | JobClassificationCreateArgs,
)
