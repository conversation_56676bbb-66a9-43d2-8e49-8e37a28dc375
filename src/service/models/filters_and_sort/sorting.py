from __future__ import annotations

import json
import logging
from enum import Enum
from functools import cached_property
from typing import TYPE_CHECKING, Any, TypeVar
from urllib.parse import quote

from pydantic import BaseModel, ConfigDict, Field, computed_field, field_validator, model_validator
from pydantic_core.core_schema import ValidationInfo
from sqlalchemy import text

from service.models import ProviderFieldType

from .shared import (
    extract_provider_field_type,
    provider_type_to_sql_type,
    validate_inner_field_type,
)

if TYPE_CHECKING:
    from prime_db_utils import BaseTableSQLModel
    from sqlalchemy.orm import InstrumentedAttribute
    from sqlmodel.sql._expression_select_cls import Select, SelectOfScalar

    T = TypeVar("T", bound=BaseTableSQLModel)


LOGGER = logging.getLogger(__name__)

DELIMITER = "."


class SortDirection(str, Enum):
    ASC = "asc"
    DESC = "desc"


class SortField(BaseModel):
    field: str = Field(..., description="The field to sort by")
    direction: SortDirection
    inner_field: str | None = Field(
        default=None, description="The for json fields: 'field=inner_filed:asc:inner_field_type'"
    )
    inner_field_type: ProviderFieldType | None = Field(default=None, description="The type of the custom inner field")

    model_config = ConfigDict(
        extra="forbid",
    )

    @model_validator(mode="before")
    def extract_provider_field_data(cls, values: dict[str, Any], context: ValidationInfo | None) -> dict[str, Any]:  # noqa: N805
        if (field := values.get("field")) is None:
            return values
        if len(field.split(DELIMITER)) == 2:
            values["field"], values["inner_field"] = field.split(DELIMITER)
        extract_provider_field_type(values, context)
        return values

    @field_validator("inner_field_type", mode="before")
    def validate_inner_field_type(
        cls,  # noqa: N805
        value: ProviderFieldType | str | None,
    ) -> ProviderFieldType | None:
        return validate_inner_field_type(value)

    @property
    def name(self) -> str:
        return self._to_snake_case()

    def _to_snake_case(self) -> str:
        result = []
        prev_char_lower = True

        for i, c in enumerate(self.field):
            if (c.isupper() and prev_char_lower and i > 0) or (
                c.isdigit() and i > 0 and not self.field[i - 1].isdigit()
            ):
                result.append("_")
            result.append(c.lower())
            prev_char_lower = c.islower() or c.isdigit()

        return "".join(result).strip("_")

    def __str__(self) -> str:
        return self.as_json_str()

    @property
    def field_str(self) -> str | None:
        return f"{self.field}{DELIMITER}{self.inner_field}" if self.inner_field else self.field

    def as_json_str(self) -> str:
        data = {
            "field": self.field_str,
            "direction": self.direction.value,
        }
        return json.dumps(data)

    def as_url_str(self) -> str:
        return quote(self.as_json_str())

    @computed_field  # type: ignore[prop-decorator]
    @cached_property
    def sql_type(self) -> str | None:
        return provider_type_to_sql_type(self.inner_field_type)

    def get_sort_query(
        self,
        q: SelectOfScalar[T] | Select[T],
        model_field: InstrumentedAttribute[T],
    ) -> SelectOfScalar[T] | Select[T]:
        if self.inner_field is None:
            order_by_clause = getattr(model_field, self.direction)()
            order_by_clause = order_by_clause.nulls_last()  # or nulls_first()
            query = q.order_by(order_by_clause)

        else:
            cast_to = f"::{self.sql_type}" if self.sql_type else ""
            order_by_clause = text(
                f"({model_field.table.name}.{self.name}->>'{self.inner_field}'){cast_to} {self.direction.value}"
            )
            query = q.order_by(order_by_clause)
        return query
