
#### 1. Define Import Statements and Filter Definitions

Before starting, you should ensure that you have the necessary imports and filter definitions in place. These might typically include your specific table models and the `Filter` class.

```python
from typing import List, Any, Optional, Type
from sqlalchemy import select
from base_filters import BaseFilters, Filter
from your_model_definitions import YourTableModel, AnotherTableModel  # Replace with your actual table models
```

#### 2. Create the Subclass

Create a subclass of `BaseFilters`:

```python
class YourCustomFilters(BaseFilters[YourTableModel]):
    ALLOWED_FILTERS = [
        "filter_field_1",
        "filter_field_2",
    ]
    DEFAULT_FILTERS = [
        Filter(field="filter_field_1", value="default_value"),
        # Add more filters as needed
    ]
    TABLES = [YourTableModel, AnotherTableModel]
    QUERY_TABLE = YourTableModel

    JOIN = {
        AnotherTableModel: (YourTableModel.foreign_key_field, AnotherTableModel.primary_key_field)
    }
    CUSTOM_FILTERS = ["custom_filter_field"]

    def __init__(self, filters: Optional[List[Filter]] = None, exclude_filters: Optional[List[str]] = None) -> None:
        super().__init__(filters, exclude_filters)

    def build_query(self, q: SelectOfScalar[YourTableModel]) -> SelectOfScalar[YourTableModel]:
        return self._generate_query(q)

    def custom_filter_field(self, f: Filter, q: SelectOfScalar[YourTableModel]) -> SelectOfScalar[YourTableModel]:
        # Implement custom filter logic
        return q
```

#### 3. Property and Attribute Definitions

- **ALLOWED_FILTERS**: List of filter field names that are allowed.
- **DEFAULT_FILTERS**: List of default `Filter` instances that are always included if they are not excluded.
- **TABLES**: List of tables involved in the query.
- **QUERY_TABLE**: The primary table for the query.
- **JOIN**: Dictionary mapping join tables to tuples containing the foreign key and primary key fields for the join.
- **CUSTOM_FILTERS**: List of custom filters that require special handling.

#### 4. Implement Custom Filter Logic

In case you need specific custom filter logic, create a method corresponding to the custom filter field:

```python
def custom_filter_field(self, f: Filter, q: SelectOfScalar[YourTableModel]) -> SelectOfScalar[YourTableModel]:
    # Example: Filter based on a custom criterion
    try:
        custom_value = CustomEnumType(f.value)
    except ValueError:
        return q

    custom_range = get_range_from_custom_value(custom_value)
    q = q.where(AnotherTableModel.custom_field >= custom_range.start).where(
        AnotherTableModel.custom_field < custom_range.stop
    )
    return q
```

after that add the `custom_filter_name` to the `CUSTOM_FILTERS`

### Example of Usage

Here's an example showing how to use the custom filter class:

```python
filters = [
    Filter(field="filter_field_1", raw_value="specific_value_1"),
    Filter(field="filter_field_2", raw_value="specific_value_2"),
]

custom_filters = YourCustomFilters(filters)
query = custom_filters.build_query(select(YourTableModel))

# Execute the query using your session
results = session.execute(query).scalars().all()
```