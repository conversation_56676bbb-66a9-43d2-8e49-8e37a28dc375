from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Any, cast

from pydantic_core.core_schema import ValidationInfo

from service.models import ProviderFieldsInfoMapping, ProviderFieldType

if TYPE_CHECKING:
    pass

LOGGER = logging.getLogger(__name__)


def extract_provider_field_type(values: dict[str, Any], context: ValidationInfo | None) -> None:
    if values.get("inner_field") is not None and values.get("inner_field_type") is None:
        inner_field = cast(str, values.get("inner_field"))
        provider_fields_info: ProviderFieldsInfoMapping = (context.context or {}) if context else {}
        inner_field_min_schema = provider_fields_info.get(inner_field, None)
        if inner_field_min_schema is None:
            LOGGER.warning("Couldn't find type for %s", inner_field)
            inner_field_type = ProviderFieldType.STRING
        else:
            inner_field_type = validate_inner_field_type(inner_field_min_schema.type)  # type: ignore[assignment]
        values["inner_field_type"] = inner_field_type


def validate_inner_field_type(
    value: ProviderFieldType | str | None,
) -> ProviderFieldType | None:
    if value is None:  # this is None when inner_field is None
        return value
    try:
        return ProviderFieldType(value)
    except ValueError:
        LOGGER.warning("Type %s is not supported, using string", value)
        return ProviderFieldType.STRING


SQL_TYPE_MAPPING = {
    ProviderFieldType.STRING: "text",
    ProviderFieldType.NUMBER: "float",
    ProviderFieldType.ARRAY: "jsonb",
    ProviderFieldType.ENUM: "text",
    ProviderFieldType.BOOLEAN: "boolean",
    ProviderFieldType.DATE: "date",
}


def provider_type_to_sql_type(provider_type: ProviderFieldType | None) -> str | None:
    if provider_type is None:
        return None
    return SQL_TYPE_MAPPING.get(provider_type, provider_type.value)
