from __future__ import annotations

import datetime
import json
import logging
from collections import defaultdict
from enum import Enum
from functools import cached_property
from typing import Any, Generic, Self, TypeVar, Union, cast, overload
from urllib.parse import quote

from dateutil.parser import parse as date_parse
from prime_db_utils import BaseTableSQLModel
from pydantic import BaseModel, ConfigDict, Field, computed_field, field_validator, model_validator
from pydantic_core.core_schema import ValidationInfo
from sqlalchemy import ColumnExpressionArgument, func
from sqlmodel import AutoString, Integer, select
from sqlmodel.sql.expression import Select, SelectOfScalar, and_

from service.models import ProviderFieldType

from .shared import (
    extract_provider_field_type,
    provider_type_to_sql_type,
    validate_inner_field_type,
)

LOGGER = logging.getLogger("filtering")
DELIMITER = "."
T = TypeVar("T", bound=BaseTableSQLModel)
QUERY_T = TypeVar("QUERY_T", bound=Union[SelectOfScalar[T], Select[T]])  # type: ignore[valid-type]
BOOL_STATEMENT = ColumnExpressionArgument[bool]
CUSTOM_FILTER_PREFIX = "_custom_filter_"


class Operator(Enum):
    EQ = "eq"
    NE = "ne"
    GT = "gt"
    GTE = "gte"
    LT = "lt"
    LTE = "lte"
    BETWEEN = "between"
    EXIST = "exist"


class Filter(BaseModel):
    field: str = Field(..., description="The field to filter by")
    value: str | list[str] | None = None  # Can be None for certain filter types (e.g., existence)
    op: Operator = Field(
        default=Operator.EQ,
        description="The comparison operator (e.g., eq, ne, gt, gte, lt, lte)",
    )
    inner_field: str | None = Field(
        default=None, description="The inner field for json fields (e.g., field:inner_field=value:op)"
    )
    inner_field_type: ProviderFieldType | None = Field(default=None, description="The type of the custom inner field")

    model_config = ConfigDict(
        extra="forbid",
    )

    @property
    def list_value(self) -> list[str]:
        if self.value is None:
            return []
        return self.value if isinstance(self.value, list) else [self.value]

    @property
    def sql_list_value(self) -> list[str]:
        return [value.replace("'", "''") for value in self.list_value]

    @property
    def is_val_list(self) -> bool:
        return self.list_value is not None and len(self.list_value) > 1

    @property
    def str_value(self) -> str | None:
        if self.is_val_list:
            return None
        return self.list_value[0]

    @model_validator(mode="before")
    def extract_provider_field_data(cls, values: dict[str, Any], context: ValidationInfo | None) -> dict[str, Any]:  # noqa: N805
        if (field := values.get("field")) is None:
            return values
        if len(field.split(DELIMITER)) == 2:
            values["field"], values["inner_field"] = field.split(DELIMITER)
        extract_provider_field_type(values, context)
        return values

    @field_validator("inner_field_type", mode="before")
    def validate_inner_field_type(
        cls,  # noqa: N805
        value: ProviderFieldType | str | None,
    ) -> ProviderFieldType | None:
        return validate_inner_field_type(value)

    @property
    def field_str(self) -> str | None:
        return f"{self.field}{DELIMITER}{self.inner_field}" if self.inner_field else self.field

    def __str__(self) -> str:
        return self.as_json_str()

    def as_json_str(self) -> str:
        v = self.value if isinstance(self.value, list) else [self.value] if self.value is not None else None
        data = {
            "field": self.field_str,
            "value": v,
            "op": self.op.value,
        }
        return json.dumps(data)

    def as_url_str(self) -> str:
        return quote(self.as_json_str())

    @computed_field  # type: ignore[prop-decorator]
    @cached_property
    def inner_sql_type(self) -> str | None:
        return provider_type_to_sql_type(self.inner_field_type)

    @cached_property
    def field_type(self) -> ProviderFieldType | None:
        return ProviderFieldType(self.value)

    @cached_property
    def sql_type(self) -> str | None:
        return provider_type_to_sql_type(self.field_type)


class JoinOption:
    def __init__(self) -> None:
        self.statements: list[ColumnExpressionArgument[bool]] = []
        self.is_outer: bool = False


class BaseFilters(Generic[T]):
    BASE_ALLOWED_FILTERS = ["account_id", "source_id", "created_at"]
    CUSTOM_FILTERS: list[str] = []
    BASE_DEFAULT_FILTERS: list[Filter] = []
    TABLES: list[type[BaseTableSQLModel]] = []
    JOIN: dict[type[BaseTableSQLModel], BOOL_STATEMENT] = {}
    ALLOWED_DUP_FILTERS: list[str] = []

    def __init__(
        self,
        filters: list[Filter] | None = None,
        exclude_filters: list[str] | None = None,
        join_default_tables: bool = False,
    ) -> None:
        """
        Initialize the Filtering object.

        Args:
            filters (list[Filter] | None): A list of Filter objects to include in the filtering.
            exclude_filters (list[str] | None): A list of filter names to exclude from the filtering.
              (can be found in default filters)

        """
        if not self.validate_custom_filters():
            raise NotImplementedError("Custom filters not implemented")
        self._filters = [f for f in self.default_filters if f.field not in (exclude_filters or [])]
        for _filter in filters or []:
            self._add_filter(_filter)
        self._dynamic_join: dict[type[BaseTableSQLModel], JoinOption] = defaultdict(JoinOption)
        self.join_default_tables = join_default_tables

    @property
    def total_filters(self) -> int:
        return len(self._filters)

    def add_to_dynamic_join(
        self, table: type[BaseTableSQLModel], statements: list[BOOL_STATEMENT], is_outer: bool | None = None
    ) -> None:
        unique = [statement for statement in statements if statement not in self._dynamic_join[table].statements]
        self._dynamic_join[table].statements.extend(unique)
        if is_outer:
            self._dynamic_join[table].is_outer = is_outer

    def _get_custom_filters(self, filter_name: str) -> str | None:
        return getattr(self, f"{CUSTOM_FILTER_PREFIX}{filter_name}", None)

    def validate_custom_filters(self) -> bool:
        return all(self._get_custom_filters(filter_name) is not None for filter_name in self.CUSTOM_FILTERS)

    def _add_filter(self, _filter: Filter) -> None:
        if _filter.field not in self.allowed_filters:
            LOGGER.warning("Invalid filter field: %s", _filter.field)
            return
        self._filters.append(_filter)
        column = self._get_query_column(_filter)
        if column is not None and not callable(column):
            try:
                self._set_filterable_column(_filter, column)
            except Exception:
                LOGGER.warning("Invalid filter value: %s", _filter.value)

        elif callable(column) and not getattr(self, _filter.field, None):
            self.__setattr__(_filter.field, column)

    def _set_filterable_column(self, _filter: Filter, column: Any) -> None:
        if isinstance(column.type, AutoString):
            if _filter.is_val_list:
                self.__setattr__(_filter.field, [str(v) for v in _filter.list_value])
            else:
                self.__setattr__(_filter.field, str(_filter.str_value))
        elif column.type.python_type is bool:
            if _filter.is_val_list:
                self.__setattr__(_filter.field, [(v.lower() == "true") for v in _filter.list_value])
            else:
                self.__setattr__(_filter.field, _filter.str_value.lower() == "true" if _filter.str_value else None)
        elif column.type.python_type is datetime.datetime:
            self._set_datetime_value(_filter)
        elif Enum in getattr(column.type.python_type, "__mro__", []):
            if _filter.is_val_list:
                self.__setattr__(_filter.field, [column.type.python_type(v) for v in _filter.list_value])
            else:
                self.__setattr__(_filter.field, column.type.python_type(_filter.str_value))
        elif isinstance(column.type, Integer) and _filter.is_val_list:
            self.__setattr__(_filter.field, [int(v) for v in _filter.list_value])
        else:
            self.__setattr__(_filter.field, column.type.python_type(_filter.str_value))

    def _set_datetime_value(self, _filter: Filter) -> None:
        if _filter.op == Operator.BETWEEN:
            if not _filter.list_value:
                raise ValueError("Invalid filter value for between operator")
            date_v_l = [date_parse(d) for d in _filter.list_value]
            self.__setattr__(_filter.field, date_v_l)
        else:
            date_v = date_parse(cast(str, _filter.str_value))
            self.__setattr__(_filter.field, date_v)

    @classmethod
    def remove_duplicate_filters(cls, filters: list[Filter]) -> list[Filter]:
        seen = set()
        unique_filters = []
        for _filter in filters:
            if _filter.inner_field:
                full_field_name = f"{_filter.field}.{_filter.inner_field}"
                if full_field_name not in seen:
                    seen.add(full_field_name)
                    unique_filters.append(_filter)
            elif _filter.field not in seen:
                seen.add(_filter.field)
                unique_filters.append(_filter)
            elif _filter.field in cls.ALLOWED_DUP_FILTERS:
                unique_filters.append(_filter)

        return unique_filters

    def add_filters(self, _filters: list[Filter]) -> Self:
        for _filter in _filters:
            self.add_filter(_filter)
        return self

    @overload
    def add_filter(self, _filter: Filter) -> Self: ...

    @overload
    def add_filter(self, _filter: str, value: str | None = None, op: str = "eq") -> Self: ...

    def add_filter(self, _filter: Filter | str, value: str | None = None, op: str = "eq") -> Self:
        if isinstance(_filter, Filter):
            self._add_filter(_filter)
        else:
            self._add_filter(Filter(field=_filter, value=value, op=op))  # type: ignore[arg-type]
        return self

    def get_filter(self, field: str) -> Filter | None:
        return getattr(self, field, None)

    def get_attr(self, field: str) -> Any | None:
        return getattr(self, field, None)

    @property
    def filters_list(self) -> list[Filter]:
        return self._filters

    @property
    def allowed_filters(self) -> list[str]:
        return list(set(self.BASE_ALLOWED_FILTERS + getattr(self, "ALLOWED_FILTERS", [])))

    @property
    def default_filters(self) -> list[Filter]:
        return self.BASE_DEFAULT_FILTERS + getattr(self, "DEFAULT_FILTERS", [])

    @property
    def tables(self) -> list[type[BaseTableSQLModel]]:
        return self.TABLES + getattr(self, "TABLES", [])

    @property
    def query_table(self) -> BaseTableSQLModel:
        return cast(BaseTableSQLModel, getattr(self, "QUERY_TABLE"))

    def _get_query_column(self, f: Filter) -> Any | None:
        if _filter_func := self._get_custom_filters(f.field):
            return _filter_func
        for table in self.tables:
            if hasattr(table, f.field):
                return getattr(table, f.field)
        return None

    def _get_statement(self, f: Filter, query_column: Any) -> BOOL_STATEMENT | None:
        statement = None
        if f.op == Operator.EQ:
            if f.is_val_list:
                statement = cast(BOOL_STATEMENT, query_column.in_(getattr(self, f.field)))
            elif isinstance(query_column.type, AutoString):
                statement = cast(
                    BOOL_STATEMENT,
                    func.lower(query_column).contains(func.lower(getattr(self, f.field))),
                )
            else:
                statement = cast(BOOL_STATEMENT, query_column == getattr(self, f.field))
        elif f.op == Operator.NE:
            statement = cast(BOOL_STATEMENT, query_column != getattr(self, f.field))
        elif f.op == Operator.GT:
            statement = cast(BOOL_STATEMENT, query_column > getattr(self, f.field))
        elif f.op == Operator.GTE:
            statement = cast(BOOL_STATEMENT, query_column >= getattr(self, f.field))
        elif f.op == Operator.LT:
            statement = cast(BOOL_STATEMENT, query_column < getattr(self, f.field))
        elif f.op == Operator.LTE:
            statement = cast(BOOL_STATEMENT, query_column <= getattr(self, f.field))
        elif f.op == Operator.BETWEEN:
            statement = cast(BOOL_STATEMENT, query_column.between(*getattr(self, f.field)))
        return statement

    def _get_filter_query(self, _filter: Filter, query: QUERY_T) -> QUERY_T:
        query_column = self._get_query_column(_filter)
        if query_column is None:
            LOGGER.warning("Invalid filter field: %s", _filter.field)
            return query
        if callable(query_column):  # custom filter
            return cast(QUERY_T, query_column(_filter, query))
        statement = self._get_statement(_filter, query_column)  # column filter
        if statement is None:
            LOGGER.warning("Invalid filter value: %s", _filter.value)
            return query
        if join_table := self._use_join_tables(query, query_column):
            self._enable_table_to_join(join_table)
            self.add_to_dynamic_join(join_table, [statement])
        else:
            query = cast(QUERY_T, query.where(statement))
        return query

    def _enable_table_to_join(
        self, join_table: type[BaseTableSQLModel], additional: list[BOOL_STATEMENT] | None = None
    ) -> None:
        join_statement = self.JOIN[join_table]
        self.add_to_dynamic_join(join_table, [join_statement] + (additional or []))

    def _get_join_table(self, table_name: str) -> type[BaseTableSQLModel] | None:
        tables = set(self.JOIN.keys()).union(set(self._dynamic_join.keys()))
        return next((table for table in tables if table_name == table.__tablename__), None)

    def _use_join_tables(self, query: QUERY_T, query_column: Any) -> type[BaseTableSQLModel] | None:
        if query_column.table.name == self.query_table.__tablename__:
            return None
        table_name = query_column.table.name
        joined = any(join[0].name == table_name for join in query._setup_joins)  # type: ignore[attr-defined]
        if joined:
            LOGGER.info("Already joined table: %s", table_name)
            return None
        try:
            join_table = next(table for table in self.JOIN if table_name == table.__tablename__)
        except StopIteration:
            LOGGER.warning("No join table found for %s", table_name)
            return None
        return join_table

    def _do_join(self, query: QUERY_T, join_table: type[BaseTableSQLModel]) -> QUERY_T:
        joined = any(join[0].name == join_table.__tablename__ for join in query._setup_joins)  # type: ignore[attr-defined]
        if joined:
            return query
        if join_table not in self._dynamic_join:
            LOGGER.warning("No join table found for %s", join_table.__tablename__)
            return query
        dynamic_join_statements = self._dynamic_join[join_table].statements
        is_outer = self._dynamic_join[join_table].is_outer
        join_statement = and_(*dynamic_join_statements)
        return cast(QUERY_T, query.join(join_table, join_statement, isouter=is_outer))

    def _generate_dynamic_join(self, query: QUERY_T) -> QUERY_T:
        if self.join_default_tables:
            for table in self.JOIN:
                self._enable_table_to_join(table)
        for table in self._dynamic_join:
            query = self._do_join(query, table)
        return query

    def _generate_query(self, query: QUERY_T | None = None) -> QUERY_T:
        q = query if query is not None else select(self.query_table)  # type: ignore[call-overload]
        for _filter in self.filters_list:
            q = self._get_filter_query(_filter, q)
        return self._generate_dynamic_join(q)

    def build_query(self, query: QUERY_T) -> QUERY_T:
        return self._generate_query(query)

    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.filters_list})"
