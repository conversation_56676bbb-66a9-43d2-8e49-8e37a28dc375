from __future__ import annotations

import logging
from enum import Enum
from typing import Annotated

from pydantic import BaseModel, Field

LOGGER = logging.getLogger(__name__)

SEVERITY_THRESHOLD = 2
SCOPE_THRESHOLD = 2
PROBABILITY_THRESHOLD = 2

CategoryScoreAIField = Annotated[float, Field(ge=0, le=1)]
ScopeScoreAIField = Annotated[int, Field(ge=1, le=3)]
SeverityScoreAIField = Annotated[int, Field(ge=1, le=3)]
ConfidenceAIField = Annotated[int, Field(ge=0, le=100)]


class FilterImpact(Enum):
    NONE = "None"
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"


class BaseStepResults(BaseModel):
    error: bool = False
