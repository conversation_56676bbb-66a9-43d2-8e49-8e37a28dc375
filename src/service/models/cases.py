from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Annotated, Any

from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import BaseModel, Field, StringConstraints, computed_field

from .concerns import ExternalFrameworkConcern, ExternalPrimeConcern
from .fields import ProviderFieldInfo
from .issue_analysis import ExternalIssueAnalysis, ExternalIssueAnalysisWorkroom, RiskScoreCategory
from .levels import RiskScoreField, risk_category_to_score, risk_score_to_category

NoWhitespaceStr = Annotated[
    str,
    StringConstraints(pattern=r"^[^\s]+$", min_length=1),
]


class CaseStatus(str, Enum):
    OPEN = "open"
    DONE = "done"
    DISMISSED = "dismissed"

    @classmethod
    def _missing_(cls, value: Any) -> CaseStatus | None:
        if not isinstance(value, str):
            return None
        return next((member for member in cls if member.lower() == value.lower()), None)


class RiskScoreByCaseId(BaseModel):
    case_id: int
    risk_score: int | None


class CaseAuditAction(str, Enum):
    create = "create_case"
    view = "user_view_case"
    update_status = "update_status"
    override_risk_category = "override_risk_category"


class CaseAuditOverrideRiskCategoryArgs(BaseModel):
    old_risk_score: RiskScoreField
    new_risk_score: RiskScoreField

    @computed_field(repr=False)  # type: ignore[prop-decorator]
    @property
    def old_risk_score_category(self) -> RiskScoreCategory:
        return risk_score_to_category(self.old_risk_score)

    @computed_field(repr=False)  # type: ignore[prop-decorator]
    @property
    def new_risk_score_category(self) -> RiskScoreCategory:
        return risk_score_to_category(self.new_risk_score)


class CaseAuditUpdateStatusArgs(BaseModel):
    new_status: CaseStatus
    old_status: CaseStatus
    reason: str | None = None


class BaseCaseComment(BaseModel):
    user: str
    text: str


class CreateCaseComment(BaseCaseComment):
    user: str = Field(..., min_length=2)
    text: str = Field(..., min_length=1)


class CaseComment(BaseCaseComment):
    created_at: datetime
    id: int


class ExternalCaseHistory(BaseModel):
    user: str
    audit_action: CaseAuditAction
    audit_action_args: dict[str, Any] | None
    created_at: datetime


class ExternalCaseWorkroom(BaseModel):
    account_id: AccountIdType
    source_id: SourceIdType
    issue_id: str
    case_id: int
    status: CaseStatus
    issue_analysis: ExternalIssueAnalysisWorkroom
    write_back_recommendations: bool
    title: str
    link: str
    labels: list[str]
    provider_fields: dict[str, str]
    provider_fields_min_schema: dict[str, ProviderFieldInfo] = Field(default_factory=dict)
    parents: list[str] = Field(default_factory=list)
    progress_percentage: int


class ExternalCase(ExternalCaseWorkroom):
    framework_concerns: dict[str, list[ExternalFrameworkConcern]]
    prime_concerns: list[ExternalPrimeConcern]
    history: list[ExternalCaseHistory]
    comments: list[CaseComment] | None
    issue_analysis: ExternalIssueAnalysis


class BulkUpdateCasesRequest(BaseModel):
    status: CaseStatus | None = None
    dismissed_reason: str | None = None
    labels: set[NoWhitespaceStr] | None = None
    risk_score_category: RiskScoreCategory | None = None
    issues_ids: set[str]

    @property
    def risk_score(self) -> int | None:
        if self.risk_score_category is None:
            return None
        return risk_category_to_score(self.risk_score_category).start


class GenerateRecommendationsForConcernIdsRequest(BaseModel):
    concern_ids: set[int]


class SearchResponse(BaseModel):
    id: int
    issue_id: str
    title: str
    issue_type: str
    is_container: bool
    source_id: SourceIdType


class CaseData(BaseModel):
    id: int
    source_id: SourceIdType
    issue_id: str
    jira_issue_url: str
