import enum
from datetime import datetime
from typing import Any

from pydantic import BaseModel, ConfigDict

provider_field_types = str | int | bool | datetime | list[dict[str, Any]] | list[str] | None


class ProviderFieldType(str, enum.Enum):
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    DATE = "date"
    ARRAY = "array"
    ENUM = "enum"


class ProviderFieldInfo(BaseModel):
    type: ProviderFieldType
    id: str
    name: str


ProviderFieldsInfoMapping = dict[str, ProviderFieldInfo]


class ProviderFieldData(ProviderFieldInfo):
    value: provider_field_types


class ExternalProviderFields(BaseModel):
    id: str
    link: str
    summary: str

    model_config = ConfigDict(
        extra="allow"  # for provider fields
    )


class ProviderFieldInfoOptions(ProviderFieldInfo):
    options: list[str] | None = None


class ProviderFieldsOptionsList(BaseModel):
    fields: list[ProviderFieldInfoOptions]
    total: int
