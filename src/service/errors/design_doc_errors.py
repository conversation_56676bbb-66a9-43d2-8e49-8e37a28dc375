import json

from prime_service_kit import BaseServiceError
from starlette import status

from .error_codes import ErrorCode


class DesignDocDuplicateError(BaseServiceError):
    http_status_code = status.HTTP_409_CONFLICT
    error_type = ErrorCode.DesignDocDuplicateError.name
    is_reliability_issue = False

    def __init__(self, names: list[str]):
        self._names = names
        super().__init__()

    def _get_msg(self) -> str:
        return json.dumps(self._names)


class DesignDocAttachContextError(BaseServiceError):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_type = ErrorCode.DesignDocAttachContextError.name
    is_reliability_issue = False

    def __init__(self, doc_id: int, context_id: int):
        self._doc_id = doc_id
        self._context_id = context_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Error attaching context to design doc {self._doc_id} with context id {self._context_id}"


class DesignDocNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.DesignDocNotFoundError.name
    is_reliability_issue = False

    def __init__(self, doc_id: int):
        self._doc_id = doc_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Design document with ID {self._doc_id} not found"


class DesignDocUpdateNotAllowed(BaseServiceError):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_type = ErrorCode.DesignDocUpdateNotAllowed.name
    is_reliability_issue = False

    def __init__(self, doc_id: int, reason: str):
        self._doc_id = doc_id
        self._reason = reason
        super().__init__()

    def _get_msg(self) -> str:
        return f"Design document with ID {self._doc_id} cannot be updated: {self._reason}"
