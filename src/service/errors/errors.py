from abc import ABCMeta

from prime_service_kit import BaseServiceError
from prime_shared.common_types import AccountIdType, SourceIdType
from starlette import status

from service.errors.error_codes import ErrorCode


class AIPipelineRunError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.AIPipelineRunError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, job_id: int):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        self._job_id = job_id
        super().__init__()

    def _get_msg(self) -> str:
        return (
            f"Failed to run AI pipeline for "
            f"account: {self._account_id} source: {self._source_id} issue: {self._issue_id} job: {self._job_id}"
        )


class AIGenerationTimeoutError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.AIGenerationTimeoutError.name
    is_reliability_issue = False

    def __init__(self, issue_id: str):
        self._issue_id = issue_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Timeout waiting for AI generation for issue {self._issue_id}"


class SummaryGenerationTimeoutError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.SummaryGenerationTimeoutError.name
    is_reliability_issue = False

    def __init__(self, issue_id: str):
        self._issue_id = issue_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Timeout waiting for summary generation for issue {self._issue_id}"


class BaseSummaryError(BaseServiceError):
    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        super().__init__()


class SummaryNotFoundError(BaseSummaryError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.SummaryNotFoundError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str):
        super().__init__(account_id, source_id, issue_id)

    def _get_msg(self) -> str:
        return (
            f"Failed to find summary for issue {self._issue_id} "
            f"for account {self._account_id} and source {self._source_id}"
        )


class SummaryTreeGenerationError(BaseSummaryError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.SummaryTreeGenerationError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType):
        super().__init__(account_id, source_id, "")

    def _get_msg(self) -> str:
        return f"Failed to generate summary tree for account {self._account_id} and source {self._source_id}"


class SummaryLoadContextError(BaseSummaryError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.SummaryLoadContextError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str):
        super().__init__(account_id, source_id, issue_id)


class SummaryInsertError(BaseSummaryError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.SummaryInsertError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str):
        super().__init__(account_id, source_id, issue_id)

    def _get_msg(self) -> str:
        return (
            f"Failed to insert summary for issue {self._issue_id} "
            f"for account {self._account_id} and source {self._source_id}"
        )


class CaseHasNotBeenProcessedError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.CaseHasNotBeenProcessedError.name
    is_reliability_issue = False

    def __init__(self, case_id: int):
        self._case_id = case_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Case {self._case_id} has not been processed yet"


class DownloadFileError(BaseServiceError, metaclass=ABCMeta):
    http_status_code = status.HTTP_404_NOT_FOUND


class IssueManagerError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.IssueManagerError.name
    is_reliability_issue = False

    def __init__(self, account_id: str, issue_id: str):
        self._account_id = account_id
        self._issue_id = issue_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Issue manager error for {self._issue_id} and account {self._account_id}"


class IssueIdNotFoundInStorageError(DownloadFileError, IssueManagerError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.FileNotFoundError.name
    is_reliability_issue = False

    def _get_msg(self) -> str:
        return f"Failed to download issue {self._issue_id} for account {self._account_id}"


class IssueAnalysisNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.IssueAnalysisFoundError.name
    is_reliability_issue = False

    def __init__(self, issue_analysis_id: int):
        self._issue_analysis_id = issue_analysis_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to find issue analysis {self._issue_analysis_id}"


class IssueAnalysisNotFoundForIssueError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.IssueAnalysisFoundError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        super().__init__()

    def _get_msg(self) -> str:
        return (
            f"Failed to find issue analysis for issue {self._issue_id} for "
            f"account {self._account_id} and source {self._source_id}"
        )


class SourceNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.SourceNotFoundError.name
    is_reliability_issue = False

    def __init__(self, source_id: SourceIdType):
        self._source_id = source_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to find source {self._source_id}"


class UnsupportedQueryError(BaseServiceError):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_type = ErrorCode.UnsupportedQueryError.name
    is_reliability_issue = False

    def __init__(self, query: str):
        self._query = query
        super().__init__()

    def _get_msg(self) -> str:
        return f"Query '{self._query}' is not supported. check the query name or return type supported by api"


class ProductNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.ProductNotFoundError.name
    is_reliability_issue = False

    def __init__(self, product_id: int | None):
        self._product_id = product_id
        super().__init__()

    def _get_msg(self) -> str:
        if self._product_id:
            return f"Failed to find product {self._product_id}"
        return "Failed to find products"


class JiraFieldNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.JiraFieldNotFoundError.name
    is_reliability_issue = False

    def __init__(self, field_id: str):
        self._field_id = field_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to find Jira field {self._field_id}"


class JiraFieldNotParsableError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.JiraFieldNotParsableError.name
    is_reliability_issue = False

    def __init__(self, field_id: str):
        self._field_id = field_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to parse Jira field {self._field_id}"


class AccountConfigurationUpdateError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.AccountConfigurationUpdateError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType):
        self._account_id = account_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to update account configuration for account {self._account_id}"


class WriteBackError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.WriteBackError.name
    is_reliability_issue = False

    def __init__(self, reason: str):
        self._reason = reason
        super().__init__()

    def _get_msg(self) -> str:
        return f"Could not write back to Jira: {self._reason}"
