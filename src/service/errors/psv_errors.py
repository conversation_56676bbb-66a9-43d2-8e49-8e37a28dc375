from collections.abc import Iterable

from prime_service_kit import BaseServiceError
from prime_shared.common_types import AccountIdType, SourceIdType
from starlette import status

from .error_codes import ErrorCode


class PsvGeneralError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.CaseGeneralError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, msg: str | None = None):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        self._msg = msg
        super().__init__()

    def _get_msg(self) -> str:
        if self._msg:
            return self._msg
        return (
            f"Error occurred while processing psv for account: "
            f"'{self._account_id}', source: '{self._source_id}', issue: '{self._issue_id}'"
        )


class PsvNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.PsvNotFoundError.name
    is_reliability_issue = False

    def __init__(self, psv_id: int | Iterable[int]):
        self._psv_id = psv_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to find psv with id: {self._psv_id}"
