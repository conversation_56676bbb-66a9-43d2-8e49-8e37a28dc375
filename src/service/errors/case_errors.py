from prime_service_kit import BaseServiceError
from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import EmailStr
from starlette import status

from .error_codes import ErrorCode


class CaseIdNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.CaseGeneralError.name
    is_reliability_issue = False

    def __init__(self, case_id: int):
        self._case_id = case_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to find case {self._case_id}"


class UserNotFoundInJiraError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.CaseGeneralError.name
    is_reliability_issue = False

    def __init__(self, watcher_email: EmailStr):
        self.watcher_email = watcher_email
        super().__init__()

    def _get_msg(self) -> str:
        return f"Failed to find user at jira account {self.watcher_email}"


class CaseGeneralError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.CaseGeneralError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, msg: str | None = None):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        self._msg = msg
        super().__init__()

    def _get_msg(self) -> str:
        if self._msg:
            return self._msg
        return (
            f"Error occurred while processing case for account: "
            f"'{self._account_id}', source: '{self._source_id}', issue: '{self._issue_id}'"
        )


class AIGenerationError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.AIGenerationError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, error: str = ""):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        self._error = error
        super().__init__()

    def _get_msg(self) -> str:
        return (
            f"Failed to generate AI for account: '{self._account_id}', "
            f"source: '{self._source_id}', issue: '{self._issue_id}'. "
            f"Error: {self._error}"
        )


class SummaryGenerationError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.SummaryGenerationError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, error: str = ""):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        self._error = error
        super().__init__()

    def _get_msg(self) -> str:
        return (
            f"Failed to generate summary for account: '{self._account_id}', "
            f"source: '{self._source_id}', issue: '{self._issue_id}'. "
            f"Error: {self._error}"
        )


class PartialSummaryIsMissingError(SummaryGenerationError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.SummaryGenerationError.name
    is_reliability_issue = False

    def _get_msg(self) -> str:
        return (
            f"Failed to generate summary for account: '{self._account_id}', "
            f"source: '{self._source_id}', issue: '{self._issue_id}'. "
            f"Error: {self._error}"
        )


class SummaryParentIsMissingError(SummaryGenerationError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.SummaryGenerationError.name
    is_reliability_issue = False

    def __init__(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, parent_id: str, error: str = ""
    ) -> None:
        self._parent_id = parent_id
        super().__init__(account_id, source_id, issue_id, error)

    def _get_msg(self) -> str:
        return f"Parent is missing but required for issue {self._issue_id} with parent id {self._parent_id}"


class CaseNotFoundError(CaseGeneralError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.CaseFoundError.name
    is_reliability_issue = False

    def _get_msg(self) -> str:
        return (
            f"Failed to find case for account: '{self._account_id}', source: '{self._source_id}',"
            f" issue: '{self._issue_id}'"
        )


class CaseRecommendationNotFound(CaseGeneralError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.CaseRecommendationNotFound.name
    is_reliability_issue = False

    def __init__(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, recommendation_ids: list[int]
    ):
        self._recommendation_ids = recommendation_ids
        super().__init__(account_id, source_id, issue_id)

    def _get_msg(self) -> str:
        return (
            f"Failed to find recommendations {self._recommendation_ids} for "
            f"issue: {self._issue_id} and account: {self._account_id}"
        )


class CustomRecommendationError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.CaseRecommendationNotFound.name
    is_reliability_issue = False

    def _get_msg(self) -> str:
        return "Custom recommendation must have concern id and control id for update"


class ClassificationGenerationError(Exception):
    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, error: str):
        self.account_id = account_id
        self.source_id = source_id
        self.issue_id = issue_id
        self.error = error

    def get_full_msg(self) -> str:
        return (
            f"Failed to generate classification for account: '{self.account_id}', "
            f"source: '{self.source_id}', issue: '{self.issue_id}'. Error: {self.error}"
        )
