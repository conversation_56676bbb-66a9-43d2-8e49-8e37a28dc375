from prime_service_kit import BaseServiceError
from starlette import status

from .error_codes import ErrorCode


class JobNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.JobNotFoundError.name
    is_reliability_issue = False

    def __init__(self, job_id: int | None):
        self._job_id = job_id
        super().__init__()

    def _get_msg(self) -> str:
        if self._job_id:
            return f"Failed to find job {self._job_id}"
        return "Failed to find jobs"


class JobNotStartedError(Exception):
    def __init__(self) -> None:
        super().__init__("Job not started")


class JobFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(message)


class GenAiOutputError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Error in Gen AI PSV response: {message}")
