# pylint: disable=invalid-name
import enum


class ErrorCode(enum.Enum):
    # E0001 - E0999 Are reserved for general / common errors (see primesec_service_kit. error_codes_mapping)

    FileNotFoundError = "E1201-FileNotFoundError"
    SourceNotFoundError = "E1202-SourceNotFoundError"
    IssueManagerError = "E1203-IssueManagerError"
    CaseHasNotBeenProcessedError = "E1204-CaseHasNotBeenProcessedError"

    ClassificationGenerationError = "E1301-ClassificationGenerationError"
    QuestionnaireAnswerParsingError = "E1402-QuestionnaireAnswerParsingError"
    AIPipelineRunError = "E1403-AIPipelineRunError"

    JobNotFoundError = "E1501-JobNotFoundError"

    IssueAnalysisFoundError = "E1601-IssueAnalysisNotFoundError"
    JiraFieldNotFoundError = "E1602-JiraFieldNotFoundError"
    JiraFieldNotParsableError = "E1603-JiraFieldNotParsableError"
    IssueAnalysisNotFoundForCasesError = "E1604-IssueAnalysisNotFoundForCasesError"

    CacheRedisError = "E1701-CacheRedisError"

    CaseGeneralError = "E1800-CaseGeneralError"
    CaseFoundError = "E1801-CaseNotFoundError"
    CaseRecommendationNotFound = "E1802-CaseRecommendationNotFound"
    CustomRecommendationError = "E1802-CaseRecommendationNotFound"

    UnsupportedQueryError = "E1901-UnsupportedQueryError"

    ProductNotFoundError = "E2001-ProductNotFoundError"

    AccountConfigurationUpdateError = "E2101-AccountConfigurationUpdateError"

    WriteBackError = "E2201-WriteBackError"

    PsvNotFoundError = "E231-PsvNotFoundError"

    BulkUpdateError = "E2401-BulkUpdateError"

    SummaryGenerationError = "E2501-SummaryGenerationError"
    SummaryGenerationTimeoutError = "E2502-SummaryGenerationTimeoutError"
    SummaryInsertError = "E2503-SummaryInsertError"
    SummaryNotFoundError = "E2504-SummaryNotFoundError"
    SummaryPartialNotFoundError = "E2505-SummaryPartialNotFoundError"
    SummaryFinalNotFoundError = "E2506-SummaryFinalNotFoundError"
    SummaryTreeGenerationError = "E2507-SummaryTreeGenerationError"
    SummaryLoadContextError = "E2508-SummaryLoadContextError"
    SummaryParentIsMissingButRequiredError = "E2509-SummaryParentIsMissingButRequiredError"

    AIGenerationError = "E2510-AIGenerationError"
    AIGenerationTimeoutError = "E2511-AIGenerationTimeoutError"

    DesignDocDuplicateError = "E2601-DesignDocDuplicateError"
    DesignDocAttachContextError = "E2602-DesignDocAttachContextError"
    DesignDocNotFoundError = "E2603-DesignDocNotFoundError"
    DesignDocUpdateNotAllowed = "E2604-DesignDocUpdateNotAllowed"
