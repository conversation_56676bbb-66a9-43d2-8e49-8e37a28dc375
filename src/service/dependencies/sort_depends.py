import json
import logging
from typing import Annotated
from urllib.parse import unquote

from fastapi import Depends, Query, Request
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath

from service.models.filters_and_sort import SortField

from .get_provider_fields_info import get_provider_fields_info_from_request
from .redis_depends import redis_type

LOGGER = logging.getLogger(__name__)


async def parse_sort_string(
    request: Request,
    account_id: AccountArgumentType,
    redis_client: redis_type,
    source_id: SourceIdPath | None = None,
    sort_str_query: list[str] = Query(default_factory=list, alias="s", title="Sort", description="Sort string"),
) -> list[SortField]:
    if len(sort_str_query) == 0:
        return []
    account_provider_fields_info = await get_provider_fields_info_from_request(
        request, account_id, source_id=source_id, redis_client=redis_client
    )
    sorting: list[SortField] = []

    for s_str in sort_str_query:
        s_json = json.loads(unquote(s_str))
        sorting.append(
            SortField.model_validate(
                s_json,
                context=account_provider_fields_info,
            )
        )

    return sorting


sorting_type = Annotated[list[SortField], Depends(parse_sort_string)]
