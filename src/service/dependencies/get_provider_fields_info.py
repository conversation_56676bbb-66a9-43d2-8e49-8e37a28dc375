from typing import cast

from fastapi import Request
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath

from service.logic.issues.utils import get_provider_fields_info_stored
from service.models import ProviderFieldsInfoMapping

from .redis_depends import redis_type


async def get_provider_fields_info_from_request(
    request: Request,
    account_id: AccountArgumentType,
    redis_client: redis_type,
    source_id: SourceIdPath | None,
) -> ProviderFieldsInfoMapping:
    try:
        return cast(ProviderFieldsInfoMapping, request.state.account_provider_fields_info)
    except AttributeError:
        request.state.account_provider_fields_info = await get_provider_fields_info_stored(
            account_id, source_id, redis_client
        )
    return cast(ProviderFieldsInfoMapping, request.state.account_provider_fields_info)
