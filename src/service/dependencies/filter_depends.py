import json
import logging
from typing import Annotated
from urllib.parse import unquote

from fastapi import Depends, Query, Request
from prime_service_kit.shared_types import AccountArgumentType, SourceIdPath

from service.dependencies.get_provider_fields_info import get_provider_fields_info_from_request
from service.models.filters_and_sort import Filter

from .redis_depends import redis_type

LOGGER = logging.getLogger(__name__)


async def parse_filter_string(
    request: Request,
    account_id: AccountArgumentType,
    redis_client: redis_type,
    source_id: SourceIdPath | None = None,
    filter_str_query: list[str] = Query(
        default_factory=list,
        alias="f",
        title="Filter",
        description="Filter string. Format: field:value:op or fields.inner_field:value:op (for json type fields). "
        "op is (eq|ne)",
    ),
) -> list[Filter]:
    if len(filter_str_query) == 0:
        return []
    filters: list[Filter] = []
    account_provider_fields_info = await get_provider_fields_info_from_request(
        request, account_id, source_id=source_id, redis_client=redis_client
    )
    for f_str in filter_str_query:
        f_json = json.loads(unquote(f_str))
        filters.append(
            Filter.model_validate(
                f_json,
                context=account_provider_fields_info,
            )
        )
    return filters


filtering_type = Annotated[list[Filter], Depends(parse_filter_string)]
