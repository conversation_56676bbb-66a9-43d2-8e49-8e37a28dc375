# flake8: noqa: E501
from functools import cache

from prime_db_utils import DBSettings
from prime_jobs import PrimeJobsSettings
from prime_rabbitmq_utils import RabbitMQSettings
from prime_redis_utils import RedisSettings
from prime_service_kit import ServiceBaseSettings
from prime_service_kit.service_base_configuration import get_base_app_config
from pydantic_settings import SettingsConfigDict


class ServiceConfig(ServiceBaseSettings, DBSettings, RabbitMQSettings, RedisSettings, PrimeJobsSettings):
    # service discovery
    file_manager_service_url: str = "http://file-manager-service:8000"
    source_service_url: str = "http://source-service:8000"
    config_service_url: str = "http://config-service:8000"
    notification_service_url: str = "http://notification-service:8000"
    rat_logic_service_url: str = "http://rat-logic-service:8000"
    fetcher_service_url: str = "http://fetcher-service:8000"
    chatbot_service_url: str = "http://chatbot-service:8000"
    policy_service_url: str = "http://policy-service:8000"
    security_review_service_url: str = "http://security-review-service:8000"

    max_workers: int = 30
    max_field_options_to_return: int = 30

    export_limit: int = 300
    build_fields_data_sample_size: int = 10000
    redis_expire_time: int = 60 * 60 * 24  # 1 day

    disable_loggers: frozenset[str] = frozenset(
        list(get_base_app_config().disable_loggers) + ["botocore", "amqp", "datadog.dogstatsd", "celery"]
    )

    jobs_image_url: str

    celery_redis: RedisSettings | None = None

    model_config = SettingsConfigDict(env_nested_delimiter="__")

    job_factory_import_str: str = "service.k8s_jobs"


@cache
def get_config() -> ServiceConfig:
    return ServiceConfig()
