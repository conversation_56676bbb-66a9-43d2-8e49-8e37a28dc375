from __future__ import annotations

from typing import Optional

from prime_db_utils import AccountIdBaseTable, AuditHistoryBaseTable
from prime_shared.common_types import SourceIdType
from sqlmodel import Field

from service.models.psv import PsvStatus


class PsvTable(AccountIdBaseTable, AuditHistoryBaseTable, table=True):
    __tablename__ = "potential_security_violations"
    id: int = Field(primary_key=True)
    description: str = Field(nullable=False)
    type: str = Field(nullable=False, index=True)
    source_id: SourceIdType = Field(index=True, nullable=False)
    issue_id: str = Field(index=True, nullable=False)
    status: PsvStatus = Field(index=True)
    dismissed_reason: str | None = Field(default=None)
    research_package_version: Optional[str] = Field(default=None)
    issue_hash: str | None = Field(default=None)
    has_psv: bool = Field(index=True, nullable=False)
