from typing import Optional

from prime_db_utils import AccountIdBaseTable
from prime_gen_ai_service_client import QuestionsOutput
from sqlalchemy import Column
from sqlmodel import Field

from service.models import provider_field_types

from .utils.json_as_pydantic import JSONAsPydantic

ProviderFieldsColumnType = dict[str, provider_field_types]


class IssueSummaryAttributesTable(AccountIdBaseTable, table=True):
    __tablename__ = "issue_summary_attributes"

    id: Optional[int] = Field(primary_key=True)

    issue_hash: str = Field()
    ai_version: str = Field()

    summary: str | None = Field(nullable=True)
    questions: QuestionsOutput | None = Field(sa_column=Column(JSONAsPydantic(QuestionsOutput), nullable=True))
    short: str | None = Field(nullable=True)

    def has_summaries(self) -> bool:
        return self.summary is not None and self.short is not None and self.questions is not None
