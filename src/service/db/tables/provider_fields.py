# from typing import Optional
#
# from prime_db_utils import AccountIdBaseTable, AuditHistoryBaseTable
# from prime_shared.common_types import SourceIdType
# from sqlmodel import Field
#
# ProviderFieldsColumnType = dict[str, str]
#
#
# class ProviderFieldsTable(AccountIdBaseTable, AuditHistoryBaseTable, table=True):
#     __tablename__ = "cases"
#
#     id: Optional[int] = Field(default=None, primary_key=True)
#     source_id: SourceIdType = Field(index=True)
#     field_name: str = Field(index=True)
#     field_values: list[str] = Field(default_factory=list)
