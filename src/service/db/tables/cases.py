from typing import TYPE_CHECKING, Optional

from prime_db_utils import AccountIdBaseTable, AuditHistoryBaseTable
from prime_shared.common_types import SourceIdType
from sqlalchemy import VARCHAR, Index, UniqueConstraint
from sqlalchemy.dialects import postgresql
from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlmodel import Column, Field, Relationship

from service.db.tables.case_history import CaseHistoryTable
from service.db.tables.issues_analysis import IssueAnalysisTable
from service.db.tables.utils.json_as_pydantic import JSONAsPydantic
from service.models import (
    CaseStatus,
    Implementation,
    provider_field_types,
)
from service.models.cases import CaseComment

from .field_types import RiskScoreDBField

if TYPE_CHECKING:
    from service.db.tables.issue_summary_attributes import IssueSummaryAttributesTable
ProviderFieldsColumnType = dict[str, provider_field_types]


class CaseTable(AccountIdBaseTable, AuditHistoryBaseTable, AsyncAttrs, table=True):
    __tablename__ = "cases"

    id: int | None = Field(primary_key=True)
    source_id: SourceIdType = Field(index=True)
    issue_id: str = Field(index=True)
    parent_issue_id: str | None = Field(default=None, index=True)

    status: CaseStatus = Field(index=True, default=CaseStatus.OPEN)
    issue_analysis_id: int | None = Field(
        nullable=True,
        foreign_key="issues_analysis.id",
        index=True,
    )
    issue_analysis: IssueAnalysisTable | None = Relationship(
        cascade_delete=True,
        sa_relationship_kwargs={
            "lazy": "select",
            "cascade": "all, delete, delete-orphan",
            "foreign_keys": "[CaseTable.issue_analysis_id]",
            "single_parent": True,
            "passive_deletes": True,
        },
    )
    comments: list[CaseComment] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(CaseComment)))
    )

    recommendations: list[Implementation] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(Implementation)))
    )
    original_risk_score: int | None = RiskScoreDBField

    write_back_ref_id: str | None = Field(nullable=True)

    provider_fields: ProviderFieldsColumnType = Field(
        default_factory=dict, sa_column=Column(postgresql.JSONB, nullable=False, default=dict)
    )

    case_history: list[CaseHistoryTable] = Relationship(
        back_populates="case",
        sa_relationship_kwargs={
            "lazy": "selectin",
            "cascade": "all, delete-orphan",  # This enables cascading delete
            "passive_deletes": True,
        },
    )

    dismissed_reason: str | None = Field(default=None)
    labels: list[str] = Field(sa_column=Column(postgresql.ARRAY(VARCHAR()), server_default="{}", nullable=False))

    partial_id: int | None = Field(default=None, foreign_key="issue_summary_attributes.id", index=True)
    final_id: int | None = Field(default=None, foreign_key="issue_summary_attributes.id", index=True)
    partial: Optional["IssueSummaryAttributesTable"] = Relationship(
        sa_relationship_kwargs={
            "lazy": "select",
            "cascade": "all, delete, delete-orphan",
            "foreign_keys": "[CaseTable.partial_id]",
            "single_parent": True,
        },
    )
    final: Optional["IssueSummaryAttributesTable"] = Relationship(
        sa_relationship_kwargs={
            "lazy": "select",
            "cascade": "all, delete, delete-orphan",
            "foreign_keys": "[CaseTable.final_id]",
            "single_parent": True,
        },
    )

    progress_percentage: int = Field(default=0, le=100, ge=0)

    class Config:
        arbitrary_types_allowed = True

    __table_args__ = (
        UniqueConstraint("account_id", "source_id", "issue_id", name="account_source_issue_unique_constraint"),
        Index("idx_provider_fields", "provider_fields", postgresql_using="gin"),
        Index("idx_cases_parent_relationship", "account_id", "source_id", "parent_issue_id"),
    )


PROVIDERS_FIELDS_COLUMN_NAME = CaseTable.provider_fields.name  # type: ignore[attr-defined]
