from prime_db_utils import AccountIdBaseTable, AuditHistoryBaseTable
from sqlalchemy import Column, Index, String
from sqlalchemy.dialects import postgresql
from sqlmodel import Field

from service.models import IssueAnalysisConcern, SecurityControl
from service.models.issue_analysis import IssueLinks

from .field_types import (
    ConfidenceDBField,
    RiskFactorDBField,
    RiskScoreDBField,
    ScopeScoreDBField,
    SeverityScoreDBField,
)
from .utils.json_as_pydantic import JSONAsPydantic


class IssueAnalysisTable(AccountIdBaseTable, AuditHistoryBaseTable, table=True):
    __tablename__ = "issues_analysis"

    id: int | None = Field(default=None, primary_key=True)

    classification: bool | None = Field(index=True, default=None)

    confidentiality: int | None = RiskFactorDBField
    confidentiality_explanation: str | None = Field(default=None)
    integrity: int | None = RiskFactorDBField
    integrity_explanation: str | None = Field(default=None)
    availability: int | None = RiskFactorDBField
    availability_explanation: str | None = Field(default=None)

    # START: not in use right now
    third_party_management: int | None = RiskFactorDBField
    third_party_management_explanation: str | None = Field(default=None)
    compliance: int | None = RiskFactorDBField
    compliance_explanation: str | None = Field(default=None)
    severity: int | None = ScopeScoreDBField
    severity_explanation: str | None = Field(default=None)
    scope: int | None = SeverityScoreDBField
    scope_explanation: str | None = Field(default=None)
    # END

    confidence: int | None = ConfidenceDBField

    keywords: list[str] = Field(default_factory=list, sa_column=Column(postgresql.ARRAY(String())))

    short_assessment: str | None = Field(default=None)
    long_assessment: str | None = Field(default=None)

    concerns: list[IssueAnalysisConcern] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(IssueAnalysisConcern)))
    )

    controls: list[SecurityControl] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(SecurityControl)))
    )

    risk_score: int | None = RiskScoreDBField

    error: bool = Field(default=False)

    issue_hash: str | None = Field(default=None)

    research_package_version: str | None = Field(default=None)

    is_automated: bool | None = Field(index=True, default=True)
    is_security_enhancement: bool | None = Field(index=True, default=True)

    issue_links: list[IssueLinks] | None = Field(
        default=None, sa_column=Column(postgresql.ARRAY(JSONAsPydantic(IssueLinks)))
    )

    fire_summary: str | None = Field(default=None)

    __table_args__ = (
        Index(
            "idx_concerns",
            Column("concerns").cast(postgresql.ARRAY(postgresql.JSONB)),
            postgresql_using="gin",
            postgresql_ops={"concerns": "jsonb_path_ops"},
        ),
    )
