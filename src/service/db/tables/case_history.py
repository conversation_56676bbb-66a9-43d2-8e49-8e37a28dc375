from __future__ import annotations

from datetime import datetime
from typing import Any, Optional

from prime_db_utils import AccountIdBaseTable, utcnow
from prime_service_kit.fastapi_utils import get_prime_headers
from sqlalchemy import Column, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import DateTime, Field, Relationship

from service.models.cases import CaseAuditAction


class CaseHistoryTable(AccountIdBaseTable, table=True):
    __tablename__ = "case_history"

    id: Optional[int] = Field(default=None, primary_key=True)
    user: str = Field(index=True, default_factory=lambda: get_prime_headers().user_id or "unknown")
    audit_action: CaseAuditAction = Field(nullable=False, index=True)

    audit_action_args: dict[str, Any] | None = Field(sa_column=Column(JSONB, nullable=True))

    case_id: int = Field(
        sa_column=Column("case_id", ForeignKey("cases.id", ondelete="CASCADE"), nullable=True, index=True)
    )
    case: CaseTable = Relationship(back_populates="case_history")  # type: ignore[name-defined]  # noqa: F821

    created_at: Optional[datetime] = Field(
        sa_type=DateTime(timezone=True),  # type: ignore[call-overload]
        index=True,
        default_factory=utcnow,
    )
