from typing import Optional

from prime_db_utils import AccountIdBaseTable
from sqlalchemy import CheckConstraint, UniqueConstraint
from sqlmodel import Field

from service.models import NoWhitespaceStr

CONSTRAINT_NAME = "label_uix_account_id_name"


class LabelTable(AccountIdBaseTable, table=True):
    __tablename__ = "labels"

    id: Optional[int] = Field(default=None, primary_key=True)
    name: NoWhitespaceStr = Field()

    __table_args__ = (
        UniqueConstraint("account_id", "name", name=CONSTRAINT_NAME),
        CheckConstraint(r"name ~ '^[^\s]+$'", name="name_no_whitespace_min_length"),
    )
