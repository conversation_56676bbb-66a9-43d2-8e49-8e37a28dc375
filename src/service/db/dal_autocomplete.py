import logging
from collections.abc import Mapping
from typing import Any

from prime_shared.common_types import AccountIdType
from sqlalchemy import and_, exists, func, or_, text
from sqlalchemy.orm import aliased
from sqlmodel import select

from .dal_base import DalBase
from .tables import CaseTable, IssueAnalysisTable

LOGGER = logging.getLogger("dal_autocomplete")


class AutocompleteDal(DalBase):
    async def get_field_autocomplete_for_array(
        self, account_id: str, field: str, value: str, count: int = 10
    ) -> list[str]:
        subquery = (
            select(func.jsonb_array_elements_text(CaseTable.provider_fields[field]).label("value"))
            .join(IssueAnalysisTable, CaseTable.issue_analysis_id == IssueAnalysisTable.id)  # type: ignore[arg-type]
            .where(func.jsonb_typeof(CaseTable.provider_fields[field]) == "array")
            .where(CaseTable.account_id == account_id)  # noqa: E711
            .where(CaseTable.deleted_at == None)  # noqa: E711
            .where(IssueAnalysisTable.classification == True and IssueAnalysisTable.deleted_at == None)  # noqa: E712 E711
            .subquery()
        )
        query = (
            select(subquery.c.value)
            .distinct()
            .where(subquery.c.value.ilike(f"%{value}%"))
            .order_by(subquery.c.value)
            .limit(count)
        )
        return list(await self._session.exec(query))

    async def get_fields_options(
        self, account_id: AccountIdType, fields: list[str], limit: int, is_container: bool | None = None
    ) -> dict[str, list[str]]:
        LOGGER.info(
            "Getting field options for account=%s and fields %s (is_container=%s)", account_id, fields, is_container
        )
        if not fields:
            return {}
        fields_str = ",".join([f"'{value}'" for value in fields])

        query = """
            WITH keys AS (
                SELECT unnest(ARRAY[{fields_str}]) AS key
            ),
        """

        if is_container is not None:
            query += """
                container_cases AS (
                    SELECT DISTINCT cases.id
                    FROM cases
                    INNER JOIN cases AS child_cases ON
                        child_cases.account_id = cases.account_id
                        AND child_cases.source_id = cases.source_id
                        AND child_cases.parent_issue_id = cases.issue_id
                    WHERE cases.account_id = '{account_id}'
                    AND cases.deleted_at IS NULL
                ),
            """

        if is_container is False:
            query += """
                non_container_cases AS (
                    SELECT cases.id
                    FROM cases
                    WHERE cases.account_id = '{account_id}'
                    AND cases.deleted_at IS NULL
                    AND NOT EXISTS (
                        SELECT 1
                        FROM container_cases
                        WHERE container_cases.id = cases.id
                    )
                ),
            """

        container_join = ""
        if is_container is True:
            container_join = """
                INNER JOIN container_cases ON cases.id = container_cases.id
            """

        elif is_container is False:
            container_join = """
                INNER JOIN non_container_cases ON cases.id = non_container_cases.id
            """

        query += """
            scalar_values AS (
                SELECT k.key, TRIM(BOTH '\"' FROM kv.value::text) AS value
                FROM keys k
                LEFT JOIN (
                    SELECT key, value
                    FROM cases
                    CROSS JOIN jsonb_each(cases.provider_fields)
                    INNER JOIN issues_analysis ON cases.issue_analysis_id = issues_analysis.id
                    {container_join}
                    WHERE cases.account_id = '{account_id}'
                    AND cases.deleted_at IS NULL
                    AND issues_analysis.risk_score > 0
                ) kv
                ON k.key = kv.key
                WHERE jsonb_typeof(kv.value) != 'array'
            ),
            -- Extract array elements
            array_elements AS (
                SELECT k.key, TRIM(BOTH '\"' FROM elem) AS value
                FROM keys k
                LEFT JOIN (
                    SELECT key, jsonb_array_elements_text(value) AS elem
                    FROM cases
                    CROSS JOIN jsonb_each(cases.provider_fields)
                    INNER JOIN issues_analysis ON cases.issue_analysis_id = issues_analysis.id
                    {container_join}
                    WHERE jsonb_typeof(value) = 'array'
                    AND cases.account_id = '{account_id}'
                    AND cases.deleted_at IS NULL
                    AND issues_analysis.classification = TRUE
                ) a
                ON k.key = a.key
            ),
            -- Combine scalar values and array elements, then count and order
            prepared_values AS (
                SELECT key, value, ROW_NUMBER() OVER (PARTITION BY key ORDER BY COUNT(*) DESC) AS rn
                FROM (
                    SELECT * FROM scalar_values
                    UNION ALL
                    SELECT * FROM array_elements
                ) AS combined
                GROUP BY key, value
            )
            -- Filter and aggregate only top <limit> values per key
            SELECT key, ARRAY_AGG(value ORDER BY rn) AS ordered_values
            FROM prepared_values
            WHERE rn <= {limit}
            GROUP BY key;
        """

        query = query.format(
            fields_str=fields_str,
            limit=limit,
            account_id=account_id,
            is_container=is_container,
            container_join=container_join,
        )
        results = {result.key: result.ordered_values for result in await self._session.exec(text(query))}  # type: ignore[call-overload]
        return {key: [v for v in value if v and v != "[]"] for key, value in results.items()}

    async def get_field_autocomplete_for_title_or_issue_id(
        self, account_id: str, value: str, limit: int
    ) -> list[tuple[int, str, int, Mapping[str, Any], bool]]:
        child_cases = aliased(CaseTable, name="child_cases")

        container_subquery = exists(
            select(1).where(
                and_(
                    child_cases.account_id == account_id,  # type: ignore[arg-type]
                    child_cases.source_id == CaseTable.source_id,  # type: ignore[arg-type]
                    child_cases.parent_issue_id == CaseTable.issue_id,  # type: ignore[arg-type]
                )
            )
        )

        is_container = container_subquery.label("is_container")

        query = (
            select(  # type: ignore[call-overload]
                CaseTable.id,
                CaseTable.issue_id,
                CaseTable.source_id,
                CaseTable.provider_fields,
                is_container,
            )
            .join(
                IssueAnalysisTable,
                CaseTable.issue_analysis_id == IssueAnalysisTable.id,
            )
            .where(
                or_(
                    CaseTable.provider_fields["summary"].astext.ilike(f"%{value}%"),  # type: ignore[union-attr]
                    CaseTable.issue_id.ilike(f"%{value}%"),  # type: ignore[attr-defined]
                )
            )
            .where(CaseTable.account_id == account_id)
            .where(CaseTable.deleted_at == None)  # noqa: E711
            .where(
                or_(
                    container_subquery,
                    IssueAnalysisTable.classification.is_(True),  # type: ignore[union-attr]
                )
            )
            .order_by(CaseTable.issue_id)
            .limit(limit)
        )

        return list(await self._session.exec(query))

    async def get_field_autocomplete_for_string(
        self, account_id: str, field: str, value: str, count: int = 10
    ) -> list[str]:
        query = (
            select(func.distinct(CaseTable.provider_fields[field].astext))  # type: ignore[union-attr]
            .join(IssueAnalysisTable, CaseTable.issue_analysis_id == IssueAnalysisTable.id)  # type: ignore[arg-type]
            .where(CaseTable.provider_fields[field].astext.ilike(f"%{value}%"))  # type: ignore[union-attr]
            .where(CaseTable.account_id == account_id)
            .where(CaseTable.deleted_at == None)  # noqa: E711
            .where(IssueAnalysisTable.classification == True and IssueAnalysisTable.deleted_at == None)  # noqa: E712 E711
            .limit(count)
        )
        return list(await self._session.exec(query))
