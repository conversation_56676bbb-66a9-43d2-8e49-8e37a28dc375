from __future__ import annotations

import logging
from collections.abc import Sequence
from typing import TYPE_CHECKING, Any, cast

from prime_db_utils import build_pagination_query, utcnow
from prime_service_kit.fastapi_utils import pagination_args_type
from prime_shared.common_types import AccountIdType, SourceIdType
from sqlalchemy import func
from sqlmodel import asc
from sqlmodel.sql.expression import SelectOfScalar, select

from service.errors import PsvNotFoundError
from service.models.psv import BulkUpdatePsvDict, PsvStatus

from ..models.filters_and_sort import Filter, SortField
from .dal_base import DalBase
from .tables import CaseTable, PsvTable

LOGGER = logging.getLogger("psv_dal")

if TYPE_CHECKING:
    from service.k8s_jobs.security_violation_job.models import PsvJobLogicResult
    from service.logic.filters_and_sort import PSVFilters


def get_psv_filter() -> PSVFilters:
    from service.logic.filters_and_sort import PSVFilters

    return PSVFilters()


class PsvDAL(DalBase):
    async def add_psv(self, psv_result: PsvJobLogicResult) -> PsvTable:
        try:
            LOGGER.info("Adding new psv for source %s and issue %s", psv_result.source_id, psv_result.issue_id)
            if await self.delete_psv(psv_result.account_id, psv_result.source_id, psv_result.issue_id):
                LOGGER.info(
                    "Deleted already existing SV for source %s and issue %s",
                    psv_result.source_id,
                    psv_result.issue_id,
                )

            new_psv = PsvTable(
                account_id=psv_result.account_id,
                source_id=psv_result.source_id,
                issue_id=psv_result.issue_id,
                status=PsvStatus.OPEN,
                description=psv_result.description,
                type=psv_result.violation_type,
                research_package_version=psv_result.research_package_version,
                has_psv=psv_result.has_psv,
                issue_hash=psv_result.issue_hash,
            )
            self._session.add(new_psv)
            await self._session.commit()
            if new_psv.id is None:
                raise ValueError("Failed to add psv")

            await self._session.refresh(new_psv)
            return new_psv
        except Exception:  # pragma: no cover
            LOGGER.exception("Failed to add psv")
            raise

    async def get_psvs_by(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType | None = None,
        psv_filters: PSVFilters | None = None,
        pagination_args: pagination_args_type | None = None,
        sort_args: list[SortField] | None = None,
        entities: list[Any] | None = None,
    ) -> Sequence[Any]:
        psv_filters = psv_filters or get_psv_filter()
        query = await self.build_query(
            account_id=account_id,
            source_id=source_id,
            pagination_args=pagination_args,
            psv_filters=psv_filters,
            entities=entities,
            sort_args=sort_args,
        )
        return (await self._session.exec(query)).all()

    @classmethod
    async def build_query(  # noqa: PLR0913
        cls,
        *,
        account_id: AccountIdType,
        source_id: SourceIdType | None = None,
        psv_filters: PSVFilters,
        entities: list[Any] | None = None,
        pagination_args: pagination_args_type | None = None,
        sort_args: list[SortField] | None = None,
    ) -> SelectOfScalar[Any]:
        entities = entities or [PsvTable]
        query = select(*entities).where(PsvTable.account_id == account_id, PsvTable.deleted_at == None)  # noqa: E711
        if source_id:
            query = query.where(PsvTable.source_id == source_id)
        query = psv_filters.build_query(query)
        if pagination_args:
            query = build_pagination_query(PsvTable, pagination_args, query)
        if sort_args:
            query = cls._apply_sort(sort_args, query)
        query = query.order_by(asc(PsvTable.id))
        return query  # type: ignore[no-any-return]

    async def get_psv_or_raise(self, account_id: AccountIdType, psv_id: int) -> PsvTable:
        query = select(PsvTable).where(PsvTable.account_id == account_id, PsvTable.id == psv_id)
        psv = (await self._session.exec(query)).first()
        if psv is None:
            raise PsvNotFoundError(psv_id)
        return psv

    async def update_psv_status(
        self, account_id: AccountIdType, psv_id: int, status: PsvStatus, dismissed_reason: str | None = None
    ) -> PsvTable:
        psv = await self.get_psv_or_raise(account_id, psv_id)
        psv.status = status
        if status == PsvStatus.DISMISSED:
            psv.dismissed_reason = dismissed_reason
        else:
            psv.dismissed_reason = None
        await self._session.commit()
        return psv

    async def update_psv_status_bulk(
        self, account_id: AccountIdType, psvs_to_update: BulkUpdatePsvDict
    ) -> Sequence[PsvTable]:
        psv_filter = get_psv_filter()
        psv_filter.add_filter(Filter(field="id", value=[str(key) for key in psvs_to_update]))
        db_psvs = await self.get_psvs_by(account_id, psv_filters=psv_filter)
        if len(db_psvs) != len(psvs_to_update):
            missing = set(psvs_to_update) - {psv.id for psv in db_psvs}
            LOGGER.warning("Could not update psv(s) %s. They do not exist", missing)
        for db_psv in db_psvs:
            psv_to_update = psvs_to_update[db_psv.id]
            db_psv.status = psv_to_update.new_status
            if psv_to_update.new_status == PsvStatus.DISMISSED:
                db_psv.dismissed_reason = psv_to_update.dismissed_reason
            else:
                db_psv.dismissed_reason = None
        await self._session.commit()
        return db_psvs

    async def get_psv_by_issue_id(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
    ) -> PsvTable | None:
        psb_filters = get_psv_filter()
        psb_filters.add_filter(Filter(field="issue_id", value=issue_id))
        result = await self.get_psvs_by(account_id, source_id=source_id, psv_filters=psb_filters)
        if not result:
            return None
        return cast(PsvTable, result[0])

    async def delete_psv(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str) -> bool:
        LOGGER.info("Trying to soft deleting psv for source %s and issue %s", source_id, issue_id)
        psv = await self.get_psv_by_issue_id(account_id, source_id, issue_id)
        if psv is None:
            return False
        psv.deleted_at = utcnow()
        await self._session.commit()
        return True

    async def delete_psv_for_source(self, account_id: AccountIdType, source_id: SourceIdType) -> None:
        LOGGER.info("Soft deleting psv for source %s", source_id)
        psvs = await self.get_psvs_by(account_id=account_id, source_id=source_id)
        for psv in psvs:
            psv.deleted_at = utcnow()
        await self._session.commit()

    async def get_psv_count_by_types(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType | None = None,
        psv_filters: PSVFilters | None = None,
    ) -> dict[str, int]:
        psv_filters = psv_filters or get_psv_filter()
        if source_id:
            psv_filters.add_filter(Filter(field="source_id", value=str(source_id)))
        psv_sub_query = await self.build_query(account_id=account_id, source_id=source_id, psv_filters=psv_filters)
        query = select(PsvTable.type, func.count()).group_by(PsvTable.type).where(psv_sub_query.whereclause)  # type: ignore[arg-type]
        return dict((await self._session.exec(query)).all())

    async def get_psv_count(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType | None = None,
        psv_filters: PSVFilters | None = None,
    ) -> int:
        psv_filters = psv_filters or get_psv_filter()
        if source_id:
            psv_filters.add_filter(Filter(field="source_id", value=str(source_id)))
        psv_sub_query = await self.build_query(account_id=account_id, source_id=source_id, psv_filters=psv_filters)
        count = await self._session.scalar(select(func.count()).where().select_from(psv_sub_query.subquery()))
        return count or 0

    @classmethod
    def _apply_sort(cls, sort_args: list[SortField], query: SelectOfScalar[PsvTable]) -> SelectOfScalar[PsvTable]:
        def _update_sort(_sort: SortField) -> None:
            if sort.field == "title":
                sort.field = "provider_fields"
                sort.inner_field = "summary"
            elif sort.field == "detection_date":
                sort.field = "created_at"

        for sort in sort_args:
            _update_sort(sort)
            try:
                table = next(table for table in [PsvTable, CaseTable] if hasattr(table, sort.name))
                model_field = getattr(table, sort.name)
            except StopIteration:
                LOGGER.warning("Ignoring unknown sort field %s", sort.name)
                continue
            query = sort.get_sort_query(query, model_field)  # type: ignore[assignment]
        return query
