from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Any, cast

from prime_db_utils import build_pagination_query
from prime_gen_ai_service_client import QuestionsOutput
from prime_service_kit.fastapi_utils import pagination_args_type
from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic.dataclasses import dataclass
from sqlalchemy import update
from sqlalchemy.orm import aliased
from sqlmodel.sql.expression import Select, SelectOfScalar, desc, select

from service.db.tables.cases import CaseTable
from service.db.tables.issue_summary_attributes import IssueSummaryAttributesTable

from .dal_base import DalBase

if TYPE_CHECKING:
    from service.k8s_jobs.base_job_logic import SummaryOutput, SummaryOutputData
    from service.logic.filters_and_sort import CaseFilters

LOGGER = logging.getLogger("issue_summary_dal")


def get_empty_case_filter() -> CaseFilters:
    from service.logic.filters_and_sort import CaseFilters

    return CaseFilters([])


@dataclass(slots=True)
class IssueDBSummaryInfo:
    partial_hash: str | None
    partial_version: str | None
    issue_id: str


@dataclass(slots=True)
class IssueDBSummaryData:
    summary: str
    questions: QuestionsOutput
    short: str


class IssueSummaryDAL(DalBase):
    async def _create_summary_attributes(
        self, account_id: AccountIdType, issue_hash: str, ai_version: str, summary_data: SummaryOutputData | None
    ) -> IssueSummaryAttributesTable:
        new_summary = IssueSummaryAttributesTable(
            account_id=account_id,
            summary=summary_data.questions_summary if summary_data else None,
            short=summary_data.short_summary if summary_data else None,
            questions=summary_data.questions if summary_data else None,
            issue_hash=issue_hash,
            ai_version=ai_version,
        )
        self._session.add(new_summary)
        await self._session.commit()
        return new_summary

    async def _update_summary_attributes(
        self,
        summary_id: int,
        account_id: AccountIdType,
        issue_hash: str,
        ai_version: str,
        summary_data: SummaryOutputData | None,
    ) -> None:
        query = (
            update(IssueSummaryAttributesTable)
            .where(IssueSummaryAttributesTable.account_id == account_id)  # type: ignore[arg-type]
            .where(IssueSummaryAttributesTable.id == summary_id)  # type: ignore[arg-type]
            .values(
                issue_hash=issue_hash,
                ai_version=ai_version,
                summary=summary_data.questions_summary if summary_data else None,
                short=summary_data.short_summary if summary_data else None,
                questions=summary_data.questions if summary_data else None,
            )
        )
        await self._session.exec(query)  # type: ignore[call-overload]

    async def upsert_summary(self, case: CaseTable, summary: SummaryOutput) -> None:
        if not case.partial_id:
            db_summary = await self._create_summary_attributes(
                case.account_id,
                issue_hash=summary.issue_hash,
                ai_version=summary.ai_version,
                summary_data=summary.data,
            )
            case.partial_id = db_summary.id
            await self._session.commit()
        else:
            await self._update_summary_attributes(
                case.partial_id,
                case.account_id,
                issue_hash=summary.issue_hash,
                ai_version=summary.ai_version,
                summary_data=summary.data,
            )

    async def get_summary(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
    ) -> IssueSummaryAttributesTable:
        query = (
            select(IssueSummaryAttributesTable)
            .join(
                CaseTable,
                (CaseTable.partial_id == IssueSummaryAttributesTable.id)  # type: ignore[arg-type]
                & (CaseTable.account_id == account_id)
                & (CaseTable.source_id == source_id)
                & (CaseTable.issue_id == issue_id),
            )
            .where(IssueSummaryAttributesTable.account_id == account_id)
        )
        return (await self._session.exec(query)).one()

    async def get_summaries_info(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        pagination_args: pagination_args_type | None = None,
    ) -> list[IssueDBSummaryInfo]:
        partial_attrs = aliased(IssueSummaryAttributesTable)
        select_cols: list[Any] = [
            partial_attrs.issue_hash,
            partial_attrs.ai_version,
            CaseTable.issue_id,
        ]
        query: Select[Any] | SelectOfScalar[Any] = (
            select(*select_cols)
            .outerjoin(partial_attrs, partial_attrs.id == CaseTable.partial_id)
            .where(
                CaseTable.account_id == account_id,
                CaseTable.source_id == source_id,
                CaseTable.deleted_at == None,  # noqa: E711
            )
        )
        query = query.order_by(desc(CaseTable.id))
        if pagination_args:
            query = build_pagination_query(CaseTable, pagination_args, query)  # type: ignore[assignment]
        results = (await self._session.exec(query)).all()
        return [IssueDBSummaryInfo(*result) for result in results]

    async def get_summaries(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_ids: list[str]
    ) -> dict[str, IssueDBSummaryData]:
        cases = cast(
            list[CaseTable],
            await self._service_dal.cases_dal.get_cases_by(
                account_id=account_id, source_id=source_id, issues=issue_ids, with_summary=True
            ),
        )
        results = {}
        for case in cases:
            if case.partial is None or not case.partial.has_summaries():
                continue
            results[case.issue_id] = IssueDBSummaryData(
                summary=case.partial.summary,  # type: ignore[arg-type]
                questions=case.partial.questions,  # type: ignore[arg-type]
                short=case.partial.short,  # type: ignore[arg-type]
            )
        return results
