import logging
from collections.abc import Sequence

from prime_shared.common_types import AccountIdType
from sqlalchemy.dialects.postgresql import insert
from sqlmodel.sql.expression import desc, select

from service.db.tables.labels import CONSTRAINT_NAME, LabelTable

from .dal_base import DalBase

LOGGER = logging.getLogger("label_dal")


class LabelsDAL(DalBase):
    async def add_labels(self, account_id: AccountIdType, labels: list[str]) -> None:
        LOGGER.info("Adding labels [%s] for account [%s]", labels, account_id)
        try:
            values_to_insert = [{"account_id": account_id, "name": label} for label in labels]
            insert_stmt = insert(LabelTable).values(values_to_insert).on_conflict_do_nothing(CONSTRAINT_NAME)
            await self._session.exec(insert_stmt)  # type: ignore[call-overload]
            await self._session.commit()
        except Exception:
            LOGGER.exception("Failed to add label")
            raise

    async def get_labels(self, account_id: AccountIdType) -> Sequence[LabelTable]:
        query = select(LabelTable).where(LabelTable.account_id == account_id).order_by(desc(LabelTable.name))
        return (await self._session.exec(query)).all()
