from __future__ import annotations

import contextlib
import logging
from collections.abc import Async<PERSON>enerator
from typing import TYPE_CHECKING, Annotated, Any

from fastapi import Depends
from prime_db_utils import get_async_session
from prime_jobs import SchedulerDAL
from prime_service_kit.fastapi_utils import pagination_args_type
from prime_shared.common_types import AccountIdType, SourceIdType
from sqlmodel import delete, select

from service.config import get_config
from service.db.dal_llm_context import LlmContextDAL
from service.models import CaseAuditAction
from service.models.filters_and_sort import SortField

from .dal_autocomplete import AutocompleteDal
from .dal_bulk_ops import BulkOpsDAL
from .dal_cases import CasesDAL, CaseTable, get_case_filter
from .dal_issue_summary import IssueSummaryDAL
from .dal_issues_analysis import IssuesAnalysisDAL
from .dal_labels import LabelsDAL
from .dal_psv import PsvDAL
from .tables import CaseHistoryTable, IssueAnalysisTable

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession

    from service.k8s_jobs.classification_job.classification_job_logic import ClassificationJobLogicResult
    from service.logic.filters_and_sort import CaseFilters

LOGGER = logging.getLogger("service_dal")


class ServiceDAL:
    def __init__(self, session: AsyncSession):
        self._session = session
        self.cases_dal = CasesDAL(self)
        self.issues_analysis_dal = IssuesAnalysisDAL(self)
        self.scheduler_dal: SchedulerDAL = SchedulerDAL(self.session)
        self.psv_dal: PsvDAL = PsvDAL(self)
        self.labels_dal = LabelsDAL(self)
        self.dal_bulk_ops = BulkOpsDAL(self)
        self.issue_summary_dal = IssueSummaryDAL(self)
        self.autocomplete_dal = AutocompleteDal(self)
        self.llm_context_dal = LlmContextDAL(self)

    @property
    def session(self) -> AsyncSession:
        return self._session

    async def get_case_view(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str) -> CaseTable:
        case = await self.cases_dal.get_case(
            account_id, source_id, issue_id, with_recommendations=True, with_summary=True
        )
        history_record = CaseHistoryTable(account_id=account_id, audit_action=CaseAuditAction.view)
        case.case_history.append(history_record)
        return case

    async def get_case_container_view(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
    ) -> CaseTable:
        return await self.cases_dal.get_case(account_id, source_id, issue_id, with_analysis=True, with_summary=True)

    async def add_classification_result_to_case(
        self, account_id: AccountIdType, result: ClassificationJobLogicResult
    ) -> IssueAnalysisTable:
        issue_analysis = await self.issues_analysis_dal._add_classification_result(account_id, result)
        case_id = result.case_id
        old_analysis_id = (
            await self._session.exec(select(CaseTable.issue_analysis_id).where(CaseTable.id == case_id))
        ).one_or_none()
        await self.cases_dal.update_case_by_id(
            result.case_id, issue_analysis_id=issue_analysis.id, recommendations=result.recommendations
        )
        if old_analysis_id:
            await self._session.exec(delete(IssueAnalysisTable).where(IssueAnalysisTable.id == old_analysis_id))  # type: ignore[call-overload, arg-type]
        return issue_analysis

    async def update_case_summary(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, result: ClassificationJobLogicResult
    ) -> None:
        if not result.summary:
            return
        case = await self.cases_dal.get_case(
            account_id=account_id, source_id=source_id, issue_id=issue_id, with_summary=True
        )
        await self.issue_summary_dal.upsert_summary(case=case, summary=result.summary)

    async def get_workroom_cases_by(
        self,
        account_id: AccountIdType,
        case_filters: CaseFilters | None = None,
        pagination_args: pagination_args_type | None = None,
        sort_args: list[SortField] | None = None,
    ) -> list[tuple[CaseTable, IssueAnalysisTable | None]]:
        ignore_columns = [
            CaseTable.recommendations,
            IssueAnalysisTable.controls,
            IssueAnalysisTable.keywords,
            IssueAnalysisTable.short_assessment,
            IssueAnalysisTable.long_assessment,
        ]
        case_columns = [column for column in CaseTable.__table__.columns if column not in ignore_columns]  # type: ignore[attr-defined]
        issue_columns = [column for column in IssueAnalysisTable.__table__.columns if column not in ignore_columns]  # type: ignore[attr-defined]
        return await self._get_case_with_analysis(
            account_id, None, case_columns + issue_columns, case_filters, pagination_args, sort_args
        )

    async def _get_case_with_analysis(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType | None,
        entities: list[Any],
        case_filters: CaseFilters | None = None,
        pagination_args: pagination_args_type | None = None,
        sort_args: list[SortField] | None = None,
    ) -> list[tuple[CaseTable, IssueAnalysisTable | None]]:
        case_filters = case_filters or get_case_filter()
        case_filters.join_default_tables = True
        select_columns = []
        for entity in entities:
            select_columns.extend(list(entity.__table__.columns) if hasattr(entity, "__table__") else [entity])
        case_columns = [column for column in CaseTable.__table__.columns if column in select_columns]  # type: ignore[attr-defined]
        issue_columns = [column for column in IssueAnalysisTable.__table__.columns if column in select_columns]  # type: ignore[attr-defined]
        results = await self.cases_dal.get_cases_by(
            account_id, source_id, case_filters, select_columns, pagination_args=pagination_args, sort_args=sort_args
        )
        cases_with_analysis: list[tuple[CaseTable, IssueAnalysisTable | None]] = []
        for row in results:
            data_tuples = zip(case_columns, row[: len(case_columns)], strict=False)
            case = CaseTable(**{column.name: value for column, value in data_tuples})
            issue_analysis_data = row[len(case_columns) :]
            if any(issue_analysis_data):
                data_tuples = zip(issue_columns, issue_analysis_data, strict=False)
                issue_analysis = IssueAnalysisTable(**{column.name: value for column, value in data_tuples})
            else:
                issue_analysis = None
            cases_with_analysis.append((case, issue_analysis))
        return cases_with_analysis

    async def set_labels(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, labels: list[str]
    ) -> CaseTable:
        count_found = set()
        unique_labels = []
        for label in labels:
            if label not in count_found:
                unique_labels.append(label)
                count_found.add(label)
        case = await self.cases_dal.get_case(account_id, source_id, issue_id)
        if unique_labels:
            await self.labels_dal.add_labels(account_id, unique_labels)
        case.labels = unique_labels
        await self.session.commit()
        return case


async def get_service_dal() -> AsyncGenerator[ServiceDAL]:
    LOGGER.info("Creating service DAL")
    async with get_async_session(get_config()) as async_session:
        yield ServiceDAL(async_session)
        await async_session.commit()


@contextlib.asynccontextmanager
async def get_service_dal_context() -> AsyncGenerator[ServiceDAL]:
    LOGGER.info("Creating service DAL")
    async with get_async_session(get_config()) as async_session:
        yield ServiceDAL(async_session)
        await async_session.commit()


service_dal_depends = Annotated[ServiceDAL, Depends(get_service_dal, use_cache=False)]
