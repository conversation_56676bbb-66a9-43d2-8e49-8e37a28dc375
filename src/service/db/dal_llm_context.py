from __future__ import annotations

import logging
from collections.abc import Sequence

from prime_db_utils import build_pagination_query
from prime_service_kit.fastapi_utils import pagination_args_type
from prime_shared.common_types import AccountIdType
from sqlalchemy import func
from sqlalchemy.orm import joinedload, selectinload
from sqlmodel import select
from sqlmodel.sql.expression import Select, SelectOfScalar

from service.db.tables.issues_analysis import IssueAnalysisTable
from service.models.filters_and_sort.sorting import SortDirection, SortField

from .dal_base import DalBase
from .tables import CaseTable

LOGGER = logging.getLogger("dal_cases")


class LlmContextDAL(DalBase):
    @staticmethod
    async def _get_base_query(
        account_id: AccountIdType,
        issue_ids: list[str] | None,
        pagination_args: pagination_args_type | None = None,
    ) -> SelectOfScalar[CaseTable] | Select[CaseTable]:
        sort_args: list[SortField] = [
            SortField(field="classification", direction=SortDirection.DESC),
            SortField(field="provider_fields", inner_field="updated", direction=SortDirection.DESC),
        ]

        query: SelectOfScalar[CaseTable] | Select[CaseTable] = (
            select(CaseTable)
            .where(CaseTable.account_id == account_id)
            .outerjoin(IssueAnalysisTable, CaseTable.issue_analysis_id == IssueAnalysisTable.id)  # type: ignore[arg-type]
            .options(
                selectinload(CaseTable.partial),  # type: ignore[arg-type]
                joinedload(CaseTable.issue_analysis),  # type: ignore[arg-type]
            )
        )

        if issue_ids:
            query = query.where(CaseTable.issue_id.in_(issue_ids))  # type: ignore[attr-defined]
        for sort in sort_args:
            try:
                table = next(table for table in [CaseTable, IssueAnalysisTable] if hasattr(table, sort.name))
                model_field = getattr(table, sort.name)
            except StopIteration:
                LOGGER.warning("Ignoring unknown sort field %s", sort.name)
                continue
            query = sort.get_sort_query(query, model_field)

        if pagination_args:
            query = build_pagination_query(CaseTable, pagination_args, query)  # type: ignore[assignment]
        return query

    async def get_cases(
        self,
        account_id: AccountIdType,
        issue_ids: list[str] | None,
        pagination_args: pagination_args_type | None = None,
    ) -> Sequence[CaseTable]:
        query = await self._get_base_query(account_id, issue_ids, pagination_args)
        return (await self._session.exec(query)).all()

    async def get_total_records(self, account_id: AccountIdType, issue_ids: list[str] | None) -> int:
        query = await self._get_base_query(account_id, issue_ids)
        count = await self._session.scalar(select(func.count()).where().select_from(query.subquery()))
        return count or 0
