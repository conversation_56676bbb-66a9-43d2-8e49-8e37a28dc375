from __future__ import annotations

import logging
from collections.abc import Sequence
from datetime import UTC, datetime
from typing import TYPE_CHECKING, Annotated, Any, Literal, cast

from prime_db_utils import build_pagination_query, utcnow
from prime_service_kit.fastapi_utils import pagination_args_type
from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import BaseModel, Field
from sqlalchemy import CTE, ScalarResult, literal_column
from sqlalchemy.dialects.postgresql import insert as pg_insert
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import defer, selectinload
from sqlalchemy.orm.attributes import flag_modified
from sqlmodel import asc, func, select, update
from sqlmodel.sql.expression import Select, SelectOfScalar, and_

from service.errors import CaseHasNotBeenProcessedError, CaseIdNotFoundError, CaseNotFoundError
from service.models import (
    CaseAuditAction,
    CaseAuditOverrideRiskCategoryArgs,
    CaseAuditUpdateStatusArgs,
    CaseComment,
    CaseStatus,
    CreateCaseComment,
    Implementation,
)
from service.models.filters_and_sort import Filter, SortField

from .dal_base import DalBase
from .tables import CaseTable, IssueAnalysisTable, ProviderFieldsColumnType
from .tables.case_history import CaseHistoryTable

if TYPE_CHECKING:
    from service.logic.filters_and_sort import CaseFilters

LOGGER = logging.getLogger("dal_cases")
UPSERT_BATCH_SIZE = 500
IssueStructure = Annotated[
    Sequence[tuple[str, str, datetime | None]] | Sequence[tuple[None, str, datetime | None]],
    "parent_id, issue_id, updated_at",
]

direction_t = Literal["parents", "children"]


def get_case_filter() -> CaseFilters:
    from service.logic.filters_and_sort import CaseFilters

    return CaseFilters()


class UpsertCasesBulkArgs(BaseModel):
    issue_id: str
    fields: dict[str, Any]


class CasesDAL(DalBase):
    def _get_last_recommendation_id(self, case: CaseTable) -> int:
        if not case.recommendations:
            return 0
        return max(rec.id for rec in case.recommendations)

    async def add_new_recommendations(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, implementations: list[Implementation]
    ) -> CaseTable:
        LOGGER.info(
            "Adding new recommendations for account=%s source_id=%s issue_id=%s",
            account_id,
            source_id,
            issue_id,
        )

        case = await self.get_case(account_id, source_id, issue_id, with_recommendations=True)

        if not case.recommendations:
            case.recommendations = []

        next_recommendation_id = self._get_last_recommendation_id(case) + 1

        # Update new ids for the new recommendations
        for implementation in implementations:
            implementation.id = next_recommendation_id
            next_recommendation_id += 1

        case.recommendations.extend(implementations)

        flag_modified(case, "recommendations")
        self._session.add(case)
        await self._session.commit()
        await self._session.refresh(case)

        return case

    async def get_case_by_id(
        self,
        account_id: AccountIdType,
        id_: int,
        with_recommendations: bool = False,
        with_summary: bool = False,
        with_analysis: bool = False,
    ) -> CaseTable:
        LOGGER.info("Getting case by id %s", id_)
        try:
            query = select(CaseTable).where(CaseTable.account_id == account_id, CaseTable.id == id_)
            if not with_recommendations:
                query = query.options(defer(CaseTable.recommendations))  # type: ignore[arg-type]
            if with_summary:
                query = query.options(
                    selectinload(CaseTable.partial),  # type: ignore[arg-type]
                )
            if with_analysis:
                query = query.options(selectinload(CaseTable.issue_analysis))  # type: ignore[arg-type]
            result: CaseTable = (await self._session.exec(query)).one()
            return result
        except NoResultFound as e:
            raise CaseIdNotFoundError(id_) from e

    async def get_case(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        issue_id: str,
        with_recommendations: bool = False,
        with_summary: bool = False,
        with_analysis: bool = False,
    ) -> CaseTable:
        LOGGER.info("Getting case for account=%s source_id=%s issue_id=%s", account_id, source_id, issue_id)
        result = await self.get_cases_by(
            account_id,
            source_id,
            issues=[issue_id],
            with_recommendations=with_recommendations,
            with_summary=with_summary,
            with_analysis=with_analysis,
        )
        if not result:
            raise CaseNotFoundError(account_id, source_id, issue_id)
        return result[0]  # type: ignore[no-any-return]

    async def get_cases_by(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        source_id: SourceIdType | None = None,
        case_filters: CaseFilters | None = None,
        entities: list[Any] | None = None,
        issues: list[str] | None = None,
        pagination_args: pagination_args_type | None = None,
        sort_args: list[SortField] | None = None,
        with_summary: bool = False,
        with_recommendations: bool = False,
        with_analysis: bool = False,
    ) -> Sequence[Any]:
        LOGGER.info(
            "Getting cases for account=%s source_id=%s by %s and entities %s",
            account_id,
            source_id,
            case_filters,
            entities,
        )
        case_filters = case_filters or get_case_filter()
        if issues:
            case_filters.add_filter(Filter(field="issue_id_exact", value=issues))
        query = self.build_query(
            account_id=account_id,
            case_filters=case_filters,
            source_id=source_id,
            entities=entities,
            pagination_args=pagination_args,
            sort_args=sort_args,
            with_summary=with_summary,
            with_recommendations=with_recommendations,
            with_analysis=with_analysis,
        )
        res = (await self._session.exec(query)).all()
        return res

    async def get_issues_structure(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        parent_id: str | None = None,
        with_parents: bool = False,
    ) -> IssueStructure:
        entities = [CaseTable.parent_issue_id, CaseTable.issue_id, CaseTable.updated_at]
        case_filters = get_case_filter()
        parents_seq: IssueStructure = []
        if parent_id:
            case_filters = case_filters.parent_id(parent_id)
            if with_parents:
                # parent_id is the issue_id of the parent issue, without parent with_parents irrelevant
                # because all the tree is returned
                # Horizontal tree structure example with the method run on parent_id=p3 and with_parents=True:
                # p1 ──→ p2 ──→ p3 ──┬──→ p5
                #                    ├──→ p6 ──┬──→ p8
                #                    └──→ p7   └──→ p9
                # Horizontal tree structure example with the method run on parent_id=p3 and with_parents=False:
                #  p3 ──┬──→ p5
                #       ├──→ p6 ──┬──→ p8
                #       └──→ p7   └──→ p9
                cte = self.get_parents_hierarchy_cte(parent_id)
                parents_query: SelectOfScalar[Any] | Select[Any] = self.build_query(
                    account_id=account_id, source_id=source_id, entities=entities, case_filters=get_case_filter()
                )
                query = parents_query.join(cte, CaseTable.id == cte.c.id)  # type: ignore[arg-type]
                parents_seq = (await self._session.exec(query)).all()
        cases_seq = await self.get_cases_by(account_id, source_id, case_filters=case_filters, entities=entities)
        return list(parents_seq) + list(cases_seq)

    async def get_issue_ids_by(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        case_filters: CaseFilters,
    ) -> Sequence[str]:
        LOGGER.info("Getting cases keys for account=%s source_id=%s", account_id, source_id)
        return await self.get_cases_by(account_id, source_id, case_filters=case_filters, entities=[CaseTable.issue_id])

    async def get_cases_for_classification(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        parent_id: str | None = None,
    ) -> Sequence[tuple[str, int, datetime | None, str | None, str | None]]:
        LOGGER.info(
            "Getting cases for classification: account=%s source_id=%s parent_id=%s", account_id, source_id, parent_id
        )
        case_filters = get_case_filter().not_container()
        if parent_id:
            case_filters = case_filters.parent_id(parent_id)
        entitles = [
            CaseTable.issue_id,
            CaseTable.id,
            IssueAnalysisTable.updated_at,
            IssueAnalysisTable.issue_hash,
            IssueAnalysisTable.research_package_version,
        ]
        case_filters.join_default_tables = True
        case_filters.add_to_dynamic_join(IssueAnalysisTable, [], is_outer=True)
        return await self.get_cases_by(account_id, source_id, case_filters=case_filters, entities=entitles)

    def build_query(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        case_filters: CaseFilters,
        pagination_args: pagination_args_type | None = None,
        sort_args: list[SortField] | None = None,
        source_id: SourceIdType | None = None,
        entities: list[Any] | None = None,
        with_summary: bool = False,
        with_recommendations: bool = False,
        with_analysis: bool = False,
    ) -> SelectOfScalar[Any] | Select[Any]:
        if source_id:
            case_filters.add_filter(Filter(field="source_id", value=str(source_id)))
        entities = entities or [CaseTable]
        query: SelectOfScalar[Any] | Select[Any] = select(*entities)
        if with_summary:
            query = query.options(
                selectinload(CaseTable.partial),  # type: ignore[arg-type]
            )
        if not with_recommendations and any(column["type"] == CaseTable for column in query.column_descriptions):
            query = query.options(defer(CaseTable.recommendations))  # type: ignore[arg-type]
        if with_analysis:
            query = query.options(selectinload(CaseTable.issue_analysis))  # type: ignore[arg-type]
        query = query.where(CaseTable.account_id == account_id).where(CaseTable.deleted_at == None)  # noqa: E711
        query = case_filters.build_query(query)
        if pagination_args:
            query = build_pagination_query(CaseTable, pagination_args, query)  # type: ignore[assignment]
        if sort_args:
            query = self._apply_sort(sort_args, query)

        return query.order_by(asc(CaseTable.id))

    @classmethod
    def _apply_sort(
        cls, sort_args: list[SortField], query: SelectOfScalar[CaseTable] | Select[CaseTable]
    ) -> SelectOfScalar[CaseTable] | Select[CaseTable]:
        def _update_sort(_sort: SortField) -> None:
            column_aliases = {
                "risk_score_category": IssueAnalysisTable.risk_score.name,  # type: ignore[union-attr]
                "confidentiality_level": IssueAnalysisTable.confidentiality.name,  # type: ignore[union-attr]
                "integrity_level": IssueAnalysisTable.integrity.name,  # type: ignore[union-attr]
                "availability_level": IssueAnalysisTable.availability.name,  # type: ignore[union-attr]
                "confidence_level": IssueAnalysisTable.confidence.name,  # type: ignore[union-attr]
                "parents": CaseTable.parent_issue_id.name,  # type: ignore[union-attr]
            }
            if sort.name in column_aliases:
                sort.field = column_aliases[sort.name]
            elif sort.field == "title":
                sort.field = "provider_fields"
                sort.inner_field = "summary"

        for sort in sort_args:
            _update_sort(sort)
            try:
                table = next(table for table in [CaseTable, IssueAnalysisTable] if hasattr(table, sort.name))
                model_field = getattr(table, sort.name)
            except StopIteration:
                LOGGER.warning("Ignoring unknown sort field %s", sort.name)
                continue
            query = sort.get_sort_query(query, model_field)
        return query

    async def get_cases_count_by(
        self,
        account_id: AccountIdType,
        case_filters: CaseFilters,
    ) -> int:
        query = self.build_query(account_id, case_filters)
        count = await self._session.scalar(select(func.count()).where().select_from(query.subquery()))
        return count or 0

    async def add_case(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        issue_id: str,
        parent_issue_id: str | None = None,
        provider_fields: ProviderFieldsColumnType | None = None,
    ) -> CaseTable:
        LOGGER.info("Adding case for account=%s source_id=%s issue_id=%s", account_id, source_id, issue_id)
        case = CaseTable(
            account_id=account_id,
            source_id=source_id,
            issue_id=issue_id,
            parent_issue_id=parent_issue_id,
            status=CaseStatus.OPEN,
            provider_fields=provider_fields or {},
            labels=[],
        )
        self._session.add(case)
        return case

    async def add_comment(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        issue_id: str,
        comment: CreateCaseComment,
    ) -> CaseTable:
        LOGGER.info("Adding comment to case for source_id %s, issue_id=%s", source_id, issue_id)
        case = await self.get_case(account_id, source_id, issue_id)
        comments = case.comments or []
        comments = [  # noqa: C416
            c for c in comments
        ]  # we need to do this due to sqlalchemy postgresql.ARRAY that doesn't recognize append as a change
        comments.append(CaseComment(id=len(comments) + 1, created_at=utcnow(), **comment.model_dump()))
        case.comments = comments
        return case

    async def get_case_recommendations(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
    ) -> list[Implementation]:
        LOGGER.info("Getting case for account=%s source_id=%s issue_id=%s", account_id, source_id, issue_id)
        case = await self.get_case(account_id, source_id, issue_id, with_recommendations=True)
        return case.recommendations or []

    async def update_case_recommendations(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, recommendations: list[Implementation]
    ) -> None:
        LOGGER.info(
            "Updating case recommendations for account=%s source_id=%s issue_id=%s recommendations=%s",
            account_id,
            source_id,
            issue_id,
            {r.id: r.status for r in recommendations},
        )
        await self.update_case(account_id, source_id, issue_id, recommendations=recommendations)

    async def delete_cases_by_source(self, account_id: AccountIdType, source_id: SourceIdType) -> None:
        LOGGER.info("Deleting cases for account=%s source_id=%s", account_id, source_id)
        query = (
            update(CaseTable)
            .values(deleted_at=datetime.now(UTC))
            .where(CaseTable.account_id == account_id, CaseTable.source_id == source_id)  # type: ignore[arg-type]
            .execution_options(synchronize_session="fetch")
        )
        await self._session.exec(query)  # type: ignore[call-overload]

    async def delete_case(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str) -> None:
        LOGGER.info("Deleting case for account=%s source_id=%s issue_id=%s", account_id, source_id, issue_id)
        await self.update_case(account_id, source_id, issue_id, deleted_at=datetime.now(UTC))

    def _update_case_status_inner(
        self, case: CaseTable, status: CaseStatus, dismissed_reason: str | None = None
    ) -> None:
        if case.status == status:
            return

        audit_args = CaseAuditUpdateStatusArgs(old_status=case.status, new_status=status)
        case.status = status
        if status == CaseStatus.DISMISSED:
            case.dismissed_reason = dismissed_reason
            audit_args.reason = dismissed_reason

        history_record = CaseHistoryTable(
            account_id=case.account_id,
            audit_action=CaseAuditAction.update_status,
            audit_action_args=audit_args.model_dump(),
        )
        case.case_history.append(history_record)

    async def update_case_status(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        issue_id: str,
        status: CaseStatus,
        dismissed_reason: str | None = None,
    ) -> None:
        LOGGER.info(
            "Updating case status for account=%s source_id=%s issue_id=%s to %s",
            account_id,
            source_id,
            issue_id,
            status,
        )
        case = await self.get_case(account_id, source_id, issue_id)
        self._update_case_status_inner(case, status, dismissed_reason)

    def _override_risk_score_inner(self, case: CaseTable, new_risk_score: int) -> None:
        if case.issue_analysis is None:
            LOGGER.error("Cannot override risk score for case %s, issue analysis is None", case.id)
            return
        if case.original_risk_score is None:
            case.original_risk_score = case.issue_analysis.risk_score
        audit_args = CaseAuditOverrideRiskCategoryArgs(
            old_risk_score=cast(int, case.issue_analysis.risk_score), new_risk_score=new_risk_score
        )
        case.issue_analysis.risk_score = new_risk_score
        history_record = CaseHistoryTable(
            account_id=case.account_id,
            audit_action=CaseAuditAction.override_risk_category,
            audit_action_args=audit_args.model_dump(),
        )
        case.case_history.append(history_record)

    async def override_risk_score(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        issue_id: str,
        new_risk_score: Annotated[int, Field(ge=0, le=100)],
    ) -> None:
        LOGGER.info(
            "Updating case risk score for account=%s source_id=%s issue_id=%s to %s",
            account_id,
            source_id,
            issue_id,
            new_risk_score,
        )
        case = await self.get_case(account_id, source_id, issue_id, with_analysis=True)
        if not case.issue_analysis:
            raise CaseHasNotBeenProcessedError(case.id)  # type: ignore[arg-type]
        self._override_risk_score_inner(case, new_risk_score)

    async def update_case(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        issue_id: str,
        **table_fields: Any,
    ) -> None:
        LOGGER.info("Updating case with issue %s with fields %s", issue_id, table_fields)
        query = (
            update(CaseTable)
            .where(CaseTable.account_id == account_id)  # type: ignore[arg-type]
            .where(CaseTable.source_id == source_id)  # type: ignore[arg-type]
            .where(CaseTable.issue_id == issue_id)  # type: ignore[arg-type]
            .values(**table_fields)
        )
        await self._session.exec(query)  # type: ignore[call-overload]
        await self._session.commit()

    async def update_case_by_id(
        self,
        case_id: int,
        **table_fields: Any,
    ) -> None:
        LOGGER.info("Updating case with case_id %s with fields %s", case_id, table_fields.keys())
        query = (
            update(CaseTable)
            .where(CaseTable.id == case_id)  # type: ignore[arg-type]
            .values(**table_fields)
        )
        await self._session.exec(query)  # type: ignore[call-overload]
        await self._session.commit()

    async def get_cases_by_account_and_source_without_filters(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
    ) -> ScalarResult[CaseTable]:
        query = select(CaseTable).where(CaseTable.account_id == account_id).where(CaseTable.source_id == source_id)
        return await self._session.exec(query)

    async def upsert_cases(
        self, account_id: AccountIdType, source_id: SourceIdType, cases: list[UpsertCasesBulkArgs]
    ) -> None:
        LOGGER.info("Upserting %s cases for account %s", len(cases), account_id)
        if not cases:
            return
        if any(case.fields.keys() != cases[0].fields.keys() for case in cases):
            raise ValueError("All cases must have the same field keys")

        for i in range(0, len(cases), UPSERT_BATCH_SIZE):
            batch = cases[i : i + UPSERT_BATCH_SIZE]
            LOGGER.info("Processing batch %s to %s of %s cases", i, i + len(batch), len(cases))
            upsert_data = [
                {"account_id": account_id, "source_id": source_id, "issue_id": case.issue_id, **case.fields}
                for case in batch
            ]
            query = pg_insert(CaseTable).values(upsert_data)
            field_names = set(batch[0].fields.keys())
            update_columns = {
                col.name: getattr(pg_insert(CaseTable).excluded, col.name)
                for col in CaseTable.__table__.columns  # type: ignore[attr-defined]
                if col.name in field_names
            }
            query = query.on_conflict_do_update(
                index_elements=["account_id", "source_id", "issue_id"], set_=update_columns
            )
            await self._session.exec(query)  # type: ignore[call-overload]
            await self._session.commit()
            LOGGER.info("Batch %s to %s of %s cases processed", i, i + len(batch), len(cases))

    @classmethod
    def get_issue_hierarchy_cte(cls, issue_id: str, direction: direction_t, max_depth: int = 20) -> CTE:
        columns: list[Any] = [
            CaseTable.id,
            CaseTable.account_id,
            CaseTable.source_id,
            CaseTable.issue_id,
            CaseTable.parent_issue_id,
            literal_column("1").label("level"),
        ]

        if direction == "children":
            base_condition = CaseTable.parent_issue_id == issue_id
        else:
            base_condition = CaseTable.issue_id == issue_id

        hierarchy_cte: CTE = (
            select(*columns)
            .where(
                CaseTable.deleted_at == None,  # noqa: E711
                base_condition,
            )
            .cte(name=f"{direction}_hierarchy", recursive=True)
        )

        recursive_columns = [
            CaseTable.id,
            CaseTable.account_id,
            CaseTable.source_id,
            CaseTable.issue_id,
            CaseTable.parent_issue_id,
            (hierarchy_cte.c.level + 1).label("level"),
        ]

        if direction == "children":
            join_condition = CaseTable.parent_issue_id == hierarchy_cte.c.issue_id
        else:
            join_condition = CaseTable.issue_id == hierarchy_cte.c.parent_issue_id

        recursive_query: Select[Any] | SelectOfScalar[Any] = (
            select(*recursive_columns)  # type: ignore[arg-type]
            .join(
                hierarchy_cte,
                and_(
                    CaseTable.account_id == hierarchy_cte.c.account_id,
                    CaseTable.source_id == hierarchy_cte.c.source_id,
                    join_condition,
                ),
            )
            .where(
                CaseTable.deleted_at == None,  # noqa: E711
                hierarchy_cte.c.level < max_depth,
            )
        )

        hierarchy_cte = hierarchy_cte.union_all(recursive_query)
        return hierarchy_cte

    @classmethod
    def get_children_hierarchy_cte(cls, issue_id: str, max_depth: int = 20) -> CTE:
        return cls.get_issue_hierarchy_cte(issue_id=issue_id, direction="children", max_depth=max_depth)

    @classmethod
    def get_parents_hierarchy_cte(cls, issue_id: str, max_depth: int = 20) -> CTE:
        return cls.get_issue_hierarchy_cte(issue_id=issue_id, direction="parents", max_depth=max_depth)

    async def get_issue_hierarchy(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        issue_id: str,
        direction: direction_t,
        include_initial: bool = False,
    ) -> list[Any]:
        hierarchy_cte = self.get_issue_hierarchy_cte(issue_id=issue_id, direction=direction)
        query = select(hierarchy_cte)  # type: ignore[call-overload]
        if direction == "parents" and not include_initial:
            query = query.where(hierarchy_cte.c.level > 1)
        query = query.where(hierarchy_cte.c.account_id == account_id)
        query = query.where(hierarchy_cte.c.source_id == source_id)
        query = query.order_by(hierarchy_cte.c.level)
        return (await self._session.exec(query)).all()  # type: ignore[no-any-return]

    async def get_children_hierarchy(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
    ) -> list[Any]:
        return await self.get_issue_hierarchy(account_id, source_id, issue_id, direction="children")

    async def get_parents_hierarchy(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
    ) -> list[Any]:
        return await self.get_issue_hierarchy(account_id, source_id, issue_id, direction="parents")

    async def get_cases_by_ids(
        self,
        account_id: AccountIdType,
        case_ids: list[int],
    ) -> Sequence[tuple[int, int, str, dict]]:  # type: ignore[type-arg]
        LOGGER.info("Getting cases for account=%s by case_ids=%s", account_id, case_ids)
        if not case_ids:
            return []
        query = select(CaseTable.id, CaseTable.source_id, CaseTable.issue_id, CaseTable.provider_fields).where(
            CaseTable.account_id == account_id,
            CaseTable.id.in_(case_ids),  # type: ignore[union-attr]
            CaseTable.deleted_at.is_(None),  # type: ignore[union-attr]
        )
        return (await self._session.exec(query)).all()  # type: ignore[return-value]
