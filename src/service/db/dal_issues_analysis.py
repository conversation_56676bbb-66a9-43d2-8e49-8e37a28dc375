from __future__ import annotations

import logging
from typing import TYPE_CHECKING

from prime_shared.common_types import AccountIdType, SourceIdType
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm.attributes import flag_modified
from sqlmodel import select
from sqlmodel.sql.expression import SelectOfScalar, and_, desc

from service.errors import IssueAnalysisNotFoundError, IssueAnalysisNotFoundForIssueError
from service.models import SecurityControl

from .dal_base import DalBase
from .tables import CaseTable, IssueAnalysisTable

if TYPE_CHECKING:
    from service.k8s_jobs.classification_job.classification_job_logic import ClassificationJobLogicResult

LOGGER = logging.getLogger("rat-logic-service")


class IssuesAnalysisDAL(DalBase):
    async def delete_controls(self, issue_analysis_id: int, control_ids: set[str]) -> IssueAnalysisTable:
        issue_analysis = await self.get_issue_analysis_by_id(issue_analysis_id)
        LOGGER.info("Control ids to delete: %s", control_ids)
        if not issue_analysis.controls:
            LOGGER.debug("No controls in issue")
            return issue_analysis

        issue_analysis.controls = [control for control in issue_analysis.controls if control.id not in control_ids]

        flag_modified(issue_analysis, "controls")
        self._session.add(issue_analysis)
        await self._session.commit()

        return issue_analysis

    async def add_new_controls(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, controls: list[SecurityControl]
    ) -> tuple[IssueAnalysisTable, dict[str, str]]:
        LOGGER.info("Adding controls to issue analysis %s", issue_id)
        issue_analysis = await self.get(account_id, source_id, issue_id)
        if not issue_analysis.controls:
            issue_analysis.controls = []
        next_id_for_new_control = 1
        for control in issue_analysis.controls:
            if control.id.isdigit() and next_id_for_new_control <= int(control.id):
                next_id_for_new_control = int(control.id)
                next_id_for_new_control += 1

        LOGGER.debug("Adding new controls to issue analysis - last control id: %s", next_id_for_new_control)

        # We have to update new control id for the new recommendations.
        # Build the mapping of old control id of the new recommendations to the new control id.
        map_old_control_id_to_new = {}
        for control in controls:
            new_control_id = str(next_id_for_new_control)
            map_old_control_id_to_new[str(control.id)] = new_control_id
            LOGGER.debug("Control id updated from [%s] to [%s]", control.id, new_control_id)

            control.id = new_control_id
            next_id_for_new_control += 1

        issue_analysis.controls.extend(controls)

        flag_modified(issue_analysis, "controls")
        self._session.add(issue_analysis)
        await self._session.commit()

        LOGGER.info("New controls have added to issue analysis")

        return issue_analysis, map_old_control_id_to_new

    async def _add_classification_result(
        self, account_id: AccountIdType, result: ClassificationJobLogicResult
    ) -> IssueAnalysisTable:
        new_issue_analysis = IssueAnalysisTable(
            account_id=account_id,
            classification=result.classification,
            issue_hash=result.issue_hash,
            error=result.error,
            concerns=result.concerns,
            long_assessment=result.long_assessment,
            short_assessment=result.short_assessment,
            risk_score=result.risk_score,
            confidence=result.confidence,
            controls=result.controls,
            research_package_version=result.research_package_version,
            confidentiality=result.confidentiality_score,
            integrity=result.integrity_score,
            availability=result.availability_score,
            keywords=result.keywords,
            is_automated=result.is_automated,
            is_security_enhancement=result.is_security_enhancement,
            issue_links=result.issue_links,
            fire_summary=result.fire_summary,
        )
        self._session.add(new_issue_analysis)
        await self._session.commit()
        return new_issue_analysis

    async def _get_single_issue_by_query(self, query: SelectOfScalar[IssueAnalysisTable]) -> IssueAnalysisTable | None:
        try:
            return (await self._session.exec(query.limit(1))).one()
        except NoResultFound:
            return None

    async def get_issue_analysis_by_id(self, issue_analysis_id: int) -> IssueAnalysisTable:
        query = select(IssueAnalysisTable).where(IssueAnalysisTable.id == issue_analysis_id)
        if issue_analysis := await self._get_single_issue_by_query(query):
            return issue_analysis
        raise IssueAnalysisNotFoundError(issue_analysis_id=issue_analysis_id)

    async def get(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str) -> IssueAnalysisTable:
        if results := await self.get_bulk(account_id, source_id, [issue_id]):
            return results[0]
        raise IssueAnalysisNotFoundForIssueError(account_id=account_id, source_id=source_id, issue_id=issue_id)

    async def get_bulk(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_ids: list[str]
    ) -> list[IssueAnalysisTable]:
        query = (
            select(IssueAnalysisTable)
            .join(
                CaseTable,
                and_(
                    CaseTable.issue_analysis_id == IssueAnalysisTable.id,
                    CaseTable.account_id == account_id,
                    CaseTable.source_id == source_id,
                    CaseTable.issue_id.in_(issue_ids),  # type: ignore[attr-defined]
                ),
            )
            .where(IssueAnalysisTable.deleted_at == None)  # noqa: E711
        )
        query = query.order_by(desc(IssueAnalysisTable.created_at))
        return list((await self._session.exec(query)).all())
