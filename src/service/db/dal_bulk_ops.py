from __future__ import annotations

import logging
from typing import TYPE_CHECKING

from prime_shared.common_types import AccountIdType, SourceIdType

from service.db.dal_base import DalBase
from service.db.tables import LabelTable
from service.models import (
    BulkUpdateCasesRequest,
)

if TYPE_CHECKING:
    from service.db import CaseTable

LOGGER = logging.getLogger("bulk_ops_dal")


class BulkOpsDAL(DalBase):
    def _update_case_status(self, case_entry: CaseTable, bulk_update_cases: BulkUpdateCasesRequest) -> None:
        if not bulk_update_cases.status:
            return
        self._service_dal.cases_dal._update_case_status_inner(
            case_entry, bulk_update_cases.status, bulk_update_cases.dismissed_reason
        )

    def _update_case_risk_score(
        self,
        case_entry: CaseTable,
        bulk_update_cases: BulkUpdateCasesRequest,
    ) -> None:
        if bulk_update_cases.risk_score is None:
            return
        if case_entry.issue_analysis is None:
            LOGGER.warning("Case %s has not been processed yet! Skipping risk score update.", case_entry.id)
            return
        if not case_entry.issue_analysis.classification:
            LOGGER.warning("Case %s doesn't have risk score, Skipping risk score update.", case_entry.id)
            return
        self._service_dal.cases_dal._override_risk_score_inner(case_entry, bulk_update_cases.risk_score)

    async def bulk_update_cases(
        self, account_id: AccountIdType, source_id: SourceIdType, bulk_update_cases: BulkUpdateCasesRequest
    ) -> list[str]:
        updated: list[str] = []

        if not bulk_update_cases.issues_ids:
            return updated

        # Adding labels to an account
        if bulk_update_cases.labels:
            existing_account_labels = await self._service_dal.labels_dal.get_labels(account_id)
            unique_labels_create = bulk_update_cases.labels - {labels.name for labels in existing_account_labels}
            self._session.add_all(LabelTable(account_id=account_id, name=label) for label in unique_labels_create)

        # Fetch all relevant cases
        existing_cases = await self._service_dal.cases_dal.get_cases_by(
            account_id, source_id, issues=list(bulk_update_cases.issues_ids), with_analysis=True
        )

        for case_entry in existing_cases:
            updated.append(case_entry.issue_id)

            if bulk_update_cases.labels:
                case_entry.labels = case_entry.labels + list(bulk_update_cases.labels - set(case_entry.labels))
            self._update_case_status(case_entry, bulk_update_cases)
            self._update_case_risk_score(case_entry, bulk_update_cases)

        LOGGER.info("Bulk updated %s issues out of %s", len(updated), len(bulk_update_cases.issues_ids))
        return updated
