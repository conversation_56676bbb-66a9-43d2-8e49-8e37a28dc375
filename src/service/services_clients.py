import logging
from collections.abc import <PERSON><PERSON><PERSON><PERSON><PERSON>, Callable, Coroutine
from datetime import datetime
from functools import cache
from typing import Any, cast

from prime_chatbot_service_client import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from prime_chatbot_service_client import Api<PERSON>lient as Cha<PERSON>botApi<PERSON><PERSON>
from prime_chatbot_service_client import Configuration as Chat<PERSON><PERSON>onfiguration
from prime_config_service_client import Api<PERSON>lient as ConfigApi<PERSON>lient
from prime_config_service_client import Config<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON><PERSON><PERSON>
from prime_config_service_client import Configuration as ConfigConfiguration
from prime_config_service_client import Notifications<PERSON>pi as ConfigNot<PERSON><PERSON><PERSON>
from prime_fetcher_service_client import ApiClient as FetcherApiClient
from prime_fetcher_service_client import Configuration as FetcherConfiguration
from prime_fetcher_service_client import Download<PERSON>pi
from prime_file_manager_service_client import ApiClient as FileManagerApiClient
from prime_file_manager_service_client import Configuration as FileManagerConfiguration
from prime_file_manager_service_client import Document<PERSON>ype, <PERSON><PERSON>n<PERSON>, FileOriginRequest, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from prime_notification_service_client import ApiClient as NotifyApi<PERSON>lient
from prime_notification_service_client import Configuration as NotifyApiConfiguration
from prime_notification_service_client import NotifyApi
from prime_policy_service_client import ApiClient as PolicyApiClient
from prime_policy_service_client import Configuration as PolicyConfiguration
from prime_policy_service_client import PoliciesApi
from prime_security_review_service_client import ApiClient as SecurityReviewApiClient
from prime_security_review_service_client import Configuration as SecurityReviewConfigurations
from prime_security_review_service_client import DesignDocsApi
from prime_service_kit.fastapi_utils import PaginationResponse, fetch_all_paginated_iterator, prime_headers_generator
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import ApiClient as SourceApiClient
from prime_source_service_client import Configuration as SourceConfigurations
from prime_source_service_client import SourcesApi

from service.config import get_config

LOGGER = logging.getLogger("clients")
MAX_RETRIES = 10
FILE_INFO_BATCH_SIZE = 9000


@cache
def _security_review_client() -> SecurityReviewApiClient:
    return SecurityReviewApiClient(
        SecurityReviewConfigurations(get_config().security_review_service_url),
        headers_generator=prime_headers_generator,
    )


@cache
def _file_manager_client() -> FileManagerApiClient:
    return FileManagerApiClient(
        FileManagerConfiguration(get_config().file_manager_service_url),
        headers_generator=prime_headers_generator,
    )


@cache
def _source_client() -> SourceApiClient:
    return SourceApiClient(
        SourceConfigurations(get_config().source_service_url), headers_generator=prime_headers_generator
    )


@cache
def _config_client() -> ConfigApiClient:
    return ConfigApiClient(
        ConfigConfiguration(get_config().config_service_url), headers_generator=prime_headers_generator
    )


@cache
def _notification_client() -> NotifyApiClient:
    return NotifyApiClient(
        NotifyApiConfiguration(get_config().notification_service_url), headers_generator=prime_headers_generator
    )


@cache
def _fetcher_client() -> FetcherApiClient:
    return FetcherApiClient(
        FetcherConfiguration(get_config().fetcher_service_url), headers_generator=prime_headers_generator
    )


@cache
def _chatbot_client() -> ChatbotApiClient:
    return ChatbotApiClient(
        ChatbotConfiguration(get_config().chatbot_service_url), headers_generator=prime_headers_generator
    )


@cache
def _policy_client() -> PolicyApiClient:
    return PolicyApiClient(
        PolicyConfiguration(get_config().policy_service_url), headers_generator=prime_headers_generator
    )


class ServicesClients:
    #############################

    @classmethod
    def agent_api(cls) -> AgentApi:
        return AgentApi(api_client=_chatbot_client())

    @classmethod
    def llm_archive_api(cls) -> ArchiveApi:
        return ArchiveApi(api_client=_chatbot_client())

    @classmethod
    def files_api(cls) -> FilesApi:
        return FilesApi(api_client=_file_manager_client())

    @classmethod
    def relationship_api(cls) -> RelationshipApi:
        return RelationshipApi(api_client=_file_manager_client())

    @classmethod
    def source_api(cls) -> SourcesApi:
        return SourcesApi(api_client=_source_client())

    @classmethod
    def config_api(cls) -> ConfigApi:
        return ConfigApi(api_client=_config_client())

    @classmethod
    def config_notification_api(cls) -> ConfigNotificationsApi:
        return ConfigNotificationsApi(api_client=_config_client())

    @classmethod
    def query_view_api(cls) -> QueryViewApi:
        return QueryViewApi(api_client=_config_client())

    @classmethod
    def dashboard_api(cls) -> DashboardApi:
        return DashboardApi(api_client=_config_client())

    @classmethod
    def notification_api(cls) -> NotifyApi:
        return NotifyApi(api_client=_notification_client())

    @classmethod
    def download_api(cls) -> DownloadApi:
        return DownloadApi(api_client=_fetcher_client())

    @classmethod
    def policy_api(cls) -> PoliciesApi:
        return PoliciesApi(api_client=_policy_client())

    @classmethod
    def design_docs(cls) -> DesignDocsApi:
        return DesignDocsApi(api_client=_security_review_client())


async def close_clients() -> None:
    LOGGER.info("Closing all clients")
    await _file_manager_client().close()
    await _source_client().close()
    await _config_client().close()
    await _notification_client().close()
    await _fetcher_client().close()
    await _chatbot_client().close()
    await _policy_client().close()
    await _security_review_client().close()


async def get_files_info_iterator(
    account_id: AccountIdType,
    source_id: SourceIdType | None = None,
    document_type: DocumentType | None = None,
    since: datetime | None = None,
    file_origin_request: FileOriginRequest | None = None,
) -> AsyncIterator[FileInfo]:
    cb = cast(
        Callable[[], Coroutine[Any, Any, PaginationResponse[FileInfo]]],
        ServicesClients.files_api().get_files_info,
    )
    async for result in fetch_all_paginated_iterator(
        cb,
        batch_size=FILE_INFO_BATCH_SIZE,
        account_id=account_id,
        source_id=source_id,
        document_type=document_type,
        since=since,
        file_origin_request=file_origin_request,
    ):
        yield result


async def get_all_files_info(
    account_id: AccountIdType,
    source_id: SourceIdType,
    document_type: DocumentType | None = None,
    since: datetime | None = None,
    file_origin_request: FileOriginRequest | None = None,
) -> dict[str, FileInfo]:
    _it = get_files_info_iterator(
        account_id, source_id, document_type=document_type, since=since, file_origin_request=file_origin_request
    )
    return {file_info.origin_id.removesuffix(".json"): file_info async for file_info in _it}
