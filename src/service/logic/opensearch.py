from functools import cache

import boto3
from opensearchpy import AsyncHttpConnection, AsyncOpenSearch, AWSV4SignerAsyncAuth


async def get_field_top_options(account_id: str, field: str, count: int = 10) -> list[str]:
    client = get_opensearch_client()
    body = {"size": 0, "aggs": {"top_values": {"terms": {"field": field, "size": count}}}}
    response = await client.search(index=account_id, body=body)
    return list({bucket["key"] for bucket in response["aggregations"]["top_values"]["buckets"]})


async def get_field_autocomplete(account_id: str, field: str, value: str, count: int = 10) -> list[str]:
    client = get_opensearch_client()
    body = {"_source": field, "query": {"prefix": {field: value}}, "size": count}
    response = await client.search(index=account_id, body=body)
    return list({hit["_source"]["value"] for hit in response["hits"]["hits"]})


@cache
def get_opensearch_client() -> AsyncOpenSearch:
    endpoint = ""
    session = boto3.Session()
    client = AsyncOpenSearch(
        hosts=[{"host": endpoint, "port": 443}],
        http_auth=AWSV4SignerAsyncAuth(session.get_credentials(), session.region_name),
        use_ssl=True,
        verify_certs=True,
        connection_class=AsyncHttpConnection,
        timeout=120,
    )
    return client
