import json
from collections import defaultdict
from functools import lru_cache
from pathlib import Path

from prime_config_service_client.models.security_framework import SecurityFramework as ConfigSecurityFramework
from prime_shared.common_dataclasses import SecurityFramework as SharedSecurityFramework

from service.models import (
    ExternalControl,
    ExternalImplementation,
    Implementation,
    SecurityControl,
)
from service.models.concerns import ExternalFrameworkConcern, ExternalPrimeConcern, PrimeRecommendation


def _build_external_control(
    framework_concern: ExternalFrameworkConcern,
    control_id: str,
    controls: dict[str, SecurityControl],
) -> tuple[ExternalControl | None, bool]:
    if external_control := next((c for c in framework_concern.controls if c.id == control_id), None):
        return external_control, True

    security_control = controls.get(control_id)
    if security_control is None:
        return None, False

    control_names = list(set(security_control.control_names).intersection(set(controls)))
    external_control = ExternalControl(**security_control.model_dump(), implementations=[])
    external_control.control_names = control_names

    return external_control, False


def normalize_external_recommendation_prime_concerns(
    external_concerns: list[ExternalPrimeConcern],
) -> list[ExternalPrimeConcern]:
    for concern in external_concerns:
        # concern.recommendations = [
        #     recommendation for recommendation in concern.recommendations if recommendation.implementations
        # ]
        for recommendation in concern.recommendations:
            for implementation in recommendation.implementations:
                implementation.concern_id = concern.id
    return external_concerns
    # return [concern for concern in external_concerns if concern.recommendations]


def normalize_external_recommendation_framework_concerns(
    external_concerns: list[ExternalFrameworkConcern],
) -> list[ExternalFrameworkConcern]:
    for concern in external_concerns:
        concern.controls = [control for control in concern.controls if control.implementations]
        for control in concern.controls:
            for recommendation in control.implementations:
                recommendation.concern_id = concern.id
    return external_concerns
    # return [concern for concern in external_concerns if concern.controls]


def _handle_prime_external_concern(
    external_control: ExternalControl,
    control_recommendations: list[Implementation],
    prime_concerns: dict[int, ExternalPrimeConcern],
    framework_concern: ExternalFrameworkConcern,
    account_framework: ConfigSecurityFramework,
) -> None:
    prime_recommendation = PrimeRecommendation(
        id=external_control.id,
        name=external_control.name,
        description=external_control.description,
        implementations=[],
    )
    if prime_recommendation not in prime_concerns[framework_concern.id].recommendations:
        prime_concerns[framework_concern.id].recommendations.append(prime_recommendation)

    external_implementations = [
        ExternalImplementation(**recommendation.model_dump()) for recommendation in control_recommendations
    ]
    for external_implementation in external_implementations:
        external_implementation.controls = {
            framework: set(sorted(framework_controls))  # noqa: C414
            for framework, framework_controls in external_implementation.controls.items()
            if framework == account_framework.value
        }

    prime_recommendation.implementations.extend(external_implementations)


def _handle_framework_external_concern(
    external_control: ExternalControl,
    control_recommendations: list[Implementation],
    framework_concern: ExternalFrameworkConcern,
    is_already_added: bool,
) -> None:
    if not is_already_added:
        framework_concern.controls.append(external_control)

    external_implementations = [
        ExternalImplementation(**recommendation.model_dump()) for recommendation in control_recommendations
    ]
    external_control.implementations.extend(external_implementations)


def _build_external_concern(
    framework_concern: ExternalFrameworkConcern,
    prime_concerns: dict[int, ExternalPrimeConcern],
    control_items: dict[str, list[Implementation]],
    controls: dict[str, SecurityControl],
    account_framework: ConfigSecurityFramework,
) -> None:
    for control_id, control_recommendations in control_items.items():
        external_control, is_already_added = _build_external_control(framework_concern, control_id, controls)
        if not external_control:
            continue

        if external_control.framework == SharedSecurityFramework.PRIME:
            _handle_prime_external_concern(
                external_control, control_recommendations, prime_concerns, framework_concern, account_framework
            )
        else:
            _handle_framework_external_concern(
                external_control, control_recommendations, framework_concern, is_already_added
            )


def build_concerns_with_external_framework_concerns(
    framework_concerns: list[ExternalFrameworkConcern],
    controls: dict[str, SecurityControl],
    recommendations: list[Implementation],
    prime_concerns: dict[int, ExternalPrimeConcern],
    account_framework: ConfigSecurityFramework,
) -> None:
    output_mapping: dict[int, dict[str, list[Implementation]]] = defaultdict(lambda: defaultdict(list))
    for recommendation in recommendations:
        output_mapping[recommendation.concern_id][recommendation.control_id].append(recommendation)

    for concern_id, control_items in output_mapping.items():
        for external_concern in [c for c in framework_concerns if not concern_id or c.id == concern_id]:
            _build_external_concern(external_concern, prime_concerns, control_items, controls, account_framework)


@lru_cache
def get_controls_dict(framework: SharedSecurityFramework) -> dict[str, SecurityControl]:
    file_path = Path(__file__).parent.parent / "static-files" / f"{framework.value.lower()}_controls.json"
    with file_path.open("r") as f:
        controls_dict = json.load(f)
    return {
        control_id: SecurityControl(
            id=control_id,
            name=control_title,
            description="",
            control_names=[control_id],
            framework=SharedSecurityFramework(framework),
        )
        for control_id, control_title in controls_dict.items()
    }
