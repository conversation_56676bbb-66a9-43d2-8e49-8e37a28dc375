import datetime
import logging
from collections.abc import Sequence
from typing import cast

from prime_service_kit.fastapi_utils import PaginationResponse
from prime_service_kit.shared_types import AccountArgumentType
from prime_shared import prime_filter
from prime_shared.common_dataclasses import PaginationArgs
from prime_shared.common_types import AccountIdType, SourceIdType

from service.config import get_config
from service.db import CaseHistoryTable, CaseTable, IssueAnalysisTable, ServiceDAL
from service.errors import CaseHasNotBeenProcessedError, CaseNotFoundError
from service.logic.jira_manager import JiraPrimeIssue
from service.models import (
    CaseAuditAction,
    ExternalCase,
    ExternalCaseHistory,
    ExternalCaseWorkroom,
    ProviderFieldsInfoMapping,
)
from service.models.filters_and_sort import Filter, SortField

from .filters_and_sort import CaseFilters
from .issue_analysis import build_external_concerns, get_external_issue_analysis, get_external_issue_analysis_summary
from .utils import get_provider_fields_from_case

LOGGER = logging.getLogger("external_cases")


async def get_external_case_by_issue_id(
    account_id: AccountIdType,
    source_id: SourceIdType,
    issue_id: str,
    service_dal: ServiceDAL,
    provider_fields_info: ProviderFieldsInfoMapping,
) -> ExternalCase:
    case = await service_dal.get_case_view(account_id=account_id, source_id=source_id, issue_id=issue_id)
    return await build_external_case(case, service_dal, provider_fields_info)


async def _get_case_parents(case: CaseTable, service_dal: ServiceDAL) -> list[str]:
    result = []
    running_case = case
    while running_case.parent_issue_id:
        result.append(running_case.parent_issue_id)
        try:
            running_case = await service_dal.cases_dal.get_case(
                account_id=running_case.account_id,
                source_id=running_case.source_id,
                issue_id=running_case.parent_issue_id,
            )
        except CaseNotFoundError:
            LOGGER.warning(
                "Parent case [%s] not found for source id [%s] with account id [%s]",
                running_case.parent_issue_id,
                running_case.source_id,
                running_case.account_id,
            )
            break

    return result


async def build_external_case_workroom(
    case: CaseTable,
    issue_analysis: IssueAnalysisTable,
    provider_fields_info: ProviderFieldsInfoMapping | None,
    service_dal: ServiceDAL,
) -> ExternalCaseWorkroom:
    external_issue_analysis = get_external_issue_analysis_summary(case, issue_analysis)
    case_fields = get_provider_fields_from_case(case, provider_fields_info)
    parents = await _get_case_parents(case, service_dal)
    api_link = str(case.provider_fields.get("self", ""))
    title = case.provider_fields.get("summary", "")
    return ExternalCaseWorkroom(
        account_id=case.account_id,
        source_id=case.source_id,
        issue_id=case.issue_id,
        case_id=cast(int, case.id),
        status=case.status,
        issue_analysis=external_issue_analysis,
        write_back_recommendations=case.write_back_ref_id is not None,
        title=cast(str, title),
        link=JiraPrimeIssue.get_ui_weblink(api_link, case.issue_id) if api_link else "",
        labels=case.labels,
        provider_fields={field: str(case_fields[field].value) for field in case_fields},
        provider_fields_min_schema=dict(case_fields),
        parents=parents,
        progress_percentage=case.progress_percentage,
    )


async def build_external_case(
    case: CaseTable,
    service_dal: ServiceDAL,
    provider_fields_info: ProviderFieldsInfoMapping | None,
) -> ExternalCase:
    if case.issue_analysis_id is None:
        raise CaseHasNotBeenProcessedError(case.id)  # type: ignore[arg-type]
    issue_analysis = await service_dal.issues_analysis_dal.get_issue_analysis_by_id(case.issue_analysis_id)

    workroom_case = await build_external_case_workroom(case, issue_analysis, provider_fields_info, service_dal)
    framework_concerns, prime_concerns = await build_external_concerns(
        case.account_id, issue_analysis.concerns, issue_analysis.controls, case.recommendations
    )
    case_history = [
        history_from_table(record) for record in case.case_history if record.audit_action != CaseAuditAction.view
    ]
    case_history_filtered = prime_filter(
        get_config().service_environment, case.account_id, case_history, lambda x: x.user
    )
    external_issue_analysis = get_external_issue_analysis(case, issue_analysis)
    return ExternalCase(
        **workroom_case.model_dump(by_alias=True, exclude={"issue_analysis"}),
        prime_concerns=prime_concerns,
        framework_concerns=framework_concerns,
        history=case_history_filtered,
        issue_analysis=external_issue_analysis,
        comments=case.comments,
    )


def history_from_table(table: CaseHistoryTable) -> ExternalCaseHistory:
    history = ExternalCaseHistory(
        user=table.user,
        audit_action=table.audit_action,
        audit_action_args=table.audit_action_args,
        created_at=cast(datetime.datetime, table.created_at),
    )
    return history


async def get_external_cases_from_cases(
    cases_data: Sequence[tuple[CaseTable, IssueAnalysisTable | None]],
    service_dal: ServiceDAL,
) -> list[ExternalCaseWorkroom]:
    LOGGER.info("Building external cases for %s cases", len(cases_data))
    results = []
    for case, issue_analysis in cases_data:
        if issue_analysis is None:
            LOGGER.error("Issue analysis is None for case %s", case.id)
            continue
        results.append(
            await build_external_case_workroom(case, issue_analysis, provider_fields_info=None, service_dal=service_dal)
        )
    return results


async def get_workroom_cases(
    account_id: AccountArgumentType,
    service_dal: ServiceDAL,
    pagination_args: PaginationArgs,
    case_filters: CaseFilters,
    sort_args: list[SortField],
) -> list[ExternalCaseWorkroom]:
    cases_data = await service_dal.get_workroom_cases_by(
        account_id=account_id,
        pagination_args=pagination_args,
        case_filters=case_filters,
        sort_args=sort_args,
    )
    LOGGER.info("Getting %s cases", len(cases_data))
    return await get_external_cases_from_cases(cases_data, service_dal)


async def get_workroom_cases_paginated(
    account_id: AccountArgumentType,
    service_dal: ServiceDAL,
    pagination_args: PaginationArgs,
    filter_args: list[Filter],
    sort_args: list[SortField],
) -> PaginationResponse[ExternalCaseWorkroom]:
    filter_cases = CaseFilters(filter_args)
    total = await service_dal.cases_dal.get_cases_count_by(account_id, case_filters=filter_cases)
    external_cases = await get_workroom_cases(account_id, service_dal, pagination_args, filter_cases, sort_args)
    return PaginationResponse[ExternalCaseWorkroom].from_results(external_cases, pagination_args, total)
