from __future__ import annotations

import json
import logging
import urllib
from datetime import datetime
from typing import Any, cast

from service.logic.issues import BasePrimeIssue, PrimeIssueAttributes
from service.models import ProviderFieldData, provider_field_types

from .jira_fields import JiraFieldInfo

LOGGER = logging.getLogger("issue_manager")


class JiraIssueAttributes(PrimeIssueAttributes):
    self: str

    status: str
    assignee: str | None = None
    reporter: str | None = None
    sprint: list[dict[str, Any]] | None = None


class JiraPrimeIssue(BasePrimeIssue):
    _attributes: JiraIssueAttributes

    def __init__(
        self, attributes: JiraIssueAttributes, parent: JiraPrimeIssue | None = None, description: str | None = None
    ):
        super().__init__(attributes, parent, description)
        self._data: dict[str, Any] = {}

    @property
    def attributes(self) -> JiraIssueAttributes:
        return self._attributes

    @property
    def raw_data(self) -> str:
        return json.dumps(self._data)

    @property
    def fields(self) -> dict[str, Any]:
        return cast(dict[str, Any], self._data.get("fields", {}))

    def get_fields_data(
        self, fields_info_list: list[JiraFieldInfo], keep_empty: bool = False
    ) -> dict[str, provider_field_types]:
        fields_data = {field_info.id: self.get_field(field_info).value for field_info in fields_info_list}
        if not keep_empty:
            fields_data = {k: v for k, v in fields_data.items() if v is not None and v != ""}

        return fields_data

    def get_field(self, field_info: JiraFieldInfo) -> ProviderFieldData:
        if (value := self.get_nested_value(self._data, field_info.key)) is None:
            value = self.get_nested_value(self.fields, field_info.key)
        value_json = json.dumps(value, default=str)
        return field_info.generate_provider_field(value_json)

    def _get_raw_field_data(self, field_id: str) -> provider_field_types:
        return self.get_nested_value(self.fields, field_id)

    @staticmethod
    def get_sprint_value(data: dict[str, Any]) -> provider_field_types | None:
        """The sprint value is a list of dictionaries, each dictionary must contain boardId and id"""
        fields = data.get("fields", {})
        if not isinstance(fields, dict):
            return None

        for field_value in fields.values():
            if not isinstance(field_value, list):
                continue

            for inner_field_value in field_value:
                if isinstance(inner_field_value, dict) and "boardId" in inner_field_value and "id" in inner_field_value:
                    return cast(provider_field_types, field_value)
        return None

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> JiraPrimeIssue:
        attributes = JiraIssueAttributes(
            id=cast(str, cls.get_nested_value(data, "key")),
            self=cast(str, cls.get_nested_value(data, "self")),
            summary=cast(str, cls.get_nested_value(data, "fields.summary")),
            created=cast(datetime, cls.get_nested_value(data, "fields.created")),
            issuetype=cast(str, cls.get_nested_value(data, "fields.issuetype.name")),
            status=cast(str, cls.get_nested_value(data, "fields.status.name")),
            creator=cast(str | None, cls.get_nested_value(data, "fields.creator.displayName")),
            assignee=cast(str | None, cls.get_nested_value(data, "fields.assignee.displayName")),
            reporter=cast(str | None, cls.get_nested_value(data, "fields.reporter.displayName")),
            project=cast(str | None, cls.get_nested_value(data, "fields.project.key")),
            sprint=cast(list[dict[str, Any]] | None, cls.get_sprint_value(data)),
        )
        description = cast(str | None, cls.get_nested_value(data, "fields.description"))
        obj = cls(attributes=attributes, description=description)

        obj._data = data
        return obj

    @staticmethod
    def get_ui_weblink(self_link: str, issue_id: str) -> str:
        hostname = urllib.parse.urlparse(self_link).hostname
        return f"https://{hostname}/browse/{issue_id}"
