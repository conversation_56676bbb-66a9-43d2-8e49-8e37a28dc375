from __future__ import annotations

import logging
from typing import Any

from prime_jira_client.jira_client import UserNotExistsInJira
from pydantic import EmailStr

from service.errors import UserNotFoundInJiraError
from service.logic.issues import FileManagerIssuesManager

from .jira_client import get_jira_client
from .jira_issue import JiraPrimeIssue

LOGGER = logging.getLogger("issue_manager")


class JiraIssuesManager(FileManagerIssuesManager[JiraPrimeIssue]):
    def _create_issue(self, data: dict[str, Any]) -> JiraPrimeIssue:
        return JiraPrimeIssue.from_dict(data)

    @staticmethod
    async def add_watcher(account_id: str, source_id: int, issue_id: str, watcher_email: EmailStr) -> None:
        jira_client = await get_jira_client(account_id=account_id, source_id=source_id)

        try:
            jira_client.add_watcher_to_issue(issue_id, str(watcher_email))
        except UserNotExistsInJira as e:
            raise UserNotFoundInJiraError(watcher_email) from e
