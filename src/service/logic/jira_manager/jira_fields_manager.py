from __future__ import annotations

import json
import logging
from datetime import UTC, datetime
from typing import Any, cast

from prime_file_manager_service_client import DocumentType, FileUploadMetadataRequest
from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType

from service.config import get_config
from service.errors import JiraFieldNotFoundError
from service.services_clients import ServicesClients

from .jira_client import get_jira_client
from .jira_fields import JiraFieldInfo, JiraFieldSchema

LOGGER = logging.getLogger("jira_custom_fields_service")

STATIC_FIELDS_MAPPING = [
    JiraFieldInfo(id="id", key="id", name="id", schema=JiraFieldSchema(type="string")),
    JiraFieldInfo(id="self", key="self", name="self", schema=JiraFieldSchema(type="string")),
    JiraFieldInfo(id="key", key="key", name="key", schema=<PERSON>ra<PERSON>ieldSchema(type="string")),
]

# Jira custom field name map to prime attribute name
JIRA_NAME_TO_PRIME_ATTR = {"Sprint": "sprint"}  # TODO: the jira manager should manage this mapping


class JiraFieldsManager:
    def __init__(self, fields: list[JiraFieldInfo]) -> None:
        self._fields_by_id: dict[str, JiraFieldInfo] = {field.id: field for field in fields}
        self._fields_by_key: dict[str, JiraFieldInfo] = {field.key: field for field in fields}

        self.prime_attr_to_jira_field: dict[str, str] = {}  # Map to custom jira field eg: Sprint -> customfield_1000202
        self._init_custom_fields_mapping()

    def _init_custom_fields_mapping(self) -> None:
        for jira_field_info in self._fields_by_id.values():
            if jira_field_info.name in JIRA_NAME_TO_PRIME_ATTR:
                custom_field_attribute_name = JIRA_NAME_TO_PRIME_ATTR[jira_field_info.name]
                self.prime_attr_to_jira_field[custom_field_attribute_name] = jira_field_info.id

        for jira_field_info in self._fields_by_key.values():
            if jira_field_info.name in JIRA_NAME_TO_PRIME_ATTR:
                custom_field_attribute_name = JIRA_NAME_TO_PRIME_ATTR[jira_field_info.name]
                self.prime_attr_to_jira_field[custom_field_attribute_name] = jira_field_info.key

    @classmethod
    async def build(
        cls, account_id: AccountIdType, source_id: SourceIdType, redis_cache: AsyncPrefixRedisClient | None = None
    ) -> JiraFieldsManager:
        fields = await cls.get_fields(account_id, source_id, redis_cache)
        return cls(fields + STATIC_FIELDS_MAPPING)

    @property
    def fields_by_id(self) -> dict[str, JiraFieldInfo]:
        return self._fields_by_id or {}

    @property
    def fields_by_key(self) -> dict[str, JiraFieldInfo]:
        return self._fields_by_key or {}

    @classmethod
    async def _upload_fields_data_file(
        cls, account_id: AccountIdType, source_id: SourceIdType, domain: str, raw_jira_fields: list[dict[str, Any]]
    ) -> None:
        LOGGER.info("Uploading fields data for account %s source %s", account_id, source_id)
        files_client = ServicesClients.files_api()
        fields_data = json.dumps(raw_jira_fields).encode()
        fields_metadata = FileUploadMetadataRequest(
            origin_id=cls._get_fields_name(account_id, source_id),
            source_id=source_id,
            document_type=DocumentType.DOCUMENT,
            timestamp=datetime.now(UTC),
            downloadable_link=cls._get_fields_name(account_id, source_id),
            domain=domain,
        )
        fields_metadata_bytes = json.dumps(fields_metadata.model_dump(mode="json")).encode()
        try:
            await files_client.upload_files(account_id, [fields_metadata_bytes, fields_data])
        except Exception:
            LOGGER.error("Failed to upload fields data for account %s source %s", account_id, source_id)

    @classmethod
    async def _download_fields_file(cls, account_id: AccountIdType, source_id: SourceIdType) -> list[dict[str, Any]]:
        LOGGER.info("Downloading fields data for account %s source %s", account_id, source_id)
        files_client = ServicesClients.files_api()
        origin_id = cls._get_fields_name(account_id, source_id)
        data = await files_client.download_origin_file_for_source(account_id, source_id, origin_id)
        return cast(list[dict[str, Any]], json.loads(data))

    @classmethod
    async def _get_fields_data(cls, account_id: AccountIdType, source_id: SourceIdType) -> list[dict[str, Any]]:
        LOGGER.info("Getting fields data from Jira for account %s source %s", account_id, source_id)
        source = await ServicesClients.source_api().get_source(account_id=account_id, source_id=source_id)
        if source.source_type == SourceType.JIRA:
            try:
                raw_jira_fields = (await get_jira_client(account_id, source_id)).fields()
                # Store the fields in the file storage to future use if the token get invalid
                domain = await ServicesClients.source_api().get_domain_from_source(account_id, source_id)
                try:
                    await cls._upload_fields_data_file(account_id, source_id, domain, raw_jira_fields)
                except Exception:
                    LOGGER.error("Failed to upload fields data for account %s source %s", account_id, source_id)
            except Exception:
                LOGGER.exception("Failed to get jira fields for account %s source %s", account_id, source_id)
                # fallback in case we cant get the fields from Jira
                raw_jira_fields = await cls._download_fields_file(account_id, source_id)

        elif source.source_type == SourceType.DESIGNDOCS:
            raw_jira_fields = cast(list[dict[str, Any]], {})
        return raw_jira_fields

    @classmethod
    async def get_fields(
        cls, account_id: AccountIdType, source_id: SourceIdType, redis_cache: AsyncPrefixRedisClient | None = None
    ) -> list[JiraFieldInfo]:
        if redis_cache and (redis_jira_fields := await redis_cache.get(cls._fields_key(source_id))):
            raw_jira_fields = json.loads(redis_jira_fields)
        else:
            raw_jira_fields = await cls._get_fields_data(account_id, source_id)
            if redis_cache:
                redis_jira_fields = json.dumps(raw_jira_fields)
                await redis_cache.set(cls._fields_key(source_id), redis_jira_fields, ex=get_config().redis_expire_time)
        fields = []
        for field in raw_jira_fields:
            try:
                fields.append(JiraFieldInfo.model_validate(field))
            except Exception:
                LOGGER.warning("Could not process jira field %s", field)
        LOGGER.info("Jira fields for account %s source %s has %s fields", account_id, source_id, len(fields))
        return fields

    @classmethod
    def _fields_key(cls, source_id: SourceIdType) -> str:
        return f"jira_fields:{source_id}"

    def get_field_by_id(self, field_id: str) -> JiraFieldInfo:
        ret_val = self.fields_by_id.get(field_id)
        if ret_val is None:
            ret_val = self.fields_by_key.get(field_id)
        if ret_val is None:
            raise JiraFieldNotFoundError(field_id)
        return ret_val

    @classmethod
    def _get_fields_name(cls, account_id: AccountIdType, source_id: SourceIdType) -> str:
        return f"fields_json_{account_id}_{source_id}"
