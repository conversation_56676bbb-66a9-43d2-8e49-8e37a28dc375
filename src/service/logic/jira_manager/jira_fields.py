import json
import logging
from collections.abc import Callable
from typing import Any, cast

from dateutil.parser import parse as date_parse
from pydantic import BaseModel, Field

from service.models import ProviderFieldData, ProviderFieldInfo, ProviderFieldType, provider_field_types

LOGGER = logging.getLogger("jira_fields")


def unknown_decoder(value: Any) -> provider_field_types:
    if isinstance(value, dict) and "name" in value:
        return cast(str, value["name"])
    elif isinstance(value, dict) and "value" in value:
        return cast(str, value["value"])
    elif value and isinstance(value, str):
        return cast(str, value)
    LOGGER.warning("Could not parse field value %s", value)
    return None


def generic_dict_decoder(value: Any) -> str:
    if "name" in value:
        return str(value["name"])
    elif "value" in value:
        return str(value["value"])
    return ""


def generic_array_decoder(value: Any) -> list[str]:
    return [generic_dict_decoder(item) if isinstance(item, dict) else str(item) for item in value]


def sprint_array_decoder(value: Any) -> list[dict[str, str]]:
    result = []
    for item in value:
        dict_item = {}
        if isinstance(item, dict):
            dict_item["state"] = item.get("state", "")
            dict_item["name"] = item.get("name", "")
            dict_item["startDate"] = item.get("startDate", "")
            dict_item["endDate"] = item.get("endDate", "")
            dict_item["boardId"] = item.get("boardId", "")
            result.append(dict_item)
    return result


DEFAULT_FIELD_TYPE = ProviderFieldType.STRING

types_handling_map: dict[str, tuple[ProviderFieldType, Callable[[Any], Any]]] = {
    "boolean": (ProviderFieldType.BOOLEAN, bool),
    "number": (ProviderFieldType.NUMBER, int),
    "datetime": (ProviderFieldType.DATE, lambda x: date_parse(x)),
    "date": (ProviderFieldType.DATE, lambda x: date_parse(x)),
    "string": (ProviderFieldType.STRING, str),
    "user": (ProviderFieldType.ENUM, lambda x: x["displayName"]),
    "array": (ProviderFieldType.ARRAY, generic_array_decoder),
    "priority": (ProviderFieldType.ENUM, lambda x: x["name"]),
    "issuetype": (ProviderFieldType.ENUM, lambda x: x["name"]),
    "project": (ProviderFieldType.ENUM, lambda x: x["key"]),
    "status": (ProviderFieldType.ENUM, lambda x: x["name"]),
    "comments-page": (ProviderFieldType.NUMBER, lambda x: str(len(x["comments"]))),
    "resolution": (ProviderFieldType.ENUM, lambda x: str(x["name"])),
    "watches": (ProviderFieldType.BOOLEAN, lambda x: int(x["watchCount"]) > 0),
    "option": (ProviderFieldType.ENUM, lambda x: x["value"] if "value" in x else x["name"]),
    "option-with-child": (ProviderFieldType.ENUM, lambda x: x["value"]),
    "any": (ProviderFieldType.STRING, str),
    "timetracking": (ProviderFieldType.STRING, lambda x: x.get("originalEstimate", "")),
    "team": (ProviderFieldType.ENUM, lambda x: str(x["name"])),
    "sprint": (ProviderFieldType.ARRAY, sprint_array_decoder),
}


class JiraFieldSchema(BaseModel):
    type: str = ""
    custom: str | None = None
    customId: int | None = None  # noqa: N815
    system: str | None = None


class JiraFieldInfo(BaseModel):
    id: str
    key: str
    name: str
    field_schema: JiraFieldSchema = Field(alias="schema", default_factory=JiraFieldSchema)
    untranslatedName: str = ""  # noqa: N815
    orderable: bool = False
    navigable: bool = False
    searchable: bool = False
    clause_names: list[str] = Field(default_factory=list, alias="clauseNames")

    @property
    def type(self) -> str:
        return self.field_schema.type

    def generate_provider_field(self, value_json: str | None) -> ProviderFieldData:
        meta = self.generate_provider_field_info()
        try:
            ret_value = self._parse_field_value(value_json) if value_json else None
        except Exception:
            LOGGER.exception("Failed to generate provider field for %s with type %s", self.name, self.type)
            ret_value = None
        return ProviderFieldData(value=ret_value, **meta.model_dump(by_alias=True))

    def generate_provider_field_info(self) -> ProviderFieldInfo:
        if field_decoder := self._get_field_decoder():
            provider_type = field_decoder[0]
        else:
            LOGGER.warning("Cant handle field %s (id: %s) of type %s", self.name, self.id, self.type)
            provider_type = DEFAULT_FIELD_TYPE
        return ProviderFieldInfo(id=self.id, name=self.name, type=provider_type)

    def _get_field_decoder(self) -> tuple[ProviderFieldType, Callable[[Any], Any]] | None:
        if (field_decoder := types_handling_map.get(self.name)) or (field_decoder := types_handling_map.get(self.type)):
            return field_decoder
        LOGGER.warning("Using default decoder for field %s", self.type)
        return None

    def _parse_field_value(self, value_json: str) -> provider_field_types:
        try:
            value = json.loads(value_json)
        except json.JSONDecodeError:
            LOGGER.error("Could not parse field %s with value %s", self.name, value_json)
            return None
        if value is None:
            return None
        if field_decoder := self._get_field_decoder():
            type_parser = field_decoder[1]
        else:
            LOGGER.info("Using generic parser for field %s with type %s", self.name, self.type)
            type_parser = unknown_decoder
        return cast(provider_field_types, type_parser(value))
