from __future__ import annotations

from typing import cast

from prime_jira_client import PrimeJiraClient
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import JiraConnectionDetails

from service.services_clients import ServicesClients


async def get_jira_client(account_id: AccountIdType, source_id: SourceIdType) -> PrimeJiraClient:
    conn_details = (
        await ServicesClients.source_api().get_connection_details(account_id, int(source_id))
    ).actual_instance
    jira_info: JiraConnectionDetails = cast(JiraConnectionDetails, conn_details)
    return PrimeJiraClient(server=jira_info.jira_url, email=jira_info.email, token=jira_info.api_token)
