import logging
import os
from abc import ABC, abstractmethod
from typing import cast

from jinja2 import Environment, FileSystemLoader
from jira import JIRAError
from prime_shared.common_types import AccountIdType, SourceIdType

from service.db import CaseTable, get_service_dal_context, service_dal_depends
from service.errors import WriteBackError
from service.logic.external_cases import build_external_case
from service.logic.jira_manager import get_jira_client
from service.models import ExternalCase, ImplementationStatus
from service.models.concerns import ExternalControl, ExternalFrameworkConcern, ExternalPrimeConcern, PrimeRecommendation
from service.models.jira_comment_data import Concern, Control, JiraCommentData, Recommendation

LOGGER = logging.getLogger("write_back")


def _build_control_for_mapping(
    controls_name: str, control_description: str, recommendation_data: Recommendation
) -> Control:
    return Control(
        name=controls_name,
        description=control_description,
        recommendations=[recommendation_data],
    )


async def _build_concerns_recommendations_map(external_case: ExternalCase) -> JiraCommentData:
    result = {}

    def process_recommendations(
        concern: ExternalPrimeConcern | ExternalFrameworkConcern,
        controls_list: list[PrimeRecommendation] | list[ExternalControl],
    ) -> None:
        for controls in controls_list:
            for implementation in controls.implementations:
                if implementation.status != ImplementationStatus.APPROVED:
                    continue

                recommendation_data = Recommendation(
                    id=implementation.id,
                    text=implementation.recommendation,
                )

                if concern.id not in result:
                    result[concern.id] = Concern(
                        short_description=concern.short_description,
                        controls={
                            str(controls.id): _build_control_for_mapping(
                                controls.name, controls.description, recommendation_data
                            )
                        },
                    )
                else:
                    controls_item = result[concern.id].controls
                    if controls.id in controls_item:
                        controls_item[controls.id].recommendations.append(recommendation_data)
                    else:
                        controls_item[controls.id] = _build_control_for_mapping(
                            controls.name, controls.description, recommendation_data
                        )

    for prime_concern in external_case.prime_concerns:
        process_recommendations(prime_concern, prime_concern.recommendations)

    for framework in external_case.framework_concerns.values():
        for framework_concern in framework:
            process_recommendations(framework_concern, framework_concern.controls)

    return JiraCommentData(data=result)


def _get_comment_template(concerns_recommendations_map: JiraCommentData) -> str:
    current_dir = os.path.dirname(os.path.realpath(__file__))
    env = Environment(loader=FileSystemLoader(f"{current_dir}/templates"), autoescape=True)
    template = env.get_template("jira_comment_template.jinja2")
    return template.render(data=concerns_recommendations_map.data)


class WriteBackHandler(ABC):
    def __init__(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, service_dal: service_dal_depends
    ) -> None:
        self._issue_id = issue_id
        self._account_id = account_id
        self._source_id = source_id
        self._service_dal = service_dal

    async def _get_case(self) -> CaseTable:
        async with get_service_dal_context() as service_dal:
            return await service_dal.cases_dal.get_case(self._account_id, self._source_id, self._issue_id)

    async def _update_write_back_ref_id(self, case: CaseTable, ref_id: str) -> None:
        case.write_back_ref_id = ref_id
        LOGGER.info("Updated write back ref id for case %s", case.id)

    async def write_back(self) -> str | None:
        LOGGER.info(
            "Writing back to Jira account=%s source_id=%s issue_id=%s",
            self._account_id,
            self._source_id,
            self._issue_id,
        )

        case = await self._service_dal.cases_dal.get_case(
            self._account_id, self._source_id, self._issue_id, with_recommendations=True, with_summary=True
        )
        external_case = await build_external_case(case, self._service_dal, None)

        concerns_recommendations_map = await _build_concerns_recommendations_map(external_case)
        if len(concerns_recommendations_map.data) == 0:
            return None

        write_back_text = _get_comment_template(concerns_recommendations_map)

        LOGGER.info("Writing back to Jira: %s", write_back_text)
        ref_id = await self._write_back(write_back_text, case.write_back_ref_id)
        await self._update_write_back_ref_id(case=case, ref_id=ref_id)

        LOGGER.info("Updated write back ref id [%s] for case [%s]", ref_id, case.id)

        await self.add_label(case)
        return ref_id

    @abstractmethod
    async def _write_back(self, recommendations_text: str, write_back_ref_id: str | None) -> str:
        raise NotImplementedError

    async def add_label(self, case: CaseTable) -> None:
        label = "write-to-jira"
        existing_labels = case.labels if case.labels else []

        if label not in existing_labels:
            await self._service_dal.cases_dal.update_case(
                self._account_id,
                case.source_id,
                case.issue_id,
                labels=existing_labels + [label],
            )

        await self._service_dal.labels_dal.add_labels(self._account_id, [label])


class JiraWriteBackHandler(WriteBackHandler):
    def __init__(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, service_dal: service_dal_depends
    ) -> None:
        super().__init__(account_id, source_id, issue_id, service_dal)

    async def _write_back(self, recommendations_text: str, write_back_ref_id: str | None) -> str:
        jira_client = await get_jira_client(account_id=self._account_id, source_id=self._source_id)
        if write_back_ref_id is not None:
            try:
                comment = jira_client.comment(self._issue_id, write_back_ref_id)
                comment.update(body=recommendations_text)
                return cast(str, comment.id)
            except JIRAError as err:
                if err.status_code != 404:
                    LOGGER.exception("Failed to write back to Jira")
                    raise WriteBackError(err.text) from None
        try:
            comment = jira_client.add_comment(self._issue_id, recommendations_text)
            return cast(str, comment.id)
        except JIRAError as err:
            LOGGER.exception("Failed to write back to Jira")
            raise WriteBackError(err.text) from None


class GithubWriteBackHandler(WriteBackHandler):
    def __init__(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, service_dal: service_dal_depends
    ) -> None:
        super().__init__(account_id, source_id, issue_id, service_dal)
