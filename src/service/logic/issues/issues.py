from __future__ import annotations

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, cast

from pydantic import BaseModel, Field, field_serializer

from service.models import ProviderFieldInfo, ProviderFieldType, provider_field_types

PRIME_FIELDS_MAPPING = {
    "summary": ProviderFieldInfo(id="summary", name="Summary", type=ProviderFieldType.STRING),
    "created": ProviderFieldInfo(id="created", name="Created", type=ProviderFieldType.DATE),
    "issuetype": ProviderFieldInfo(id="issuetype", name="Issue Type", type=ProviderFieldType.ENUM),
    "project": ProviderFieldInfo(id="project", name="Project", type=ProviderFieldType.ENUM),
    "creator": ProviderFieldInfo(id="creator", name="Creator", type=ProviderFieldType.ENUM),
}


class PrimeIssueAttributes(BaseModel):
    id_: str = Field(alias="id")

    summary: str
    created: datetime
    issuetype: str
    project: str | None = None

    creator: str | None = None

    @property
    def key(self) -> str:
        return self.id_

    @field_serializer("created")
    def serialize_created(self, created: datetime, _info: Any) -> str:
        return created.isoformat()


class BasePrimeIssue(ABC):
    def __init__(self, attributes: PrimeIssueAttributes, parent: BasePrimeIssue | None, description: str | None):
        self._attributes = attributes
        self.parent = parent
        self.description = description

    def __str__(self) -> str:
        return f"{self.key} -> {self.parent.key if self.parent else None}: {self.description}"

    def __repr__(self) -> str:
        return str(self)

    def to_dict(self) -> dict[str, Any]:
        dict_to_return: dict[str, Any] = {
            **self.get_attributes_fields(),
            "parent": self.parent.to_dict() if self.parent else None,
            "description": self.description,
        }
        return dict_to_return

    @property
    def key(self) -> str:
        return self._attributes.key

    @property
    @abstractmethod
    def attributes(self) -> PrimeIssueAttributes: ...

    @classmethod
    @abstractmethod
    def from_dict(cls, data: dict[str, Any]) -> BasePrimeIssue: ...

    def get_attributes_fields(self) -> dict[str, provider_field_types]:
        return self._attributes.model_dump(exclude_none=True, by_alias=True)

    @staticmethod
    def get_nested_value(data: dict[str, Any], keys: str) -> provider_field_types:
        keys_list = keys.split(".")
        try:
            for key in keys_list:
                data = data[key]
            result = cast(provider_field_types, data)
        except (KeyError, TypeError):
            result = None
        return result

    @staticmethod
    @abstractmethod
    def get_ui_weblink(self_link: str, issue_id: str) -> str: ...
