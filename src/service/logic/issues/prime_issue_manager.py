from __future__ import annotations

import logging
from typing import Any

from .issue_manager import FileManagerIssuesManager
from .issues import PrimeIssueAttributes
from .prime_issue import GenericPrimeIssue

LOGGER = logging.getLogger("issue_manager")


class PrimeIssuesManager(FileManagerIssuesManager[GenericPrimeIssue]):
    def __init__(self, account_id: str, source_id: int):
        super().__init__(
            account_id=account_id,
            source_id=source_id,
        )

    def _create_issue(self, data: dict[str, Any]) -> GenericPrimeIssue:
        attributes = PrimeIssueAttributes.model_validate(data, strict=False)
        parent_data = data.get("parent")
        parent = GenericPrimeIssue.from_dict(parent_data) if parent_data else None
        return GenericPrimeIssue(attributes=attributes, description=data.get("description"), parent=parent)
