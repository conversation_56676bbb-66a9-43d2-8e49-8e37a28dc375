from __future__ import annotations

import asyncio
import logging
from typing import TYPE_CHECKING

from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType

from service.errors import JiraFieldNotFoundError
from service.models import ProviderFieldInfo, ProviderFieldsInfoMapping
from service.services_clients import ServicesClients

if TYPE_CHECKING:
    from service.logic.jira_manager import JiraFieldInfo, JiraFieldsManager
LOGGER = logging.getLogger("jira_fields")


async def _get_jira_fields(
    account_id: AccountIdType,
    source_id: SourceIdType | None,
    redis_client: AsyncPrefixRedisClient,
    fields_ids: list[str],
) -> list[JiraFieldInfo]:
    from service.logic.jira_manager import JiraFieldsManager

    sources_ids: list[SourceIdType]
    if source_id:
        sources_ids = [source_id]
    else:
        sources = await ServicesClients.source_api().get_sources(account_id=account_id, source_type=SourceType.JIRA)
        sources_ids = [source.id for source in sources]
    LOGGER.info("Getting Jira fields for account %s and sources %s", account_id, sources_ids)

    tasks = [JiraFieldsManager.build(account_id, _source_id, redis_client) for _source_id in sources_ids]
    ret_fields: list[JiraFieldInfo] = []
    for jira_field_manager, field_source_id in zip(await asyncio.gather(*tasks), sources_ids, strict=False):
        for field_id in fields_ids:
            try:
                if field_id in jira_field_manager.prime_attr_to_jira_field:
                    jira_field = _get_custom_field_by_id(field_id, jira_field_manager)
                else:
                    jira_field = jira_field_manager.get_field_by_id(field_id)

                ret_fields.append(jira_field)
            except JiraFieldNotFoundError:
                LOGGER.info("Field with id %s not found in Jira fields for source %s", field_id, field_source_id)
    return ret_fields


def _get_custom_field_by_id(field_id: str, jira_field_manager: JiraFieldsManager) -> JiraFieldInfo:
    field_id_in_jira = jira_field_manager.prime_attr_to_jira_field[field_id]
    LOGGER.debug("Update custom field by id [%s] to [%s]", field_id_in_jira, field_id)

    jira_field = jira_field_manager.get_field_by_id(field_id_in_jira)
    jira_field = update_custom_field_attributes(jira_field, field_id)

    return jira_field


def update_custom_field_attributes(sprint_field_id: JiraFieldInfo, new_field_id: str) -> JiraFieldInfo:
    sprint_field_id.name = new_field_id
    sprint_field_id.id = new_field_id
    return sprint_field_id


async def get_provider_field_info(
    account_id: AccountIdType, source_id: SourceIdType | None, field_id: str, redis_client: AsyncPrefixRedisClient
) -> ProviderFieldInfo:
    jira_fields = await _get_jira_fields(account_id, source_id, redis_client, [field_id])
    if not jira_fields:
        raise JiraFieldNotFoundError(field_id)
    return jira_fields[0].generate_provider_field_info()


async def get_provider_fields_info_stored(
    account_id: AccountIdType,
    source_id: SourceIdType | None,
    redis_client: AsyncPrefixRedisClient,
) -> ProviderFieldsInfoMapping:
    if source_id:
        source = await ServicesClients.source_api().get_source(account_id=account_id, source_id=source_id)
        sources = [source]
    else:
        sources = await ServicesClients.source_api().get_sources(account_id=account_id)
    provider_fields: list[ProviderFieldInfo] = []
    for source in sources:
        if source.source_type == SourceType.JIRA:
            stored_jira_fields = await get_jira_fields_stored(account_id, source.id, redis_client)
            provider_fields.extend(jira_field.generate_provider_field_info() for jira_field in stored_jira_fields)
    return {field.id: field for field in provider_fields}


async def get_provider_fields_info_selected(
    account_id: AccountIdType,
    source_id: SourceIdType,
    redis_client: AsyncPrefixRedisClient,
) -> ProviderFieldsInfoMapping:
    selected_jira_fields = await get_jira_fields_selected(account_id, source_id, redis_client)
    return {jira_field.id: jira_field.generate_provider_field_info() for jira_field in selected_jira_fields}


async def get_jira_fields_selected(
    account_id: AccountIdType, source_id: SourceIdType, redis_client: AsyncPrefixRedisClient
) -> list[JiraFieldInfo]:
    account_config = await ServicesClients.config_api().download_config_file(account_id)
    account_config_fields: list[str] = account_config.providers_attributes.get("jira", [])
    ret_fields = await _get_jira_fields(account_id, source_id, redis_client, account_config_fields)
    return ret_fields


async def get_jira_fields_stored(
    account_id: AccountIdType, source_id: SourceIdType, redis_client: AsyncPrefixRedisClient
) -> list[JiraFieldInfo]:
    from service.logic.jira_manager import JiraIssueAttributes

    selected_jira_fields = await get_jira_fields_selected(account_id, source_id, redis_client)
    prime_attributes = [field.alias or field_name for field_name, field in JiraIssueAttributes.model_fields.items()]
    prime_fields_ids = [field for field in prime_attributes if field not in [_f.id for _f in selected_jira_fields]]
    prime_jira_fields = await _get_jira_fields(account_id, source_id, redis_client, prime_fields_ids)
    all_fields = selected_jira_fields + prime_jira_fields
    LOGGER.info("stored Jira fields %s", all_fields)
    return all_fields
