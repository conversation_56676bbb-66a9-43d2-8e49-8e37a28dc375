from __future__ import annotations

import json
import logging
import time
from abc import abstractmethod
from typing import Any, <PERSON><PERSON>, TypeVar

from aiohttp import ServerDisconnectedError
from prime_file_manager_service_client import FileOriginRequest
from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import unzip_data
from tenacity import before_sleep_log, retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from service.errors import (
    IssueIdNotFoundInStorageError,
)
from service.services_clients import ServicesClients

from ..issues_graph import IssuesGraph
from .prime_issue import BasePrimeIssue

LOGGER = logging.getLogger("issue_manager")

issues_t = list[str] | None
redis_t = AsyncPrefixRedisClient | None

ISSUE_T_co = TypeVar("ISSUE_T_co", bound=BasePrimeIssue, covariant=True)


class BaseIssuesManager(Generic[ISSUE_T_co]):
    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
    ) -> None:
        LOGGER.info("Initializing BaseIssuesManager with account_id: %s, source_id: %s", account_id, source_id)
        self._account_id: AccountIdType = account_id
        self._source_id: SourceIdType = source_id
        self._hash_cache: dict[str, str] = {}

    async def get_issues(self, issues_id: list[str]) -> dict[str, ISSUE_T_co]:
        raise NotImplementedError("get_issues method should be implemented in subclasses")

    async def get_issue(self, issue_id: str, issue_graph: IssuesGraph | None = None) -> ISSUE_T_co:
        raise NotImplementedError("get_issue method should be implemented in subclasses")

    async def get_full_issue(self, issue_id: str, issues_graph: IssuesGraph) -> BasePrimeIssue:
        parents = issues_graph.get_ancestors(issue_id)
        issues = await self.get_issues([issue_id] + parents)
        prime_issue = issues[issue_id]
        current = prime_issue
        for parent in parents:
            if parent not in issues:
                LOGGER.error("Parent issue %s not found in the fetched issues", parent)
                break
            current.parent = issues[parent]
            current = current.parent
        return prime_issue


class LocalIssuesManager(BaseIssuesManager[ISSUE_T_co]):
    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        prime_issue: list[ISSUE_T_co],
    ) -> None:
        super().__init__(account_id=account_id, source_id=source_id)
        self._issues: dict[str, ISSUE_T_co] = {issue.key: issue for issue in prime_issue}

    async def get_issues(self, issues_id: list[str]) -> dict[str, ISSUE_T_co]:
        return {issues_id: self._issues[issues_id] for issues_id in issues_id if issues_id in self._issues}

    async def get_issue(self, issue_id: str, issues_grap: IssuesGraph | None = None) -> ISSUE_T_co:
        return self._issues[issue_id]


class FileManagerIssuesManager(BaseIssuesManager[ISSUE_T_co]):
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(ServerDisconnectedError),
        reraise=True,
        before_sleep=before_sleep_log(LOGGER, logging.DEBUG, exc_info=True),
    )
    async def get_issues(self, issues_id: list[str]) -> dict[str, ISSUE_T_co]:
        if not issues_id:
            LOGGER.info("No issues ids provided to download")
            return {}
        start = time.time()
        msg = f"{issues_id[0]} -> {issues_id[-1]}"
        LOGGER.info("Downloading %s issues: %s", len(issues_id), msg)
        request = FileOriginRequest(file_names=[f"{issue_id}.json" for issue_id in issues_id])
        files_data = await ServicesClients.files_api().download_files_for_source(
            self._account_id, self._source_id, request
        )
        results: dict[str, ISSUE_T_co] = {}
        for key, file_data in unzip_data(files_data).items():
            results[key.replace(".json", "")] = self._create_issue(json.loads(file_data))
        LOGGER.info("Downloaded %s issues: %s took %s", len(results), msg, time.time() - start)
        return results

    async def get_issue(self, issue_id: str, issues_graph: IssuesGraph | None = None) -> ISSUE_T_co:
        issues_resp = await self.get_issues([issue_id])
        if (issue := issues_resp.get(issue_id)) is None:
            LOGGER.error("Issue %s not found in storage", issue_id)
            raise IssueIdNotFoundInStorageError(account_id=self._account_id, issue_id=issue_id) from None
        if issues_graph and (parent_id := issues_graph.get_parent(issue_id)):
            LOGGER.info("Getting parent %s for issue %s", parent_id, issue.attributes.id_)
            issue.parent = await self.get_issue(parent_id, issues_graph)
        return issue

    @abstractmethod
    def _create_issue(self, data: dict[str, Any]) -> ISSUE_T_co: ...
