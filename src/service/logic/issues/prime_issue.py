from __future__ import annotations

import logging
from typing import Any

from .issues import BasePrimeIssue, PrimeIssueAttributes

LOGGER = logging.getLogger("issue_manager")


class GenericPrimeIssue(BasePrimeIssue):
    @property
    def attributes(self) -> PrimeIssueAttributes:
        return self._attributes

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> GenericPrimeIssue:
        attributes = PrimeIssueAttributes.model_validate(data, strict=False)
        description = data.get("description")
        parent_data = data.get("parent")
        parent = GenericPrimeIssue.from_dict(parent_data) if parent_data else None
        return cls(attributes=attributes, description=description, parent=parent)

    @staticmethod
    def get_ui_weblink(self_link: str, issue_id: str) -> str:
        return ""
