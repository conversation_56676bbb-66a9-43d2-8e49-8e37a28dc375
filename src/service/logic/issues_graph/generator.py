from __future__ import annotations

import logging
from datetime import datetime

from jira import Issue
from prime_file_manager_service_client import DocumentType, FileInfo
from prime_jira_client import PrimeJiraClient, jql_builder
from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import AccountIdType, SourceIdType

from service.db.dal_cases import IssueStructure, UpsertCasesBulkArgs
from service.db.service_dal import ServiceDAL
from service.errors.errors import SummaryTreeGenerationError
from service.logic.jira_manager.jira_client import get_jira_client
from service.services_clients import get_all_files_info

from .issues_graph import GraphItem, IssuesGraph

LOGGER = logging.getLogger("summary_generation")

LIMIT = 5000
EX_TIME_24_HOURS = 60 * 60 * 24


class GraphGenerator:
    def __init__(self, service_dal: ServiceDAL, account_id: AccountIdType, source_id: SourceIdType):
        self._service_dal = service_dal
        self._account_id = account_id
        self._source_id = source_id

    async def load(
        self, redis_client: AsyncPrefixRedisClient | None, issue_id_fallback_from_db: str | None = None
    ) -> IssuesGraph:
        if redis_client and (tree := await self.load_from_redis(redis_client)):
            return tree
        return await self.load_from_db(issue_id_fallback_from_db)

    async def load_from_db(self, issue_id: str | None = None) -> IssuesGraph:
        return IssuesGraph.build(await self._get_tree_items_from_db(issue_id))

    async def _get_tree_items_from_db(self, parent_id: str | None) -> list[GraphItem]:
        LOGGER.info("Generating tree from db items for account %s, source %s", self._account_id, self._source_id)
        existing_structure: IssueStructure = await self._service_dal.cases_dal.get_issues_structure(
            self._account_id, self._source_id, parent_id, with_parents=True
        )
        return [GraphItem(key=issue_id, parent=parent) for parent, issue_id, updated_at in existing_structure]

    async def update_since(self, since: datetime | None) -> IssuesGraph:
        LOGGER.info("Generating tree for account %s, source %s since %s", self._account_id, self._source_id, since)
        try:
            LOGGER.info("Updating tree for account %s, source %s since %s", self._account_id, self._source_id, since)
            file_manager_issues = await get_all_files_info(self._account_id, self._source_id, DocumentType.JIRA, since)
            return await self.update_by_issues(set(file_manager_issues.keys()))
        except SummaryTreeGenerationError:
            LOGGER.exception("Error generating tree")
            raise

    async def update_by_issues(self, issues: set[str]) -> IssuesGraph:
        LOGGER.info("Starting tree generate update flow for %s issues", len(issues))
        jira_items_dict = await self._update_db_from_jira(issues)
        tree_items = await self._get_tree_items_from_db(None)
        tree_items_dict = {item.key: item for item in tree_items}
        tree_items_dict.update(jira_items_dict)
        return IssuesGraph.build(list(tree_items_dict.values()))

    def _get_redis_key(self) -> str:
        return f"{self._source_id}:tree"

    async def write_to_to_redis(self, issues_graph: IssuesGraph, redis_client: AsyncPrefixRedisClient) -> None:
        await redis_client.set(self._get_redis_key(), issues_graph.dump(), ex=EX_TIME_24_HOURS)

    async def load_from_redis(self, redis_client: AsyncPrefixRedisClient) -> IssuesGraph | None:
        if graph_redis_data := await redis_client.get(self._get_redis_key()):
            return IssuesGraph.load(graph_redis_data)
        return None

    @staticmethod
    def _generate_item(issue_info: FileInfo, issue: Issue) -> GraphItem | None:
        parent = getattr(issue.fields.parent, "key", None) if hasattr(issue.fields, "parent") else None
        if issue_info.timestamp is None:
            return None
        return GraphItem(key=issue.key, parent=parent)

    async def _update_db_from_jira(self, issues: set[str]) -> dict[str, GraphItem]:
        LOGGER.info("Updating %s issues from jira into db", len(issues))
        items = await self._get_item_from_jira(issues)
        cases_to_update = [
            UpsertCasesBulkArgs(issue_id=item.key, fields={"parent_issue_id": item.parent}) for item in items.values()
        ]
        await self._service_dal.cases_dal.upsert_cases(
            account_id=self._account_id, source_id=self._source_id, cases=cases_to_update
        )
        if diff := set(issues) - set(items.keys()):
            LOGGER.warning("Some issues were not found in Jira but exists in file manager, keys: %s", diff)
        return items

    async def _get_item_from_jira(self, issues: set[str]) -> dict[str, GraphItem]:
        LOGGER.info("Getting parents for %s jira issues", len(issues))
        tree_items = {}
        jira_client: PrimeJiraClient = await get_jira_client(self._account_id, self._source_id)
        for jql in jql_builder(list(issues)):
            for jira_issue in jira_client.get_issues_generator(jql, fields="parent,type"):
                if jira_issue.key not in issues:
                    LOGGER.warning("Issue %s exists in Jira but not in file manager", jira_issue.key)
                    continue
                parent = jira_issue.fields.parent.key if hasattr(jira_issue.fields, "parent") else None
                tree_items[jira_issue.key] = GraphItem(key=jira_issue.key, parent=parent)
        return tree_items
