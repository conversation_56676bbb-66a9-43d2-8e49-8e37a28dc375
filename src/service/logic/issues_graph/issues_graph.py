from __future__ import annotations

import logging
from collections import defaultdict
from collections.abc import Generator
from typing import NamedTuple, Optional

import msgpack
from igraph import Graph, Vertex  # type: ignore[import-untyped]

ROOT_DEPTH = 0

LOGGER = logging.getLogger("summary_generation")


class GraphItem(NamedTuple):
    key: str
    parent: Optional[str] = None


class IssuesGraph:
    def __init__(self, graph: Graph) -> None:
        self._graph = graph

    @classmethod
    def build(cls, tree_items: list[GraphItem]) -> IssuesGraph:
        graph = Graph(directed=True)
        vertices = list({item.key for item in tree_items}.union(item.parent for item in tree_items if item.parent))
        graph.add_vertices(vertices)
        for item in (item for item in tree_items if item.parent is not None):
            graph.add_edge(graph.vs.find(name=item.parent).index, graph.vs.find(name=item.key).index)
        return cls(graph)

    def __contains__(self, key: str) -> bool:
        return self.exists(key)

    @classmethod
    def build_from_tuples(cls, tree_items: list[tuple[str, str]]) -> IssuesGraph:
        graph = Graph(directed=True)
        vertices = list({item[0] for item in tree_items}.union(item[1] for item in tree_items if item[1]))
        graph.add_vertices(vertices)
        for item in (item for item in tree_items if item[1] is not None):
            graph.add_edge(graph.vs.find(name=item[1]).index, graph.vs.find(name=item[0]).index)
        return cls(graph)

    @property
    def graph(self) -> Graph:
        return self._graph

    def size(self) -> int:
        return len(self._graph.vs)

    def is_leaf(self, key: str) -> bool:
        return len(self.get_children(key)) == 0

    def to_items(self) -> list[GraphItem]:
        items = []
        for vertex in self._graph.vs:
            if parent := self.get_parent(vertex["name"]):
                items.append(GraphItem(vertex["name"], parent))
            else:
                items.append(GraphItem(vertex["name"]))
        return items

    def get_parent(self, key: str) -> str | None:
        parents = self._graph.predecessors(self.get(key))
        return self._graph.vs[parents[0]]["name"] if parents else None

    def get_children(self, key: str) -> list[str]:
        children = self._graph.successors(self.get(key))
        return [self._graph.vs[vertex_id]["name"] for vertex_id in children]

    def has_children(self, key: str) -> bool:
        return len(self.get_children(key)) > 0

    def get_ancestors(self, key: str) -> list[str]:
        all_parents: list[str] = []
        current = self.get(key)
        while True:
            parents = self._graph.predecessors(current)
            if not parents:
                break
            parent_id = parents[0]
            parent_vertex = self._graph.vs[parent_id]
            all_parents.append(parent_vertex["name"])
            current = parent_id
        return all_parents

    def create_subgraph(self, parent_id: str) -> IssuesGraph:
        parent_vertex = self.get(parent_id)
        descendants_indices = self._graph.subcomponent(parent_vertex, mode="out")
        subgraph = self._graph.subgraph(descendants_indices)
        return IssuesGraph(subgraph)

    def get_all_descendants(self, key: str) -> list[str]:
        vertex = self.get(key)
        descendants = self._graph.subcomponent(vertex, mode="out")
        descendants.remove(vertex.index)
        return [self.graph.vs[vertex_id]["name"] for vertex_id in descendants]

    def iterator_from_top(self) -> Generator[list[str]]:
        roots = [vertex for vertex in self._graph.vs if self._graph.indegree(vertex) == 0]
        current_level = roots
        while current_level:
            yield [vertex["name"] for vertex in current_level]
            next_level: list[Vertex] = []
            for vertex in current_level:
                next_level.extend(self._graph.vs[vertex_id] for vertex_id in self._graph.successors(vertex))
            current_level = next_level

    def iterator_from_bottom(self) -> Generator[list[str]]:
        distances = self.get_distance_from_bottom()
        for distance in sorted(distances.keys()):
            yield [vertex["name"] for vertex in distances[distance]]

    def iterator(self) -> Generator[str]:
        for vertex in self._graph.vs:
            yield vertex["name"]

    def _get_distances_from_root(self) -> dict[int, list[Vertex]]:
        roots = [vertex for vertex in self._graph.vs if self._graph.indegree(vertex) == 0]
        distance_to_vertices = defaultdict(list)
        for root in roots:
            queue = [(root.index, 0)]
            visited = {root.index}
            while queue:
                vertex, distance = queue.pop(0)
                distance_to_vertices[distance].append(self._graph.vs[vertex])
                for child in self._graph.successors(vertex):
                    if child not in visited:
                        visited.add(child)
                        queue.append((child, distance + 1))
        return dict(distance_to_vertices)

    def get_distance_from_bottom(self) -> dict[int, list[Vertex]]:
        max_distances = {}
        topo_order = self._graph.topological_sorting(mode="OUT")
        for v_idx in reversed(topo_order):
            if self._graph.outdegree(v_idx) == 0:
                max_distances[v_idx] = 0
            else:
                children = self._graph.successors(v_idx)
                max_distances[v_idx] = max([max_distances[child] for child in children]) + 1
        distance_to_vertices = defaultdict(list)
        for vertex, distance in max_distances.items():
            distance_to_vertices[distance].append(self._graph.vs[vertex])
        return distance_to_vertices

    def is_empty(self) -> bool:
        return not bool(self.size())

    def get(self, key: str) -> Vertex:
        return self._graph.vs.find(name=key)

    def exists(self, key: str) -> bool:
        try:
            self.get(key)
            return True
        except (ValueError, KeyError):
            return False

    @classmethod
    def load(cls, data: bytes) -> IssuesGraph:
        data_dict = msgpack.unpackb(data, raw=False)
        graph = Graph(directed=True)
        graph.add_vertices([node["name"] for node in data_dict["nodes"]])
        name_to_index = {vertex["name"]: i for i, vertex in enumerate(graph.vs)}
        graph.add_edges([(name_to_index[src], name_to_index[tgt]) for src, tgt in data_dict["edges"]])
        return cls(graph)

    def dump(self) -> bytes:
        graph_dict = {
            "nodes": [{"name": vertex["name"]} for vertex in self._graph.vs],
            "edges": [
                (self._graph.vs[edge[0]]["name"], self._graph.vs[edge[1]]["name"])
                for edge in self._graph.get_edgelist()
            ],
        }
        return msgpack.packb(graph_dict, use_bin_type=True)

    def delete_vertices(self, keys: list[str]) -> None:
        self._graph.delete_vertices(keys)

    def __iter__(self) -> Generator[str]:
        yield from self.iterator()
