import copy
import json
import logging
from datetime import datetime
from functools import lru_cache
from pathlib import Path
from typing import cast

from prime_config_service_client import SecurityFramework as ConfigSecurityFramework
from sqlalchemy import inspect
from sqlalchemy.orm import NO_VALUE

from service.db import CaseTable, IssueAnalysisTable
from service.models import (
    ConcernType,
    ExternalFrameworkConcern,
    ExternalIssueAnalysis,
    ExternalIssueAnalysisWorkroom,
    ExternalPrimeConcern,
    Implementation,
    IssueAnalysisConcern,
    MitreTacticData,
    RiskFactors,
    SecurityControl,
    Summary5W,
)
from service.services_clients import ServicesClients

from .external_container import DEFAULT_SUMMARY
from .recommendation_builder import (
    build_concerns_with_external_framework_concerns,
    get_controls_dict,
    normalize_external_recommendation_framework_concerns,
    normalize_external_recommendation_prime_concerns,
)

LOGGER = logging.getLogger("customer_results")

DEFAULT_ASSESSMENT = "No assessment available"


@lru_cache
def get_linddun_options() -> list[str]:
    return [
        "Linkability",
        "Identifiability",
        "Non-repudiation",
        "Detectability",
        "Disclosure of information",
        "Unawareness",
        "Non-compliance",
    ]


@lru_cache
def get_mitre_data_dict() -> dict[str, MitreTacticData]:
    file_path = Path(__file__).parent.parent / "static-files" / "enterprise-attack-tactics-v15.1.json"
    with file_path.open("r") as f:
        mitre_dict = json.load(f)
    return {
        mitre_id: MitreTacticData(
            stix_id=mitre_tactics_data["STIX ID"],
            name=mitre_tactics_data["name"],
            description=mitre_tactics_data["description"],
            url=mitre_tactics_data["url"],
            created=datetime.strptime(mitre_tactics_data["created"], "%d %B %Y"),
            last_modified=datetime.strptime(mitre_tactics_data["last modified"], "%d %B %Y"),
            domain=mitre_tactics_data["domain"],
            version=mitre_tactics_data["version"],
        )
        for mitre_id, mitre_tactics_data in mitre_dict.items()
    }


@lru_cache
def get_mitre_name_to_id_mapping_dict() -> dict[str, str]:
    data_dict = get_mitre_data_dict()
    return {mitre_tactics_data.name: mitre_id for mitre_id, mitre_tactics_data in data_dict.items()}


def _process_concern_for_mitre_tactic(
    concern: IssueAnalysisConcern, mitre_tactic_data: dict[str, MitreTacticData]
) -> IssueAnalysisConcern:
    processed_concern = copy.deepcopy(concern)
    if (
        processed_concern.methodology.type == ConcernType.MITRE
        and processed_concern.methodology.category in mitre_tactic_data
    ):
        processed_concern.methodology.category = mitre_tactic_data[concern.methodology.category].name
    return processed_concern


def _init_concerns(
    concerns: list[IssueAnalysisConcern],
) -> tuple[dict[int, ExternalPrimeConcern], list[ExternalFrameworkConcern]]:
    mitre_tactic_data = get_mitre_data_dict()

    framework_concerns = []
    prime_concerns = {}
    for concern in concerns:
        processed_concern = _process_concern_for_mitre_tactic(concern, mitre_tactic_data)
        framework_concerns.append(
            ExternalFrameworkConcern(
                **processed_concern.model_dump(),
                controls=[],
            )
        )
        prime_concerns[processed_concern.id] = ExternalPrimeConcern(
            **processed_concern.model_dump(),
            recommendations=[],
        )
    return prime_concerns, framework_concerns


async def build_external_concerns(
    account_id: str,
    concerns: list[IssueAnalysisConcern] | None,
    controls: list[SecurityControl] | None,
    recommendations: list[Implementation] | None,
) -> tuple[dict[str, list[ExternalFrameworkConcern]], list[ExternalPrimeConcern]]:
    account_config = await ServicesClients.config_api().download_config_file(account_id)
    account_framework = account_config.security_framework or ConfigSecurityFramework.NIST

    controls_map = {control.id: control for control in (controls or [])}
    controls_map.update(get_controls_dict(account_framework))

    prime_concerns, framework_concerns = _init_concerns(concerns or [])

    build_concerns_with_external_framework_concerns(
        framework_concerns, controls_map, recommendations or [], prime_concerns, account_framework
    )

    framework_concerns = normalize_external_recommendation_framework_concerns(framework_concerns)
    prime_concerns = normalize_external_recommendation_prime_concerns(list(prime_concerns.values()))  # type: ignore[assignment]

    return {account_framework.value: framework_concerns}, prime_concerns  # type: ignore[return-value]


def _get_mitre_and_linddun_data(concerns: list[IssueAnalysisConcern] | None) -> tuple[list[str], list[str]]:
    if not concerns:
        return [], []

    mitre_tactic_data = get_mitre_data_dict()

    linddun = set()
    mitre = set()
    for concern in concerns:
        if concern.methodology.type == ConcernType.LINDDUN:
            linddun.add(concern.methodology.category)
        elif concern.methodology.type == ConcernType.MITRE and concern.methodology.category in mitre_tactic_data:
            mitre.add(mitre_tactic_data[concern.methodology.category].name)

    return list(mitre), list(linddun)


def get_external_issue_analysis_summary(
    case_db: CaseTable,
    issue_analysis_db: IssueAnalysisTable,
) -> ExternalIssueAnalysisWorkroom:
    risk_factors = _get_risk_factors(issue_analysis_db)
    mitre_categories, linddun_categories = _get_mitre_and_linddun_data(issue_analysis_db.concerns)
    result = ExternalIssueAnalysisWorkroom(
        account_id=case_db.account_id,
        source_id=case_db.source_id,
        issue_id=case_db.issue_id,
        risk_factors=risk_factors,
        confidence=issue_analysis_db.confidence,
        risk_score=issue_analysis_db.risk_score,
        is_automated=issue_analysis_db.is_automated,
        mitre_categories=mitre_categories,
        linddun_categories=linddun_categories,
        fire_summary=issue_analysis_db.fire_summary,
        issue_hash=issue_analysis_db.issue_hash,
    )
    return result


def get_external_issue_analysis(
    case: CaseTable,
    issue_analysis_db: IssueAnalysisTable,
) -> ExternalIssueAnalysis:
    issue_analysis_summary = get_external_issue_analysis_summary(case, issue_analysis_db)
    short_ai_summary, summary_5w = DEFAULT_SUMMARY, Summary5W.empty()
    if inspect(case).attrs.partial.loaded_value != NO_VALUE and case.partial and case.partial.has_summaries():  # type: ignore[union-attr]
        short_ai_summary = cast(str, case.partial.short)
        summary_5w = Summary5W.from_question(case.partial.questions)
    result = ExternalIssueAnalysis(
        **issue_analysis_summary.model_dump(),
        short_ai_summary=short_ai_summary,
        long_ai_summary_5w=summary_5w,
        long_assessment=issue_analysis_db.long_assessment or DEFAULT_ASSESSMENT,
        short_assessment=issue_analysis_db.short_assessment or DEFAULT_ASSESSMENT,
        issue_links=issue_analysis_db.issue_links or [],
        keywords=issue_analysis_db.keywords or [],
    )
    return result


def _get_risk_factors(issue_analysis: IssueAnalysisTable) -> RiskFactors:
    return RiskFactors(
        confidentiality=issue_analysis.confidentiality,
        confidentiality_explanation=issue_analysis.confidentiality_explanation,
        integrity=issue_analysis.integrity,
        integrity_explanation=issue_analysis.integrity_explanation,
        availability=issue_analysis.availability,
        availability_explanation=issue_analysis.availability_explanation,
        third_party_management=issue_analysis.third_party_management,
        third_party_management_explanation=issue_analysis.third_party_management_explanation,
        compliance=issue_analysis.compliance,
        compliance_explanation=issue_analysis.compliance_explanation,
        severity=issue_analysis.severity,
        severity_explanation=issue_analysis.severity_explanation,
        scope=issue_analysis.scope,
        scope_explanation=issue_analysis.scope_explanation,
    )
