from __future__ import annotations

import logging
from collections import defaultdict
from datetime import date, datetime
from typing import Any, Never, cast, override

from sqlalchemy import ColumnElement, text
from sqlmodel import func, literal, literal_column, select, union_all
from sqlmodel.sql.expression import Select

from service.db import CaseHistoryTable, CaseTable, IssueAnalysisTable
from service.logic.filters_and_sort import CaseFilters
from service.models import CaseAuditAction, CasesByStatusStats, CaseStatus, DatePoint, QueriesName

from .base_query import StatsBaseQuery

LOGGER = logging.getLogger("cases_by_status")

query_type = Select[tuple[Any, int, Never]]


class CaseByStatus(StatsBaseQuery[CasesByStatusStats]):
    __queryname__ = QueriesName.CASES_BY_STATUS

    @override
    async def get_stats(self, case_filters: CaseFilters | None = None) -> CasesByStatusStats:
        LOGGER.info("Getting daily cases by status stats from %s to %s", self._start, self._end)

        dates = self._get_datetime_buckets()
        results = await self._get_counts(dates, case_filters)
        scanned_stats_items = [DatePoint(x=d, y=results["scanned"][d]) for d in dates]
        identified_stats_items = [DatePoint(x=d, y=results["identified"][d]) for d in dates]
        closed_stats_items = [DatePoint(x=d, y=results["closed"][d]) for d in dates]
        stats_result = CasesByStatusStats(
            start=self._start,
            end=self._end,
            query_name=self.name,
            scanned=scanned_stats_items,
            identified=identified_stats_items,
            close=closed_stats_items,
        )
        return stats_result

    async def _get_counts(
        self, dates: list[datetime], case_filters: CaseFilters | None = None
    ) -> defaultdict[str, defaultdict[date, int]]:
        queries = []
        dates_union = union_all(*[select(literal(d).label("datetime")) for d in dates]).subquery()
        closed_statuses = [CaseStatus.DONE, CaseStatus.DISMISSED]

        latest_action_cte = (
            select(
                CaseHistoryTable.case_id.label("case_id"),  # type: ignore[attr-defined]
                func.jsonb_extract_path_text(CaseHistoryTable.audit_action_args, text("'new_status'")).label(
                    "new_status"
                ),
                func.max(CaseHistoryTable.created_at)
                .over(partition_by=[CaseHistoryTable.case_id])  # type: ignore[list-item]
                .label("latest_created_at"),
            )
            .where(
                CaseHistoryTable.audit_action == CaseAuditAction.update_status,
                CaseHistoryTable.account_id == self._account_id,
            )
            .cte("latest_action")
        )
        closed_query: Select[tuple[Any, int, Never]] = (
            select(
                dates_union.c.datetime.label("datetime"),
                func.count(cast(ColumnElement[Any], CaseTable.id)).label("count"),
                literal_column("'closed'").label("type"),
            )
            .join(
                latest_action_cte,
                latest_action_cte.c.case_id == CaseTable.id,
                isouter=True,  # Ensure all dates are included
            )
            .where(latest_action_cte.c.latest_created_at <= dates_union.c.datetime)
            .where(latest_action_cte.c.new_status.in_([status.value for status in closed_statuses]))
            .where(CaseTable.account_id == self._account_id)
            .where(CaseTable.deleted_at == None)  # noqa: E711
            .group_by(dates_union.c.datetime)  # Group by the date from the dates_table CTE
            .order_by(dates_union.c.datetime)  # Order by the date from the dates_table CTE
        )
        if case_filters:
            closed_query = cast(query_type, case_filters.build_query(closed_query))  # type: ignore[arg-type]
        queries.append(closed_query)

        identified_query: Select[tuple[Any, int, Never]] = (
            select(
                dates_union.c.datetime.label("datetime"),
                func.count(CaseTable.id).label("count"),  # type: ignore[arg-type]
                literal_column("'identified'").label("type"),
            )
            .join(IssueAnalysisTable, CaseTable.issue_analysis_id == IssueAnalysisTable.id)  # type: ignore[arg-type]
            .where(IssueAnalysisTable.classification == True)  # noqa: E712
            .where(IssueAnalysisTable.is_automated == False)  # noqa: E712
            .where(IssueAnalysisTable.is_security_enhancement == False)  # noqa: E712
            .where(CaseTable.account_id == self._account_id)
            .where(CaseTable.created_at <= dates_union.c.datetime)
            .where(CaseTable.deleted_at == None)  # noqa: E711
            .group_by(dates_union.c.datetime)
            .order_by(dates_union.c.datetime)
        )
        if case_filters:
            temp_query = case_filters.build_query(select(CaseTable))
            identified_query = identified_query.where(temp_query.whereclause)  # type: ignore[arg-type]
        queries.append(identified_query)

        scanned_query: Select[tuple[Any, int, Never]] = (
            select(
                dates_union.c.datetime.label("datetime"),
                func.count(CaseTable.id).label("count"),  # type: ignore[arg-type]
                literal_column("'scanned'").label("type"),
            )
            .where(CaseTable.account_id == self._account_id)
            .where(CaseTable.created_at <= dates_union.c.datetime)
            .where(CaseTable.deleted_at == None)  # noqa: E711
            .group_by(dates_union.c.datetime)
            .order_by(dates_union.c.datetime)
        )
        if case_filters:
            scanned_query = cast(query_type, case_filters.build_query(scanned_query))  # type: ignore[arg-type]
        queries.append(scanned_query)

        query = union_all(*queries).order_by("datetime", "type")
        results: defaultdict[str, defaultdict[date, int]] = defaultdict(lambda: defaultdict(lambda: 0))
        for row in await self._service_dal.session.exec(query):  # type: ignore[call-overload]
            results[row.type][row.datetime] = row.count
        return results
