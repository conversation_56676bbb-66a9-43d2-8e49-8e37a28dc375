from __future__ import annotations

import enum
import inspect
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import TYPE_CHECKING, Any, Generic, TypeVar

from prime_shared.common_types import AccountIdType
from sqlalchemy import CTE
from sqlmodel import select

from service.db import CaseTable, IssueAnalysisTable
from service.db.service_dal import ServiceDAL
from service.logic.filters_and_sort import CaseFilters
from service.models.statistics import BaseCustomerTrends, QueriesName

LOGGER = logging.getLogger(__name__)

CustomerTrends = TypeVar("CustomerTrends", bound=BaseCustomerTrends)


class BaseQuery(ABC, Generic[CustomerTrends]):
    if TYPE_CHECKING:
        __queryname__: QueriesName

    @property
    def name(self) -> str:
        return self.__queryname__.value

    @abstractmethod
    async def get_stats(self) -> CustomerTrends:
        raise NotImplementedError


class Granularity(enum.Enum):
    hour = enum.auto()
    day = enum.auto()
    week = enum.auto()
    month = enum.auto()


class StatsBaseQuery(BaseQuery[CustomerTrends], ABC):
    def __init_subclass__(cls, **kwargs: dict[str, Any]) -> None:
        super().__init_subclass__(**kwargs)
        if not inspect.isabstract(cls) and not hasattr(cls, "__queryname__"):
            raise TypeError(f"Can't instantiate subclass {cls.__queryname__} without __queryname__ attribute")

    def __init__(
        self,
        service_dal: ServiceDAL,
        account_id: AccountIdType,
        start: datetime,
        end: datetime,
        granularity: Granularity = Granularity.day,
    ) -> None:
        self._account_id = account_id
        self._start = start
        self._end = end
        self._granularity = granularity
        self._service_dal = service_dal
        self._chart_size = self._calculate_chart_size()
        ABC.__init__(self)

    def _calculate_chart_size(self) -> int:
        if self._granularity == Granularity.hour:
            return int((self._end - self._start).total_seconds() // 3600)
        if self._granularity == Granularity.day:
            return (self._end - self._start).days
        if self._granularity == Granularity.week:
            return (self._end - self._start).days // 7
        if self._granularity == Granularity.month:
            return (self._end - self._start).days // 30
        raise ValueError(f"Invalid granularity: {self._granularity}")

    def _get_datetime_buckets(self) -> list[datetime]:
        if self._start == self._end:
            return [self._start]
        count = self._chart_size
        if self._granularity == Granularity.hour:
            return [self._start + timedelta(hours=i) for i in range(max(count, 1))] + [self._end]
        if self._granularity == Granularity.day:
            return [self._start + timedelta(days=i) for i in range(max(count, 1))] + [self._end]
        if self._granularity == Granularity.week:
            return [self._start + timedelta(weeks=i) for i in range(max(count, 1))] + [self._end]
        if self._granularity == Granularity.month:
            return [self._start + timedelta(days=i * 30) for i in range(max(count, 1))] + [self._end]
        raise ValueError(f"Invalid granularity: {self._granularity}")

    def _create_cte(self, case_filters: CaseFilters | None = None) -> CTE:
        case_filters = case_filters or CaseFilters.default_workroom_filter()
        cte_subquery = select(CaseTable, IssueAnalysisTable).where(
            CaseTable.account_id == self._account_id,
            CaseTable.deleted_at.is_(None),  # type: ignore[union-attr]
        )
        case_filters.join_default_tables = True
        cte_subquery = case_filters.build_query(cte_subquery)  # type: ignore[assignment, arg-type]
        return cte_subquery.cte("cte_query")
