from __future__ import annotations

import logging
from abc import ABC

from sqlalchemy import func
from sqlalchemy.dialects.postgresql import <PERSON><PERSON>N<PERSON>
from sqlmodel import select

from service.logic.filters_and_sort import CaseFilters
from service.models import (
    RISK_SCORE_RANGES,
    CasesPerCategoryCount,
    MethodologyStats,
    QueriesName,
    RiskScoreCategory,
)

from ..issue_analysis import get_mitre_data_dict
from .base_query import StatsBaseQuery

LOGGER = logging.getLogger("cases_by_status")


class SecurityMethodologyStats(StatsBaseQuery[MethodologyStats], ABC):
    __queryname__: QueriesName = None  # type: ignore[assignment]

    async def get_stats(self, case_filters: CaseFilters | None = None) -> MethodologyStats:
        result = await self._get_methodology_stats(self.name, case_filters=case_filters)
        return MethodologyStats(start=self._start, end=self._end, query_name=self.name, categories=result)

    async def _get_methodology_stats(
        self, methodology_type: str, case_filters: CaseFilters | None
    ) -> dict[str, CasesPerCategoryCount]:
        cte_query = self._create_cte(case_filters=case_filters)

        concerns_extracted = (
            select(
                func.unnest(cte_query.c.concerns).cast(JSONB).label("concern_jsonb"),
                cte_query.c.risk_score,
            )
        ).subquery("concerns_extracted")

        risk_count_expressions = [
            func.count().filter(concerns_extracted.c.risk_score.in_(risk_range)).label(f"{category}_count")
            for category, risk_range in RISK_SCORE_RANGES.items()
        ]

        query = (
            select(
                func.jsonb_extract_path_text(concerns_extracted.c.concern_jsonb, "methodology", "category").label(
                    "category"
                ),
                *risk_count_expressions,
            )
            .select_from(concerns_extracted)
            .where(
                func.jsonb_extract_path_text(concerns_extracted.c.concern_jsonb, "methodology", "type")
                == methodology_type
            )
            .where(
                func.coalesce(
                    func.jsonb_extract_path_text(concerns_extracted.c.concern_jsonb, "methodology", "category"), ""
                )
                != ""
            )
            .group_by("category")
            .order_by("category")
        )
        rows = (await self._service_dal.session.exec(query)).all()
        columns = ["category"] + list(RISK_SCORE_RANGES.keys())
        data = {}
        for row in rows:
            risk_scores = {RiskScoreCategory(columns[i]): row[i] for i in range(1, len(columns))}
            data[row[0]] = CasesPerCategoryCount(risk_scores=risk_scores)
        return data


class SecurityMitreMethodologyStats(SecurityMethodologyStats):
    __queryname__ = QueriesName.MITRE

    async def get_stats(self, case_filters: CaseFilters | None = None) -> MethodologyStats:
        results = await super().get_stats(case_filters=case_filters)
        mitre_data = get_mitre_data_dict()
        results_by_name = {mitre_data[key].name: val for key, val in results.categories.items() if key in mitre_data}
        results.categories = results_by_name
        return results


class SecurityLinddunMethodologyStats(SecurityMethodologyStats):
    __queryname__ = QueriesName.LINDDUN
