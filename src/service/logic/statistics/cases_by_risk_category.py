from __future__ import annotations

import logging
from collections import defaultdict
from datetime import datetime
from typing import Any, override

from sqlalchemy import Select
from sqlmodel import case, func, literal, select, union_all

from service.logic.filters_and_sort import CaseFilters
from service.models import DatePoint, RiskScoreCategory, risk_category_to_score
from service.models.statistics import CasesByRiskCategoryStats, QueriesName

from .base_query import StatsBaseQuery

LOGGER = logging.getLogger("cases_by_risk_category")

query_type = Select[tuple[Any, Any, int]]


class CasesByRiskCategory(StatsBaseQuery[CasesByRiskCategoryStats]):
    __queryname__ = QueriesName.CASES_BY_RISK_CATEGORY

    @override
    async def get_stats(self, case_filters: CaseFilters | None = None) -> CasesByRiskCategoryStats:
        LOGGER.info("Getting cases by risk category stats from %s to %s", self._start, self._end)
        dates = self._get_datetime_buckets()
        results = await self._get_counts(dates, case_filters)

        intervene_stats_items = [DatePoint(x=d, y=results[RiskScoreCategory.INTERVENE][d]) for d in dates]
        analyze_stats_items = [DatePoint(x=d, y=results[RiskScoreCategory.ANALYZE][d]) for d in dates]
        monitor_stats_items = [DatePoint(x=d, y=results[RiskScoreCategory.MONITOR][d]) for d in dates]

        stats_result = CasesByRiskCategoryStats(
            start=self._start,
            end=self._end,
            query_name=self.name,
            intervene=intervene_stats_items,
            analyze=analyze_stats_items,
            monitor=monitor_stats_items,
        )
        return stats_result

    async def _get_counts(
        self, dates: list[datetime], case_filters: CaseFilters | None
    ) -> defaultdict[str, defaultdict[datetime, int]]:
        cte_query = self._create_cte(case_filters=case_filters)

        dates_union = union_all(*[select(literal(d).label("datetime")) for d in dates]).subquery()

        db_ranges = []
        for level in [RiskScoreCategory.INTERVENE, RiskScoreCategory.ANALYZE, RiskScoreCategory.MONITOR]:
            level_range = risk_category_to_score(level)
            db_ranges.append((cte_query.c.risk_score.between(level_range.start, level_range.stop), level))
        level_ranges = case(*db_ranges).label("level")

        query: query_type = (
            select(level_ranges, func.count(cte_query.c.id).label("count"), dates_union.c.datetime.label("datetime"))  # type: ignore[assignment]
            .select_from(cte_query)
            .where(cte_query.c.created_at <= dates_union.c.datetime)
            .group_by(level_ranges, dates_union.c.datetime)
            .order_by(level_ranges, dates_union.c.datetime)
        )
        results: defaultdict[str, defaultdict[datetime, int]] = defaultdict(lambda: defaultdict(lambda: 0))
        for row in await self._service_dal.session.exec(query):  # type: ignore[call-overload]
            results[row.level][row.datetime] = row.count
        return results
