from collections.abc import Sequence
from typing import cast

from service.logic.jira_manager import JiraPrimeIssue
from service.models.cases import CaseData


def to_case_data_response(data: Sequence[tuple[int, int, str, dict]]) -> dict[int, CaseData]:  # type: ignore[type-arg]
    result = {}
    for case_id, source_id, issue_id, provider_fields in data:
        issue_self = cast(str, provider_fields.get("self"))
        jira_issue_url = JiraPrimeIssue.get_ui_weblink(issue_self, issue_id)
        result[case_id] = CaseData(id=case_id, source_id=source_id, issue_id=issue_id, jira_issue_url=jira_issue_url)

    return result
