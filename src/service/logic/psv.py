import logging
from collections import defaultdict
from collections.abc import Sequence
from datetime import datetime
from typing import Any, cast

from prime_service_kit.shared_types import AccountArgumentType
from prime_shared.common_dataclasses import PaginationArgs

from service.db import CaseTable, PsvTable, ServiceDAL
from service.logic.filters_and_sort import PSVFilters
from service.logic.jira_manager import JiraPrimeIssue
from service.models import PotentialSecurityViolation
from service.models.filters_and_sort import SortField

LOGGER = logging.getLogger("psv")


async def get_external_psvs(
    account_id: AccountArgumentType,
    service_dal: ServiceDAL,
    psv_filters: PSVFilters,
    pagination_args: PaginationArgs,
    sort_args: list[SortField] | None = None,
) -> list[PotentialSecurityViolation]:
    pav_table = await service_dal.psv_dal.get_psvs_by(
        account_id, psv_filters=psv_filters, pagination_args=pagination_args, sort_args=sort_args
    )
    return await external_psv_from_tables(service_dal, account_id, pav_table)


async def external_psv_from_tables(
    service_dal: ServiceDAL, account_id: AccountArgumentType, psv_tables: Sequence[PsvTable]
) -> list[PotentialSecurityViolation]:
    LOGGER.info("Building external psv for %s psv", len(psv_tables))
    psv_tables_by_source = defaultdict(list)
    for psv_table in psv_tables:
        psv_tables_by_source[psv_table.source_id].append(psv_table)
    cases_provider_fields = {}
    for source_id, items in psv_tables_by_source.items():
        issue_ids = [psv_table.issue_id for psv_table in items]
        cases = await service_dal.cases_dal.get_cases_by(
            account_id=account_id,
            source_id=source_id,
            issues=issue_ids,
            entities=[CaseTable.issue_id, CaseTable.provider_fields],
        )
        cases_provider_fields.update({case[0]: case[1] for case in cases})
    results = [
        psv_from_table(psv_table, provider_fields)
        for psv_table in psv_tables
        if (provider_fields := cases_provider_fields.get(psv_table.issue_id))
    ]
    return results


def psv_from_table(psv_table: PsvTable, case_provider_fields: dict[str, Any]) -> PotentialSecurityViolation:
    return PotentialSecurityViolation(
        psv_id=psv_table.id,
        description=psv_table.description,
        type=psv_table.type,
        source_id=psv_table.source_id,
        issue_id=psv_table.issue_id,
        status=psv_table.status,
        dismissed_reason=psv_table.dismissed_reason,
        project=case_provider_fields["project"],
        reporter=case_provider_fields.get("reporter"),
        created_at=case_provider_fields["created"],
        detection_date=cast(datetime, psv_table.created_at),
        title=case_provider_fields["summary"],
        issue_link=JiraPrimeIssue.get_ui_weblink(case_provider_fields["self"], psv_table.issue_id),
    )
