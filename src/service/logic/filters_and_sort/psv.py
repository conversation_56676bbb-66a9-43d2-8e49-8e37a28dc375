from __future__ import annotations

import logging
from enum import Enum
from typing import TypeVar, cast

from prime_db_utils import BaseTableSQLModel
from sqlalchemy import ColumnExpressionArgument, and_
from sqlmodel.sql.expression import SelectOfScalar

from service.db import CaseTable, PsvTable
from service.models.filters_and_sort import BaseFilters, Filter, Operator

from .cases import CaseFilters

LOGGER = logging.getLogger("filtering")

T = TypeVar("T", bound=Enum)


class PSVFilters(BaseFilters[PsvTable]):
    ALLOWED_FILTERS = ["status", "id", "issue_id", "created_at", "type", "provider_fields", "has_psv"]
    CUSTOM_FILTERS = ["provider_fields"]

    TABLES = [PsvTable, CaseTable]
    QUERY_TABLE = PsvTable
    HAS_PSV_FILTER = Filter(field="has_psv", value="true")

    JOIN: dict[type[BaseTableSQLModel], ColumnExpressionArgument[bool]] = {
        CaseTable: cast(
            "ColumnExpressionArgument[bool]",
            and_(
                cast("ColumnExpressionArgument[bool]", PsvTable.account_id == CaseTable.account_id),
                cast("ColumnExpressionArgument[bool]", PsvTable.source_id == CaseTable.source_id),
                cast("ColumnExpressionArgument[bool]", PsvTable.issue_id == CaseTable.issue_id),
            ),
        ),
    }

    @classmethod
    def base_filter(cls, filters: list[Filter] | None = None) -> PSVFilters:
        psv_filters = cls().has_psv().add_filters(filters or [])
        psv_filters.join_default_tables = True
        return psv_filters

    def has_psv(self) -> PSVFilters:
        return PSVFilters(self._filters).add_filter(self.HAS_PSV_FILTER)

    def _custom_filter_provider_fields(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._handle_filter_enum(_filter, query)

    def _handle_filter_enum(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        if _filter.op == Operator.EXIST:
            return CaseFilters._provider_fields_build_exist_query(_filter, query)
        if _filter.op in [Operator.EQ, Operator.NE]:
            value = [f"'{value}'" for value in _filter.sql_list_value]
            return CaseFilters._provider_fields_build_equal_query(_filter, query, value)
        LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
        return query
