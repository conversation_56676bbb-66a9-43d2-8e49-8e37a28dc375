from __future__ import annotations

import json
import logging
from collections.abc import Callable
from datetime import datetime
from enum import Enum
from typing import TypeVar, cast

from dateutil.parser import parse as date_parse
from prime_db_utils import BaseTableSQLModel
from sqlalchemy import ColumnElement, ColumnExpressionArgument, and_, column, exists, select
from sqlalchemy.orm import aliased
from sqlmodel import func, or_, text
from sqlmodel.sql.expression import Select, SelectOfScalar

from service.db import PROVIDERS_FIELDS_COLUMN_NAME, CasesDAL, CaseTable, IssueAnalysisTable
from service.models import (
    CaseStatus,
    ConcernType,
    ConfidenceScoreLevel,
    ProviderFieldType,
    RiskFactorLevel,
    RiskScoreCategory,
    confidence_level_to_score,
    risk_category_to_score,
    risk_factor_level_to_score,
)
from service.models.filters_and_sort import BaseFilters, Filter, Operator

LOGGER = logging.getLogger("filtering")

CONTAINER_FILTER_NAME = "container"

OPERATOR_TO_ARITHMETIC = {
    Operator.EQ: "=",
    Operator.NE: "!=",
    Operator.GT: ">",
    Operator.GTE: ">=",
    Operator.LT: "<",
    Operator.LTE: "<=",
}

T = TypeVar("T", bound=Enum)


class CaseFilters(BaseFilters[CaseTable]):
    ALLOWED_FILTERS = [
        "id",
        "status",
        "title",
        "classification",
        "source_id",
        "confidentiality",
        "integrity",
        "availability",
        "confidentiality_level",
        "integrity_level",
        "availability_level",
        "third_party_management",
        "compliance",
        "risk_score_category",
        "risk_score",
        "since_date",
        "confidence_level",
        PROVIDERS_FIELDS_COLUMN_NAME,
        "issue_id",
        "issue_id_exact",
        "is_automated",
        "is_security_enhancement",
        "labels",
        "parent_id",
        "mitre_categories",
        "linddun_categories",
        "fire_summary",
        "active_sprint",
        CONTAINER_FILTER_NAME,
        "progress_percentage",
        "has_parent",
    ]

    STATUS_OPEN_FILTER = Filter(field="status", value=CaseStatus.OPEN.value)
    IS_SECURITY_FILTER = Filter(field="classification", value="true")
    NOT_AUTOMATED_FILTER = Filter(field="is_automated", value="false")
    NOT_SECURITY_ENHANCEMENT = Filter(field="is_security_enhancement", value="false")
    IS_CONTAINER_FILTER = Filter(field=CONTAINER_FILTER_NAME, value="true")
    NOT_CONTAINER_FILTER = Filter(field=CONTAINER_FILTER_NAME, value="false")
    TABLES = [CaseTable, IssueAnalysisTable]
    QUERY_TABLE = CaseTable

    JOIN: dict[type[BaseTableSQLModel], ColumnExpressionArgument[bool]] = {
        IssueAnalysisTable: cast(
            "ColumnExpressionArgument[bool]", CaseTable.issue_analysis_id == IssueAnalysisTable.id
        ),
    }
    CUSTOM_FILTERS = [
        "risk_score_category",
        PROVIDERS_FIELDS_COLUMN_NAME,
        "since_date",
        "confidence_level",
        "confidentiality_level",
        "integrity_level",
        "availability_level",
        "title",
        "issue_id",
        "issue_id_exact",
        "labels",
        "parent_id",
        "mitre_categories",
        "linddun_categories",
        "fire_summary",
        CONTAINER_FILTER_NAME,
        "active_sprint",
        "has_parent",
    ]
    ALLOWED_DUP_FILTERS = ["labels"]

    @classmethod
    def default_workroom_filter(cls, filters: list[Filter] | None = None) -> CaseFilters:
        return cls().open().security().not_automated().not_enhancement().not_container().add_filters(filters or [])

    @classmethod
    def base_workroom_filter(cls, filters: list[Filter] | None = None) -> CaseFilters:
        return cls().open().security().not_container().add_filters(filters or [])

    @classmethod
    def base_container_filter(cls, filters: list[Filter] | None = None) -> CaseFilters:
        return cls().open().is_container().add_filters(filters or [])

    def security(self) -> CaseFilters:
        return CaseFilters(self._filters).add_filter(self.IS_SECURITY_FILTER)

    def is_container(self) -> CaseFilters:
        return CaseFilters(self._filters).add_filter(self.IS_CONTAINER_FILTER)

    def not_container(self) -> CaseFilters:
        return CaseFilters(self._filters).add_filter(self.NOT_CONTAINER_FILTER)

    def not_automated(self) -> CaseFilters:
        return CaseFilters(self._filters).add_filter(self.NOT_AUTOMATED_FILTER)

    def parent_id(self, parent_id: str) -> CaseFilters:
        return CaseFilters(self._filters).add_filter(Filter(field="parent_id", value=parent_id))

    def not_enhancement(self) -> CaseFilters:
        return CaseFilters(self._filters).add_filter(self.NOT_SECURITY_ENHANCEMENT)

    def open(self) -> CaseFilters:
        return CaseFilters(self._filters).add_filter(self.STATUS_OPEN_FILTER)

    def build_query(  # type: ignore[override]
        self, q: SelectOfScalar[CaseTable] | Select[CaseTable]
    ) -> SelectOfScalar[CaseTable] | Select[CaseTable]:
        return cast(SelectOfScalar[CaseTable] | Select[CaseTable], self._generate_query(q))

    def _custom_filter_confidentiality_level(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._score_query_generic(
            _filter, query, RiskFactorLevel, risk_factor_level_to_score, IssueAnalysisTable.confidentiality
        )

    def _custom_filter_container(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        LOGGER.debug("Filtering by container: [%s]", _filter)

        if _filter.op not in [Operator.EQ]:
            LOGGER.warning("Invalid operator for container filter: [%s]", _filter.op)
            return query

        cases_alias = aliased(CaseTable)
        custom_condition = and_(
            cases_alias.account_id == CaseTable.account_id,  # type: ignore[arg-type]
            cases_alias.source_id == CaseTable.source_id,  # type: ignore[arg-type]
            cases_alias.parent_issue_id == CaseTable.issue_id,  # type: ignore[arg-type]
        )
        return self._handle_boolean_filter(_filter, query, exists().where(custom_condition))

    def _custom_filter_active_sprint(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        LOGGER.info("Filtering by active sprint: %s", _filter)

        value_to_filter = _filter.value
        if isinstance(_filter.value, list):
            value_to_filter = _filter.value[0]

        LOGGER.debug("Filter value for active sprint [%s]", value_to_filter)

        condition = exists(
            select(1)
            .select_from(func.jsonb_array_elements(CaseTable.provider_fields["sprint"]).alias("sprint"))
            .where(column("sprint").op("->>")("state") == "active")
        )
        query = self._handle_boolean_filter(_filter, query, condition)

        if query._from_obj is None or not query._from_obj:
            query = query.select_from(CaseTable)
        return query

    def _custom_filter_fire_summary(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        LOGGER.info("Filtering by fire_summary: %s", _filter)

        if _filter.op not in [Operator.EQ, Operator.NE]:
            LOGGER.warning("Invalid operator for fire_summary: [%s]", _filter.op)
            return query

        value_to_filter = _filter.value
        if isinstance(_filter.value, list):
            value_to_filter = _filter.value[0]

        LOGGER.debug("Filter value for fire_summary [%s]", value_to_filter)
        condition = (
            IssueAnalysisTable.fire_summary.ilike(f"%{value_to_filter}%")  # type: ignore[union-attr]
            if _filter.op == Operator.EQ
            else IssueAnalysisTable.fire_summary.notilike(f"%{value_to_filter}%")  # type: ignore[union-attr]
        )
        self._enable_table_to_join(IssueAnalysisTable, [condition])
        if query._from_obj is None or not query._from_obj:
            query = query.select_from(CaseTable)
        return query

    def _custom_filter_linddun_categories(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._filter_concern_methodology(ConcernType.LINDDUN, _filter, query)

    def _custom_filter_mitre_categories(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        from ..issue_analysis import get_mitre_name_to_id_mapping_dict

        def _parse_tactic_name_to_tactic_ids(
            values: str | list[str] | None, mitre_name_to_id: dict[str, str]
        ) -> str | list[str] | None:
            if not values or not isinstance(values, list):
                return values
            result = []
            for value in values:
                if value in mitre_name_to_id:
                    result.append(mitre_name_to_id[value])
                else:
                    result.append(value)
            return result

        mitre_name_to_id = get_mitre_name_to_id_mapping_dict()
        _filter.value = _parse_tactic_name_to_tactic_ids(_filter.value, mitre_name_to_id)
        return self._filter_concern_methodology(ConcernType.MITRE, _filter, query)

    def _custom_filter_has_parent(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        parent_condition = ~CaseTable.parent_issue_id.is_(None)  # type: ignore[union-attr]
        return self._handle_boolean_filter(_filter, query, parent_condition)

    def _custom_filter_integrity_level(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._score_query_generic(
            _filter, query, RiskFactorLevel, risk_factor_level_to_score, IssueAnalysisTable.integrity
        )

    def _custom_filter_availability_level(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._score_query_generic(
            _filter, query, RiskFactorLevel, risk_factor_level_to_score, IssueAnalysisTable.availability
        )

    def _custom_filter_title(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        _filter = Filter(
            field="provider_fields",
            value=_filter.value,
            inner_field="summary",
            inner_field_type=ProviderFieldType.STRING,
            op=_filter.op,
        )
        return self._custom_filter_provider_fields(_filter, query)

    def _custom_filter_issue_id_exact(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return query.where(CaseTable.issue_id.in_(_filter.list_value))  # type: ignore[attr-defined]

    def _custom_filter_issue_id(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        if _filter.op not in [Operator.EQ]:
            LOGGER.warning("Invalid operator for issue_id: %s", _filter.op)
            return query
        if _filter.is_val_list:
            query = query.where(CaseTable.issue_id.in_(_filter.list_value))  # type: ignore[attr-defined]
        elif value := _filter.str_value:
            if value.isdigit():
                query = query.where(CaseTable.issue_id.endswith(f"-{value}"))
            elif "-" not in value or value.endswith("-"):
                query = query.where(func.lower(CaseTable.issue_id).startswith(value.lower()))
            else:
                query = query.where(func.lower(CaseTable.issue_id) == value.lower())
        return query

    def _custom_filter_confidence_level(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._score_query_generic(
            _filter, query, ConfidenceScoreLevel, confidence_level_to_score, IssueAnalysisTable.confidence
        )

    def _custom_filter_parent_id(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        if _filter.op == Operator.EQ and _filter.str_value:
            hierarchy_level = next((_f.str_value for _f in self.filters_list if _f.field == "hierarchy_level"), None)
            max_depth = int(hierarchy_level) if hierarchy_level else 20
            cte = CasesDAL.get_children_hierarchy_cte(_filter.str_value, max_depth=max_depth)
            query = query.join(cte, CaseTable.id == cte.c.id)  # type: ignore[arg-type]
        return query

    def _custom_filter_risk_score_category(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._score_query_generic(
            _filter, query, RiskScoreCategory, risk_category_to_score, IssueAnalysisTable.risk_score
        )

    def _custom_filter_since_date(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        if _filter.str_value is None:
            return query
        from_date = _filter.str_value
        try:
            from_date_obj = date_parse(from_date)
        except ValueError:
            LOGGER.warning("Invalid date format: %s", _filter.value)
            return query
        if _filter.op == Operator.GT:
            statment = cast(datetime, IssueAnalysisTable.created_at) > from_date_obj
        elif _filter.op == Operator.GTE:
            statment = cast(datetime, IssueAnalysisTable.created_at) >= from_date_obj
        elif _filter.op == Operator.EQ:
            statment = func.date(cast(datetime, IssueAnalysisTable.created_at)) == func.date(from_date_obj)  # type: ignore[assignment]
        else:
            LOGGER.warning("Invalid operator for from_date:%s", _filter.op)
            return query
        condition = cast("ColumnExpressionArgument[bool]", IssueAnalysisTable.created_at and statment)
        self._enable_table_to_join(IssueAnalysisTable, [condition])
        return query

    def _custom_filter_provider_fields(  # noqa: PLR0911
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if _filter.inner_field is None or _filter.inner_sql_type is None:
            raise ValueError("Custom inner field and type must be provided for provider_fields filtering")

        if not _filter.list_value:
            LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
            return query

        if _filter.inner_field_type == ProviderFieldType.DATE:
            return self._provider_fields_handle_filter_date(_filter, query)
        elif _filter.inner_field_type == ProviderFieldType.STRING:
            return self._provider_fields_handle_filter_string(_filter, query)
        elif _filter.inner_field_type == ProviderFieldType.BOOLEAN:
            return self._provider_fields_handle_filter_boolean(_filter, query)
        elif _filter.inner_field_type == ProviderFieldType.NUMBER:
            return self._provider_fields_handle_filter_number(_filter, query)
        elif _filter.inner_field_type == ProviderFieldType.ARRAY:
            return self._provider_fields_handle_filter_array(_filter, query)
        elif _filter.inner_field_type == ProviderFieldType.ENUM:
            return self._provider_fields_handle_filter_enum(_filter, query)

        LOGGER.warning("Invalid type for provider_fields: %s", _filter.inner_field_type)
        return query

    def _custom_filter_labels(self, _filter: Filter, query: SelectOfScalar[CaseTable]) -> SelectOfScalar[CaseTable]:
        if _filter.op not in [Operator.EQ, Operator.NE, Operator.EXIST]:
            LOGGER.warning("Invalid operator for labels: %s", _filter.op)
            return query
        if _filter.op in [Operator.EQ, Operator.NE]:
            if not _filter.list_value:
                LOGGER.warning("Invalid value for labels: %s", _filter.value)
                return query
            where_query = CaseTable.labels.overlap(_filter.list_value)  # type: ignore[attr-defined]
            query = query.where(where_query) if _filter.op == Operator.EQ else query.where(~where_query)
        elif _filter.op == Operator.EXIST:
            exist_query: ColumnElement[bool] = func.array_length(CaseTable.labels, 1) != None  # noqa: E711
            query = self._handle_boolean_filter(_filter, query, exist_query)
        return query

    def _handle_boolean_filter(
        self, _filter: Filter, query: SelectOfScalar[CaseTable], condition: ColumnElement[bool]
    ) -> SelectOfScalar[CaseTable]:
        value_to_filter = _filter.value
        if isinstance(_filter.value, list):
            if len(_filter.value) != 1:
                LOGGER.warning("An array with both True and False: [%s]", _filter.value)
                return query
            value_to_filter = _filter.value[0]
        boolean_value = json.loads(value_to_filter.lower())  # type: ignore[union-attr]
        LOGGER.debug("Filter value by [%s]", value_to_filter)
        return query.where(condition) if boolean_value else query.where(~condition)

    def _score_query_generic(
        self,
        _filter: Filter,
        query: SelectOfScalar[CaseTable],
        level_enum: type[T],
        range_function: Callable[[T], range],
        column: int | None,
    ) -> SelectOfScalar[CaseTable]:
        if not _filter.list_value:
            return query
        try:
            conditions = []
            for value in _filter.list_value:
                risk_category = level_enum(value)
                score_range = range_function(risk_category)
                if score_range:
                    conditions.append((column >= score_range.start) & (column < score_range.stop))  # type: ignore[operator]

            combined_condition = or_(*conditions)
            query = query.where(combined_condition)
            self.join_default_tables = True
        except ValueError:
            LOGGER.exception("Invalid filter %s for %s", _filter, column)
        return query

    def _provider_fields_handle_filter_date(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if _filter.op in [Operator.EQ, Operator.NE]:
            LOGGER.warning("Invalid operator for provider_fields: %s", _filter.op)
            return query
        return self._provider_fields_build_comparable_filter(_filter, query)

    def _provider_fields_handle_filter_number(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        return self._provider_fields_build_comparable_filter(_filter, query)

    def _provider_fields_handle_filter_string(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if _filter.op == Operator.EXIST:
            return self._provider_fields_build_exist_query(_filter, query)
        if _filter.op in [Operator.EQ, Operator.NE]:
            op = "ILIKE" if _filter.op == Operator.EQ else "NOT ILIKE"
            value = [f"'%{value}%'" for value in _filter.sql_list_value]
            return self._provider_fields_build_equal_query(_filter, query, value, op)
        LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
        return query

    def _provider_fields_handle_filter_boolean(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if _filter.op == Operator.EXIST:
            return self._provider_fields_build_exist_query(_filter, query)
        if _filter.op in [Operator.EQ, Operator.NE] and len(_filter.list_value) == 1:
            return self._provider_fields_build_equal_query(
                _filter, query, [str(value) for value in _filter.sql_list_value]
            )
        LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
        return query

    def _provider_fields_handle_filter_array(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if _filter.op == Operator.EXIST:
            return self._provider_fields_build_exist_query(_filter, query)
        if _filter.op not in [Operator.EQ, Operator.NE]:
            LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
            return query
        field_definition = self._provider_fields_get_field_definition(_filter)
        array_values = ",".join([f"'{value}'" for value in _filter.sql_list_value])
        where_part = f"elem::text = ANY (ARRAY[{array_values}])"
        from_part = f"jsonb_array_elements_text({field_definition}) as elem"
        select_subquery = select(1).select_from(text(from_part)).where(text(where_part))
        full_where_subquery = select_subquery.exists() if _filter.op == Operator.EQ else ~select_subquery.exists()
        return query.where(full_where_subquery)

    def _provider_fields_handle_filter_enum(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if _filter.op == Operator.EXIST:
            return self._provider_fields_build_exist_query(_filter, query)
        if _filter.op in [Operator.EQ, Operator.NE]:
            value = [f"'{value}'" for value in _filter.sql_list_value]
            return self._provider_fields_build_equal_query(_filter, query, value)
        LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
        return query

    def _provider_fields_build_comparable_filter(
        self, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if _filter.op == Operator.EXIST:
            return self._provider_fields_build_exist_query(_filter, query)
        if _filter.op == Operator.BETWEEN:
            if len(_filter.list_value) != 2:
                LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
                return query
            val_a, val_b = _filter.list_value
            return query.where(
                text(f"{self._provider_fields_get_field_definition(_filter)} BETWEEN '{val_a}' AND '{val_b}'")
            )
        if _filter.op in [Operator.EQ, Operator.NE]:
            return self._provider_fields_build_equal_query(
                _filter, query, [str(value) for value in _filter.sql_list_value]
            )
        else:
            return self._provider_fields_build_order_query(_filter, query)

    @classmethod
    def _provider_fields_build_order_query(
        cls, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if len(_filter.list_value) != 1 or _filter.op not in [Operator.LT, Operator.GTE, Operator.GT, Operator.LTE]:
            LOGGER.warning("Invalid operator for provider_fields: %s", _filter.op)
            return query
        op = OPERATOR_TO_ARITHMETIC[_filter.op]
        return query.where(text(f"{cls._provider_fields_get_field_definition(_filter)} {op} '{_filter.str_value}'"))

    @classmethod
    def _provider_fields_build_equal_query(
        cls, _filter: Filter, query: SelectOfScalar[CaseTable], values: list[str], op: str | None = None
    ) -> SelectOfScalar[CaseTable]:
        if _filter.op not in [Operator.EQ, Operator.NE]:
            LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
            return query
        field_definition = cls._provider_fields_get_field_definition(_filter)
        exist_text = f"{PROVIDERS_FIELDS_COLUMN_NAME} ? '{_filter.inner_field}'"
        if op is None:
            op = OPERATOR_TO_ARITHMETIC[_filter.op]
        value = ",".join(values)
        value = f"ANY(ARRAY[{value}])" if _filter.op == Operator.EQ else f"ALL(ARRAY[{value}])"

        # For not equals (NE), include rows where field doesn't exist
        if _filter.op == Operator.NE:
            return query.where(text(f"(NOT ({exist_text}) OR {field_definition} {op} {value})"))
        # For equals (EQ), original logic
        return query.where(text(f"{field_definition} {op} {value}"))

    @classmethod
    def _provider_fields_build_exist_query(
        cls, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        field_definition = cls._provider_fields_get_field_definition(_filter)
        if _filter.inner_field_type == ProviderFieldType.ARRAY:
            not_empty_check = f"jsonb_array_length({field_definition}) > 0"
        else:
            not_empty_check = f"{field_definition} != ''"
        column_exist_text = f"{PROVIDERS_FIELDS_COLUMN_NAME}?'{_filter.inner_field}'"
        non_null_text = f"{field_definition} != 'null' AND {field_definition} IS NOT NULL"
        exist_text = f"{column_exist_text} AND {non_null_text} AND {not_empty_check}"
        if _filter.str_value and _filter.str_value.lower() == "true":
            return query.where(text(f"{exist_text}"))
        elif _filter.str_value and _filter.str_value.lower() == "false":
            return query.where(text(f"NOT ({exist_text})"))
        LOGGER.warning("Invalid value for provider_fields: %s", _filter.value)
        return query

    @classmethod
    def _provider_fields_get_field_definition(cls, _filter: Filter) -> str:
        op = "->" if _filter.inner_field_type == ProviderFieldType.ARRAY else "->>"
        query = f"({PROVIDERS_FIELDS_COLUMN_NAME}{op}'{_filter.inner_field}')"
        if _filter.inner_field_type != ProviderFieldType.ARRAY:
            query = f"{query}::{_filter.inner_sql_type}"
        return query

    def _filter_concern_methodology(
        self, _type: ConcernType, _filter: Filter, query: SelectOfScalar[CaseTable]
    ) -> SelectOfScalar[CaseTable]:
        if not _filter.list_value:
            LOGGER.warning("Invalid value for mitre_categories: %s", _filter.value)
            return query

        operation_to_query = {
            Operator.EQ: lambda q: q.exists(),
            Operator.NE: lambda q: ~q.exists(),
        }

        subquery = (
            select(1)
            .select_from(func.unnest(IssueAnalysisTable.concerns).alias("concern"))
            .where(
                and_(
                    text("concern->>'methodology' IS NOT NULL"),
                    text(f"(concern->>'methodology')::json->>'type' = '{_type.value}'"),
                    text(f"(concern->>'methodology')::json->>'category' = ANY(ARRAY[{_filter.value}])"),
                )
            )
        )
        condition = cast(
            "ColumnExpressionArgument[bool]",
            IssueAnalysisTable.concerns.is_not(None) & operation_to_query[_filter.op](subquery),  # type: ignore[union-attr]
        )
        self._enable_table_to_join(IssueAnalysisTable, [condition])
        if query._from_obj is None or not query._from_obj:
            query = query.select_from(CaseTable)
        return query
