import logging

from fastapi import BackgroundTasks
from prime_events import EventMessage, EventNotifier, EventStatus
from prime_gen_ai_service_client import (
    CeleryHeaders,
    PersonalRecommendationsTaskInput,
    PersonalRecommendationsTaskOutput,
)
from prime_gen_ai_service_client import (
    Concern as GenAIConcern,
)
from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import AccountIdType, SourceIdType

from service.db import ServiceDAL
from service.k8s_jobs.base_job_logic import GenAIBaseJob
from service.k8s_jobs.base_job_logic.gen_ai_celery import GENAI_PIPELINES_QUEUE, celery_manager_instance
from service.k8s_jobs.classification_job.task_helpers import get_recommendations_and_controls
from service.logic.issues_graph import GraphGenerator
from service.logic.jira_manager import JiraIssuesManager
from service.models import Implementation, IssueAnalysisConcern, SecurityControl

TASK_NAME = "genai-service.ai.personal_recommendations"
RESULTS_TASK_NAME = "rat-logic.results"
LOGGER = logging.getLogger("recommendation_on_demand")

HIGHEST_PRIORITY = 10


class NoPersonalRecommendationsFound(Exception):
    pass


class NoConcernsForCase(Exception):
    pass


class NoMatchForCaseConcernsToRequestedConcernIds(Exception):
    pass


async def _get_result_from_ai_task(  # noqa: PLR0913
    service_dal: ServiceDAL,
    account_id: AccountIdType,
    source_id: SourceIdType,
    issue_id: str,
    concerns: list[IssueAnalysisConcern],
    user_id: str,
    hashed_request_parameters: str,
    redis_client: AsyncPrefixRedisClient,
) -> PersonalRecommendationsTaskOutput:
    issue_manager = JiraIssuesManager(account_id, source_id)
    graph = await GraphGenerator(service_dal, account_id, source_id).load(redis_client, issue_id)
    issue_data = await issue_manager.get_issue(issue_id, graph)

    context_issue = await GenAIBaseJob.get_context_issue_from_db(issue_data, service_dal, account_id, source_id)

    input_args = PersonalRecommendationsTaskInput(
        context_issue=context_issue,
        concern_list=[
            GenAIConcern(group_name=concern.short_description, concern_text=concern.long_description)
            for concern in concerns
        ],
    )
    personal_recommendation_result = await celery_manager_instance.send_and_wait_for_result(
        priority=HIGHEST_PRIORITY,
        input_args=input_args.to_dict(),
        task_id=hashed_request_parameters,
        headers=CeleryHeaders(
            issue_id=issue_id,
            account_id=account_id,
            source_id=source_id,
            job_id=-1,
            user_id=user_id,
            results_queue_name=RESULTS_TASK_NAME,
            additional_properties={},
        ),
        task_name=TASK_NAME,
        queue=GENAI_PIPELINES_QUEUE,
    )
    personal_recommendation_output = PersonalRecommendationsTaskOutput(**personal_recommendation_result)
    LOGGER.info("Personal recommendations output: %s", personal_recommendation_output.model_dump())

    return personal_recommendation_output


async def _get_new_recommendations_and_controls(  # noqa: PLR0913
    service_dal: ServiceDAL,
    account_id: AccountIdType,
    source_id: SourceIdType,
    issue_id: str,
    concern_ids: set[int],
    user_id: str,
    hashed_request_parameters: str,
    redis_client: AsyncPrefixRedisClient,
) -> tuple[list[SecurityControl], list[Implementation]]:
    concerns = await _get_concerns_data(service_dal, account_id, source_id, issue_id, concern_ids)

    personal_recommendation_output = await _get_result_from_ai_task(
        service_dal, account_id, source_id, issue_id, concerns, user_id, hashed_request_parameters, redis_client
    )

    if (
        not personal_recommendation_output.results
        or not personal_recommendation_output.results.personal_recommendations
    ):
        LOGGER.error("No personal recommendations found for issue_id %s", issue_id)
        raise NoPersonalRecommendationsFound("No personal recommendations found for ticket [%s]", issue_id)

    controls, recommendations = get_recommendations_and_controls(
        personal_recommendation_output.results.personal_recommendations,
        concerns,
    )

    LOGGER.info("Some new data has been generated - controls: [%s], recommendations [%s]", controls, recommendations)
    return controls, recommendations


async def _cleanup_old_result_for_concerns(
    service_dal: ServiceDAL, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, concern_ids: set[int]
) -> None:
    controls_ids_to_delete = set()
    case = await service_dal.cases_dal.get_case(account_id, source_id, issue_id, with_recommendations=True)

    if case.recommendations:
        for recommendation in case.recommendations:
            if recommendation.concern_id in concern_ids:
                LOGGER.info("Removing recommendation %s", recommendation.id)

                controls_ids_to_delete.add(recommendation.control_id)

        case.recommendations = [
            recommendation for recommendation in case.recommendations if recommendation.concern_id not in concern_ids
        ]

    await service_dal.session.commit()

    await service_dal.issues_analysis_dal.delete_controls(case.issue_analysis_id, controls_ids_to_delete)  # type: ignore[arg-type]


async def _store_new_results_in_db(
    service_dal: ServiceDAL,
    account_id: AccountIdType,
    source_id: SourceIdType,
    issue_id: str,
    controls: list[SecurityControl],
    recommendations: list[Implementation],
) -> None:
    _, map_old_control_id_to_new = await service_dal.issues_analysis_dal.add_new_controls(
        account_id, source_id, issue_id, controls
    )

    # Update recommendation control id to new control id
    for rec in recommendations:
        if rec.control_id in map_old_control_id_to_new:
            current_id = rec.control_id
            rec.control_id = map_old_control_id_to_new[current_id]
            LOGGER.debug(
                "Control id updated from [%s] to [%s] for recommendation id [%s]", current_id, rec.control_id, rec.id
            )

    await service_dal.cases_dal.add_new_recommendations(account_id, source_id, issue_id, recommendations)

    LOGGER.info("Recommendations updated for concerns")


async def generate(  # noqa: PLR0913
    service_dal: ServiceDAL,
    account_id: AccountIdType,
    source_id: SourceIdType,
    issue_id: str,
    concern_ids: set[int],
    user_id: str,
    hashed_request_parameters: str,
    redis_client: AsyncPrefixRedisClient,
) -> None:
    LOGGER.info("Generating recommendations for issue_id %s", issue_id)
    controls, recommendations = await _get_new_recommendations_and_controls(
        service_dal, account_id, source_id, issue_id, concern_ids, user_id, hashed_request_parameters, redis_client
    )

    await _cleanup_old_result_for_concerns(service_dal, account_id, source_id, issue_id, concern_ids)

    await _store_new_results_in_db(service_dal, account_id, source_id, issue_id, controls, recommendations)
    await _notify_recommendation_on_demand_completion(account_id, issue_id, user_id, redis_client)
    LOGGER.info("Recommendation generation completed successfully.")


async def _notify_recommendation_on_demand_completion(
    account_id: AccountIdType,
    issue_id: str,
    user_id: str,
    redis_client: AsyncPrefixRedisClient,
) -> None:
    LOGGER.info("Notifying recommendation_on_demand completion")
    try:
        event_notifier = EventNotifier(redis_client, user_id)
        await event_notifier.notify(
            status=EventStatus.COMPLETED,
            message=EventMessage(
                title="Recommendation On Demand Completed",
                description=(f"Recommendation On Demand Completed successfully for issue_id: {issue_id}"),
            ),
            action="recommendation_on_demand_completed",
            progress=100,
        )
    except Exception:
        LOGGER.exception("Failed to notify recommendation_on_demand completion")


async def _get_concerns_data(
    service_dal: ServiceDAL, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, concern_ids: set[int]
) -> list[IssueAnalysisConcern]:
    issue_analysis = await service_dal.issues_analysis_dal.get(account_id, source_id, issue_id)
    if not issue_analysis.concerns:
        LOGGER.warning("No concerns found for issue_id %s", issue_id)
        raise NoConcernsForCase

    concerns = [concern for concern in issue_analysis.concerns if concern.id in concern_ids]

    if len(concerns) == 0:
        LOGGER.warning("No concerns found for issue_id %s", issue_id)
        raise NoMatchForCaseConcernsToRequestedConcernIds(
            "No match for case concerns and requested concern ids", issue_id
        )

    return concerns


def _hash_request_parameters(
    account_id: AccountIdType,
    source_id: SourceIdType,
    issue_id: str,
) -> str:
    return f"{account_id}-{source_id}-{issue_id}"


def _is_ai_already_running_for_issue_id(hashed_parameters: str) -> bool:
    active_tasks = celery_manager_instance.get_active_tasks()

    for tasks in active_tasks.values():
        for task in tasks:
            if task["id"] == hashed_parameters:
                return True

    return False


async def generate_recommendation_on_demand(  # noqa: PLR0913
    service_dal: ServiceDAL,
    account_id: AccountIdType,
    source_id: SourceIdType,
    issue_id: str,
    concern_ids: set[int],
    user_id: str,
    background_tasks: BackgroundTasks,
    redis_client: AsyncPrefixRedisClient,
) -> bool:
    hashed_request_parameters = _hash_request_parameters(account_id, source_id, issue_id)

    is_ai_already_running = _is_ai_already_running_for_issue_id(hashed_request_parameters)
    if is_ai_already_running:
        LOGGER.info(
            "AI is already running for account id [%s] source id [%s] and issue_id [%s]",
            account_id,
            source_id,
            issue_id,
        )
        return False

    background_tasks.add_task(
        generate,
        service_dal,
        account_id,
        source_id,
        issue_id,
        concern_ids,
        user_id,
        hashed_request_parameters,
        redis_client,
    )
    LOGGER.info("Recommendation generation started in new task.")

    return True
