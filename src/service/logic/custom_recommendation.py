from prime_config_service_client import CustomRecommendationOutput
from prime_shared.common_types import AccountIdType

from service.errors import CustomRecommendationError
from service.models import Implementation, ImplementationStatus, ImplementationStatusUpdate, UserImplementationStatus
from service.services_clients import ServicesClients

REC_CUSTOM_TAG = "custom"


def recommendation_from_custom(
    custom_recommendation: CustomRecommendationOutput, concern_id: int, control_id: str
) -> Implementation:
    return Implementation(
        id=custom_recommendation.id,
        concern_id=concern_id,
        control_id=control_id,
        recommendation=custom_recommendation.recommendation,
        status=ImplementationStatus.UNKNOWN,
        raci=[REC_CUSTOM_TAG],
        controls={},
    )


async def get_custom_recommendations(account_id: AccountIdType) -> list[CustomRecommendationOutput]:
    account_config = await ServicesClients.config_api().download_config_file(account_id)
    return account_config.custom_recommendations or []


def _get_case_rec(
    rec_to_update: ImplementationStatusUpdate, case_recommendations: list[Implementation]
) -> Implementation | None:
    custom_rec = rec_to_update.concern_id is not None and rec_to_update.control_id is not None
    for rec in case_recommendations:
        if rec.id == rec_to_update.id:
            if not custom_rec:
                return rec
            if rec.concern_id == rec_to_update.concern_id and rec.control_id == rec_to_update.control_id:
                return rec
    return None


def handle_recommendations_status_change(
    case_recommendations: list[Implementation],
    rec_to_update: ImplementationStatusUpdate,
    custom_recommendations: list[CustomRecommendationOutput],
) -> None:
    case_rec = _get_case_rec(rec_to_update, case_recommendations)
    if rec_to_update.status == UserImplementationStatus.DISMISS:
        if case_rec is None:
            return
        if REC_CUSTOM_TAG in case_rec.raci:
            case_recommendations.remove(case_rec)
        else:
            case_rec.status = ImplementationStatus(rec_to_update.status)
    else:
        if case_rec is None:
            if rec_to_update.concern_id is None or rec_to_update.control_id is None:
                raise CustomRecommendationError from None
            custom_rec = next(custom_rec for custom_rec in custom_recommendations if custom_rec.id == rec_to_update.id)
            case_rec = recommendation_from_custom(custom_rec, rec_to_update.concern_id, rec_to_update.control_id)
            case_recommendations.append(case_rec)
        case_rec.status = ImplementationStatus(rec_to_update.status)
