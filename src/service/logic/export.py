import csv
import io
import logging
from collections.abc import Iterable
from typing import Any

from service.models import ExternalCaseWorkroom, ExternalIssueAnalysisWorkroom, RiskFactors

LOGGER = logging.getLogger("export")


def stream_export_to_csv(data: Iterable[dict[str, Any]], fields: list[str], write_header: bool) -> str:
    dialect = csv.unix_dialect()
    dialect.quoting = csv.QUOTE_NONNUMERIC
    output = io.StringIO()
    writer = csv.DictWriter(output, fieldnames=fields, dialect=dialect)
    if write_header:
        writer.writeheader()
    writer.writerows(data)
    return output.getvalue()


def select_fields_from_cases(
    selected_fields: list[str],
    cases: list[ExternalCaseWorkroom],
) -> list[dict[str, Any]]:
    LOGGER.info("Selecting fields %s from %s cases", selected_fields, len(cases))
    output_data = []
    for case in cases:
        case_row: dict[str, Any] = {}
        for field in selected_fields:
            selected_field_value = None
            if field in case.model_dump():
                selected_field_value = getattr(case, field)
            elif (
                field in case.issue_analysis.model_fields_set
                or field in ExternalIssueAnalysisWorkroom.model_computed_fields
            ):
                selected_field_value = getattr(case.issue_analysis, field)
            elif field in case.provider_fields:
                selected_field_value = case.provider_fields[field]
            elif field.removeprefix("provider_fields.") in case.provider_fields:
                selected_field_value = case.provider_fields[f"{field.removeprefix('provider_fields.')}"]
            elif (
                field in case.issue_analysis.risk_factors.model_fields_set or field in RiskFactors.model_computed_fields
            ):
                selected_field_value = getattr(case.issue_analysis.risk_factors, field)
            if selected_field_value is None:
                case_row[field] = selected_field_value
                continue
            try:
                selected_field_value = selected_field_value.value
            except AttributeError:
                pass
            case_row[field] = selected_field_value
        output_data.append(case_row)
    return output_data
