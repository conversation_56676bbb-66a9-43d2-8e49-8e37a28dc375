import logging

from prime_jobs import Job, JobScheduler
from prime_shared.common_types import AccountIdType
from prime_utils.monitoring import report_exception

from service.db import CaseTable
from service.models import (
    ProviderFieldData,
    ProviderFieldInfo,
    ProviderFieldsInfoMapping,
    ProviderFieldType,
)
from service.models.jobs import JobCreateArg

LOGGER = logging.getLogger("utils")


def get_provider_fields_from_case(
    case: CaseTable, provider_fields_info: ProviderFieldsInfoMapping | None
) -> dict[str, ProviderFieldData]:
    return_fields = {}
    for key, value in (case.provider_fields or {}).items():
        field_info: ProviderFieldInfo | None
        if provider_fields_info is None:
            field_info = ProviderFieldInfo(type=ProviderFieldType.STRING, id=key, name=key)
        else:
            field_info = provider_fields_info.get(key, None)
        if field_info:
            try:
                field = ProviderFieldData(**field_info.model_dump(), value=value)
                return_fields[field.id] = field
            except Exception as ex:
                report_exception(ex)
                LOGGER.exception("Failed to create ProviderFieldData for field %s", field_info)
    return return_fields


async def add_job_logic(account_id: AccountIdType, job_create_args: JobCreateArg, scheduler: JobScheduler) -> Job:
    job_args = job_create_args.model_dump(exclude={"job", "created_by"})
    new_job = await scheduler.add_job(
        account_id=account_id,
        job_args=job_args,
        created_by=job_create_args.created_by,
        job_type=str(job_create_args.job.value),
    )
    return new_job
