from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import TYPE_CHECKING, cast

from prime_service_kit.shared_types import AccountArgumentType
from prime_shared.common_types import AccountIdType, SourceIdType

from service.db import CaseTable, IssueAnalysisTable, ServiceDAL
from service.errors import SummaryNotFoundError
from service.logic.filters_and_sort import CaseFilters
from service.models import (
    ExternalContainer,
    ExternalContainerRisk,
    ExternalPrimeConcern,
    IssueAnalysisConcern,
    RiskScoreCategory,
    Summary5W,
    risk_score_to_category,
)

from .utils import get_provider_fields_from_case

if TYPE_CHECKING:
    pass

LOGGER = logging.getLogger("external_summaries")
SUMMARY_PLACEHOLDER = "not-provided"
MAX_CONCERNS_TO_RETURN = 3
DEFAULT_SUMMARY = "No summary available"


@dataclass
class ChildInfo:
    issue_id: str
    risk_score: int
    concerns: list[IssueAnalysisConcern]


async def get_external_container(
    *,
    account_id: AccountArgumentType,
    source_id: SourceIdType,
    issue_id: str,
    service_dal: ServiceDAL,
) -> ExternalContainer:
    case = await service_dal.get_case_container_view(account_id=account_id, source_id=source_id, issue_id=issue_id)
    if not case.partial or not case.issue_analysis:
        raise SummaryNotFoundError(account_id, source_id, issue_id)
    LOGGER.info("Getting container for issue %s", issue_id)
    results_cases = await get_container_children_for_score(service_dal, account_id, source_id, issue_id)
    concerns = _get_issue_concerns(results_cases)
    summaries_5w = Summary5W.from_question(case.partial.questions) if case.partial else Summary5W.empty()
    case_fields = get_provider_fields_from_case(case, None)
    external_container = ExternalContainer(
        id=case.id or -1,
        source_id=source_id,
        issue_id=issue_id,
        risk_score=case.issue_analysis.risk_score or 0,
        provider_fields={field_id: str(field.value) for field_id, field in case_fields.items()},
        issue_summary_short=(case.partial.short if case.partial else None) or DEFAULT_SUMMARY,
        issue_summary_5w=summaries_5w,
        risk=_get_issue_risk_scores(results_cases),
        concerns=concerns,
        title=cast(str, case.provider_fields.get("summary", "")),
    )
    return external_container


async def get_container_children_for_score(
    service_dal: ServiceDAL, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
) -> list[ChildInfo]:
    case_filters = CaseFilters.default_workroom_filter().parent_id(issue_id)
    entities = [CaseTable.issue_id, IssueAnalysisTable.risk_score, IssueAnalysisTable.concerns]
    results = await service_dal.cases_dal.get_cases_by(account_id, source_id, case_filters, entities=entities)
    child_info = []
    for child_issue_id, risk_score, concerns in results:
        child_info.append(ChildInfo(issue_id=child_issue_id, risk_score=risk_score or 0, concerns=list(concerns or [])))
    return child_info


def _get_issue_risk_scores(issue_children: list[ChildInfo]) -> ExternalContainerRisk:
    results: dict[RiskScoreCategory, int] = dict.fromkeys(RiskScoreCategory, 0)
    for child in issue_children:
        results[risk_score_to_category(child.risk_score)] += 1
    return ExternalContainerRisk(
        monitor=results[RiskScoreCategory.MONITOR],
        analyze=results[RiskScoreCategory.ANALYZE],
        intervene=results[RiskScoreCategory.INTERVENE],
    )


def _get_issue_concerns(child_info: list[ChildInfo]) -> list[ExternalPrimeConcern]:
    concerns: list[ExternalPrimeConcern] = []
    top_children: list[ChildInfo] = sorted(child_info, key=lambda x: x.risk_score, reverse=True)
    for child in top_children:
        for concern in child.concerns:
            concerns.append(ExternalPrimeConcern(**concern.model_dump(), recommendations=[]))
            if len(concerns) >= MAX_CONCERNS_TO_RETURN:
                return concerns
    return concerns
