import datetime
import logging
from collections.abc import Callable, Sequence

from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_dataclasses import PaginationArgs
from prime_shared.common_types import AccountIdType

from service.db.service_dal import ServiceDAL
from service.db.tables.cases import CaseTable
from service.logic.issues_graph import GraphGenerator
from service.logic.llm_context.models import AnalysisRecord, ExportedRecord, ExportedSummary
from service.models import (
    risk_score_to_category,
)

LOGGER = logging.getLogger("rat-logic-service")


class DataExporter:
    def __init__(
        self,
        account_id: AccountIdType,
        service_dal: ServiceDAL,
        by_container_id: int,
        redis_client: AsyncPrefixRedisClient | None = None,  # for tree caching
        exporter_callback: Callable[[ExportedRecord], None] | None = None,
    ) -> None:
        self.exporter_callback = exporter_callback or self._internal_export_data_callback
        self.by_container_id = by_container_id
        self.account_id = account_id
        self.service_dal = service_dal
        self.redis_client = redis_client
        self.output_records: list[ExportedRecord] = []

    def _internal_export_data_callback(self, record: ExportedRecord) -> None:
        self.output_records.append(record)

    async def export_data(self, pagination_args: PaginationArgs) -> list[ExportedRecord]:
        LOGGER.info("Limited to %s records", pagination_args.limit)

        await self._export_table(pagination_args)
        return self.output_records

    async def _export_table(
        self,
        pagination_args: PaginationArgs,
    ) -> None:
        total_exported = 0
        ids = await self._get_children_ids()
        records = await self.service_dal.llm_context_dal.get_cases(self.account_id, ids, pagination_args)

        total_exported = await self._process_records_batch(records, total_exported)

        LOGGER.info("Completed export of case id: %s with %s total records", self.by_container_id, total_exported)

    async def get_total_records(self) -> int:
        ids = await self._get_children_ids()
        return await self.service_dal.llm_context_dal.get_total_records(self.account_id, ids)

    async def _get_children_ids(self) -> list[str] | None:
        case = await self.service_dal.cases_dal.get_case_by_id(self.account_id, self.by_container_id)
        tree = await GraphGenerator(self.service_dal, self.account_id, case.source_id).load(self.redis_client)
        node = tree.get(case.issue_id)
        if not node:
            raise ValueError(f"Container ID {case.issue_id} not found in tree")
        ids = [case.issue_id] + tree.get_all_descendants(case.issue_id)
        return ids

    async def _process_records_batch(self, records: Sequence[CaseTable], total_exported: int) -> int:
        total_exported += len(records)

        for record in records:
            if hasattr(record, "id") and record.id is not None:
                exported_record = self._prepare_record(record)
                self.exporter_callback(exported_record)

        return total_exported

    def _prepare_record(self, record: CaseTable) -> ExportedRecord:
        date_time = record.created_at or datetime.datetime.now()

        summary = self._get_summary(record)
        analysis = self._extract_issue_analysis(record)

        exported_record = ExportedRecord(
            issue_id=record.issue_id,
            parent_issue_id=record.parent_issue_id or "",
            status=record.status,
            created_at=date_time,
            summary=summary,
            provider_fields=record.provider_fields,
            analysis=analysis,
        )

        return exported_record

    @staticmethod
    def _get_summary(record: CaseTable) -> ExportedSummary:
        if record.partial and record.partial.summary and record.partial.questions and record.partial.short:
            summary = ExportedSummary(
                summary=record.partial.summary,
                questions=record.partial.questions,
                short=record.partial.short,
            )
        else:
            summary = ExportedSummary()

        return summary

    @staticmethod
    def _extract_issue_analysis(record: CaseTable) -> AnalysisRecord:
        if record.issue_analysis:
            concerns = (
                [c.long_description for c in record.issue_analysis.concerns] if record.issue_analysis.concerns else None
            )
            return AnalysisRecord(
                availability=record.issue_analysis.availability,
                confidentiality=record.issue_analysis.confidentiality,
                integrity=record.issue_analysis.integrity,
                risk_score=record.issue_analysis.risk_score,
                risk_score_category=risk_score_to_category(record.issue_analysis.risk_score),
                classification=record.issue_analysis.classification,
                concerns=concerns,
                is_automated=record.issue_analysis.is_automated,
                is_security_enhancement=record.issue_analysis.is_security_enhancement,
            )
        return AnalysisRecord()
