from datetime import datetime
from typing import Any

from prime_db_utils import utcnow
from prime_gen_ai_service_client import QuestionsOutput
from prime_security_review_service_client import DesignDocTopRecommendation
from pydantic import BaseModel, Field, computed_field, field_serializer, field_validator
from pydantic_ai import format_as_xml

from service.models.levels.risk_score import RiskScoreCategory

MAX_MESSAGES_HISTORY = 10


class BaseExportedRecord(BaseModel):
    created_at: datetime | None = Field(default_factory=utcnow)

    @field_serializer("created_at")
    def serialize_dt(self, dt: datetime, _info: Any) -> str:  # noqa: PLR6301
        return dt.isoformat()


class AnalysisRecord(BaseModel):
    availability: int | None = Field(default=None)
    confidentiality: int | None = Field(default=None)
    integrity: int | None = Field(default=None)
    risk_score: int | None = Field(default=None)
    risk_score_category: RiskScoreCategory | None = Field(default=None)
    classification: bool | None = Field(default=False)
    concerns: list[str] | None = Field(default=None)
    is_automated: bool | None = Field(default=None)
    is_security_enhancement: bool | None = Field(default=None)

    @field_validator("availability", "confidentiality", "integrity", "risk_score", mode="before")
    @classmethod
    def none_to_zero(cls, value: Any) -> int:
        return 0 if value is None else value


class ExportedSummary(BaseModel):
    summary: str = Field(default="No summary found")
    questions: QuestionsOutput | None = Field(default=None)
    short: str = Field(default="No short summary found")


class ExportedRecord(BaseExportedRecord):
    issue_id: str
    parent_issue_id: str
    status: str
    analysis: AnalysisRecord
    summary: ExportedSummary
    provider_fields: dict[str, Any]


class DesignDocRecord(BaseExportedRecord):
    id: int
    title: str
    summary: str

    top_recommendations: list[DesignDocTopRecommendation]

    @computed_field  # type: ignore[prop-decorator]
    @property
    def xml_date(self) -> str:
        return format_as_xml(
            self.model_dump(exclude={"xml_date"}),
            root_tag="design_doc",
            item_tag="item",
            include_root_tag=True,
            indent=None,
        )
