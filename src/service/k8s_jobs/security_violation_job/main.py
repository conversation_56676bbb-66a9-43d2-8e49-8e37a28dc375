import logging

from service.db import get_service_dal_context

from ..run_job_main import run_main
from .models import PsvJobArgs
from .psv_job_logic import PsvJobLogic

LOGGER = logging.getLogger("psv-job")


async def main() -> None:
    job_vars = PsvJobArgs()
    try:
        LOGGER.info("Running SV Job with args: %s", job_vars)
        async with get_service_dal_context() as service_dal:
            job_logic = PsvJobLogic(
                account_id=job_vars.account_id,
                source_id=job_vars.source_id,
                force=job_vars.force,
                job_id=job_vars.job_id,
                service_dal=service_dal,
                last_psv_at=None if job_vars.force else job_vars.last_psv_at,
                ai_version=PsvJobLogic.get_ai_version(),
            )
            await job_logic.start()
        LOGGER.info("Finished SV Job %s", job_vars.job_id)
    except:
        LOGGER.exception("Failed to run main")
        raise


if __name__ == "__main__":
    run_main(main)
