from __future__ import annotations

import asyncio
import logging
import re
from asyncio import Future
from collections.abc import Generator
from datetime import UTC, datetime
from typing import TypeVar

from packaging.version import Version
from prime_file_manager_service_client import DocumentType, FileInfo
from prime_gen_ai_service_client import PSIPipelineInput, PSITaskOutput
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType
from prime_utils import AsyncRateLimit
from tenacity import before_sleep_log, retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from service.config import get_config
from service.db import ServiceDAL
from service.errors import GenAiOutputError, JobFailedError
from service.job_type import JobType
from service.logic.jira_manager import JiraIssuesManager, JiraPrimeIssue
from service.services_clients import ServicesClients, get_all_files_info

from ..base_job_logic import GenAIBaseJob
from ..base_job_logic.gen_ai_celery import GENAI_PIPELINE_PSV_TASK_NAME, GENAI_PIPELINES_QUEUE, celery_manager_instance
from .models import JobStatisticsResults, PsvInfo, PsvJobLogicResult, PsvJobReport
from .psv_reporter import PsvMetricsReporter

TOTAL_RUNNING_TASKS_PROGRESS = 95  # percentage

LOGGER = logging.getLogger("psv_job_logic")

BULK_SIZE = 100

T = TypeVar("T")


def _bulk(items: list[T], bulk_size: int) -> Generator[list[T]]:
    for i in range(0, len(items), bulk_size):
        yield items[i : i + bulk_size]


class PsvJobLogic(GenAIBaseJob):
    PSV_PATTERNS = {
        "aws_key": re.compile(r"AKIA[0-9A-Z]{16}", re.IGNORECASE),
        "github_token": re.compile(r"gh[pousr]_[0-9a-zA-Z]{36}", re.IGNORECASE),
        "private_key": re.compile(
            r"-----BEGIN\s+(?:RSA|DSA|EC|OPENSSH|PRIVATE)\s+PRIVATE\s+KEY-----[\s\S]*?-----END\s+(?:RSA|DSA|EC|OPENSSH|PRIVATE)\s+PRIVATE\s+KEY-----",
            re.IGNORECASE,
        ),
    }

    @property
    def job_type(self) -> str:
        return JobType.SECURITY_VIOLATION.value

    def __init__(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
        ai_version: Version | None = None,
        force: bool = False,
        last_psv_at: datetime | None = None,
    ) -> None:
        self._force = force
        LOGGER.info("SV initialized with force=%s and last_run=%s", self._force, last_psv_at)
        self._issue_id_to_case_id: dict[str, int] = {}
        self._issue_manager: JiraIssuesManager = JiraIssuesManager(account_id, source_id)
        self._issues_process_max_degree_of_parallelism = get_config().max_workers
        self._service_dal = service_dal
        self._last_psv_at: datetime = last_psv_at or datetime.min.replace(tzinfo=UTC)
        self._ai_version: Version = ai_version or PsvJobLogic.get_ai_version()
        self._issues_to_process_total: int = 0
        self._job_statistics_results = JobStatisticsResults()
        super().__init__(account_id, source_id, job_id, service_dal)

    async def _validate_source(self) -> bool:
        try:
            source = await ServicesClients.source_api().get_source(
                account_id=self._account_id, source_id=self._source_id
            )
            return source is not None and source.id == self._source_id and source.source_type == SourceType.JIRA
        except Exception as e:
            raise JobFailedError("Failed to get source with id %s") from e

    async def _run(self) -> None:
        await self._validate_source()
        try:
            await self._progress_manager.increase_step_progress()
            LOGGER.info("Running SV job for account %s and source %s", self._account_id, self._source_id)
            await self._progress_manager.increase_step_progress()
            issues_to_process = await self._get_issues_to_process()
            self._issues_to_process_total = len(issues_to_process)
            if len(issues_to_process) > 0:
                factor = TOTAL_RUNNING_TASKS_PROGRESS / len(issues_to_process)
                self._setup_progress_step(factor)
                await self.process_issues_parallel(issues_to_process)
            else:
                self._setup_progress_step(TOTAL_RUNNING_TASKS_PROGRESS)
                await self._increase_step_progress()

            await self.report(True)
            LOGGER.info("Execution completed. %s issues processed", len(issues_to_process))
        except Exception:
            await self.report(False)
            raise

    async def _get_issues_to_process(self) -> list[PsvInfo]:
        def _should_process(_issue_info: FileInfo, _psv_info: PsvInfo | None) -> bool:
            if _issue_info.timestamp is None:
                return False
            if _psv_info is None:
                return True
            if self._force or GenAIBaseJob.version_changed(_psv_info.research_package_version, self._ai_version):
                _psv_info.issue_hash = None
                return True
            return _issue_info.timestamp > max(self._last_psv_at, _psv_info.updated_at)

        LOGGER.info("Getting issues to process")
        existing_issues = await get_all_files_info(self._account_id, self._source_id, DocumentType.JIRA)
        self._issues_total = len(existing_issues)
        LOGGER.info("Found %s issues in file manager", self._issues_total)
        rows = await self._service_dal.psv_dal.get_psvs_by(account_id=self._account_id, source_id=self._source_id)
        db_psvs = {row.issue_id: PsvInfo(**row.model_dump(include=set(PsvInfo.model_fields.keys()))) for row in rows}
        ret_psvs: dict[str, PsvInfo] = {}
        for issue_id, issue_info in existing_issues.items():
            psv_info = db_psvs.get(issue_id)
            if not _should_process(issue_info, psv_info):
                LOGGER.debug("Will not process %s", issue_id)
                self._job_statistics_results.issues_not_processed += 1
                continue
            LOGGER.info("Will process %s, new=%s", issue_id, psv_info is None)
            if psv_info:
                LOGGER.debug(
                    "Issue %s timestamps cmp: %s > %s",
                    issue_id,
                    issue_info.timestamp,
                    max(self._last_psv_at, psv_info.updated_at),
                )
                self._job_statistics_results.existing_issues_to_process += 1
            else:
                self._job_statistics_results.new_issues_to_process += 1
            psv_info = psv_info or PsvInfo(issue_id=issue_id, source_id=self._source_id, account_id=self._account_id)
            ret_psvs[issue_id] = psv_info
        LOGGER.info("Found %s issues to process", len(ret_psvs))
        return list(ret_psvs.values())

    async def process_issues_parallel(self, issues_to_process: list[PsvInfo]) -> None:
        for issues_bulk in _bulk(sorted(issues_to_process, key=lambda x: x.issue_id), BULK_SIZE):
            await self.process_jira_issues_bulk(issues_bulk)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
        reraise=True,
        before_sleep=before_sleep_log(LOGGER, logging.DEBUG, exc_info=True),
    )
    async def get_jira_issues(self, issues_to_process: list[str]) -> dict[str, JiraPrimeIssue]:
        return await self._issue_manager.get_issues(issues_to_process)

    async def process_jira_issues_bulk(self, issues_to_process: list[PsvInfo]) -> None:
        try:
            jira_issues = await self.get_jira_issues([i.issue_id for i in issues_to_process])
        except Exception:
            LOGGER.exception("Failed to get issues for %s", issues_to_process)
            self._job_statistics_results.failed_to_fetch_bulk += 1
            return
        issue_dict = {issue.issue_id: issue for issue in issues_to_process}
        tasks = []
        # self._job_statistics_results.fetched_issues_bulks += 1
        for issue in jira_issues.values():
            if not (psv_info := issue_dict.get(issue.attributes.id_)):
                LOGGER.error("Issue %s not found in issues to process", issue.attributes.id_)
                self._job_statistics_results.missing_issues_from_bulk += 1
                await self._progress_manager.increase_step_progress()
                continue
            LOGGER.debug("Adding issue %s to process %s", issue.attributes.id_, psv_info)
            tasks.append(self._get_psv_result(issue, psv_info))
        for res in AsyncRateLimit(self._issues_process_max_degree_of_parallelism).as_completed(tasks):
            await self._handle_possible_psv(res)

    async def _handle_possible_psv(self, fut: Future[PsvJobLogicResult | None]) -> None:
        if psv_result := await fut:
            LOGGER.info("Adding SV to DB for issue %s", psv_result.issue_id)
            await self._service_dal.psv_dal.add_psv(psv_result)
            # self._job_statistics_results.issues_saved_to_db += 1
        await self._progress_manager.increase_step_progress()

    async def _get_psv_result(self, issue: JiraPrimeIssue, psv_info: PsvInfo) -> PsvJobLogicResult | None:
        LOGGER.debug("Trying to get psv for issue %s, %s", issue.attributes.id_, psv_info)
        self._job_statistics_results.issues_started_to_process += 1
        try:
            if psv_logic_res := self._get_regex_psv(issue):
                return psv_logic_res
            current_issue_hash = await asyncio.to_thread(self.get_issue_hash, issue)
            if psv_info.issue_hash and psv_info.issue_hash == current_issue_hash:
                self._job_statistics_results.issues_not_changed_skip += 1
                LOGGER.info("Skipping issue %s as it has not changed", psv_info.issue_id)
                return None
            LOGGER.debug(
                "Running gen-ai, issue %s has changed: %s != %s",
                psv_info.issue_id,
                psv_info.issue_hash,
                current_issue_hash,
            )
            if psv_logic_res := await self._get_gen_ai_psv(issue):
                psv_logic_res.issue_hash = current_issue_hash
                return psv_logic_res
            # self._job_statistics_results.issues_has_sv_false += 1
            return PsvJobLogicResult(
                account_id=self._account_id,
                description="",
                violation_type="",
                research_package_version=str(self._ai_version),
                source_id=self._source_id,
                issue_id=issue.attributes.id_,
                has_psv=False,
                issue_hash=current_issue_hash,
            )
        except Exception:
            LOGGER.exception("Failed to process SV for issue %s", psv_info.issue_id)
            self._job_statistics_results.error_during_sv_processing += 1
            return None
        finally:
            self._job_statistics_results.issues_finished_to_process += 1

    def _get_regex_psv(self, issue: JiraPrimeIssue) -> PsvJobLogicResult | None:
        for pattern_name, pattern in self.PSV_PATTERNS.items():
            if match := pattern.search(issue.raw_data):
                self._job_statistics_results.issues_with_sv_from_regex += 1
                LOGGER.info("Found SV pattern %s for issue %s", pattern_name, issue.attributes.id_)
                return PsvJobLogicResult(
                    account_id=self._account_id,
                    description=match.group().encode().decode("unicode_escape"),
                    violation_type=pattern_name,
                    research_package_version=str(self._ai_version),
                    source_id=self._source_id,
                    issue_id=issue.attributes.id_,
                    has_psv=True,
                )
        return None

    async def _get_gen_ai_psv(self, issue: JiraPrimeIssue) -> PsvJobLogicResult | None:
        LOGGER.info("Getting psv for issue %s from gen-ai", issue.attributes.id_)
        self._job_statistics_results.issues_running_gen_ai += 1
        psv_input = PSIPipelineInput(jira_ticket=self.to_ai_issue(issue))
        result = await celery_manager_instance.send_and_wait_for_result(
            input_args=psv_input.to_dict(),
            headers=self._get_celery_headers(issue.attributes.id_, GENAI_PIPELINES_QUEUE, {}),
            task_name=GENAI_PIPELINE_PSV_TASK_NAME,
            queue=GENAI_PIPELINES_QUEUE,
        )
        self._job_statistics_results.issues_returned_from_gen_ai += 1
        output = PSITaskOutput(**result)
        LOGGER.info("Gen AI SV response for issue %s is %s", issue.attributes.id_, output)
        if output.error:
            raise GenAiOutputError(output.error)
        if output.results is None:
            raise GenAiOutputError("Results are None in response")
        if not output.results.ticket_has_psi:
            LOGGER.info("No SV found for issue %s", issue.attributes.id_)
            return None
        self._job_statistics_results.issues_with_sv_from_gen_ai += 1
        LOGGER.info("SV found from AI for issue %s", issue.attributes.id_)
        psv_text = ", ".join([f.incident_value for f in output.results.results])
        return PsvJobLogicResult(
            account_id=self._account_id,
            description=psv_text,
            violation_type=output.results.results[0].incident_type or "password",  # default to password
            research_package_version=str(self._ai_version),
            source_id=self._source_id,
            issue_id=issue.attributes.id_,
            has_psv=True,
        )

    async def report(self, execution_status: bool) -> None:
        reporter = PsvMetricsReporter(account_id=self._account_id, source_id=self._source_id, job_id=self.get_job_id())
        summary_report = PsvJobReport(
            duration_in_seconds=self.duration,
            execution_status=execution_status,
            **self._job_statistics_results.model_dump(),
        )
        LOGGER.info("Reporting job metrics %s", summary_report)
        reporter.report_job_metrics(summary_report)
