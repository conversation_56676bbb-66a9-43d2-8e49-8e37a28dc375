import json
from datetime import datetime

from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import BaseModel, computed_field
from pydantic_settings import BaseSettings

from service.models import BaseStepResults
from service.models.jobs import JobState


class PsvJobArgs(BaseSettings):
    account_id: AccountIdType
    source_id: SourceIdType
    job_id: int
    force: bool
    last_psv_at: datetime | None = None
    datadog_enabled: bool = False


class PsvInfo(BaseModel):
    account_id: AccountIdType
    issue_id: str
    source_id: SourceIdType
    psv_id: int | None = None
    updated_at: datetime = datetime.min
    issue_hash: str | None = None
    research_package_version: str | None = None


class PsvJobLogicResult(BaseStepResults):
    account_id: AccountIdType
    source_id: SourceIdType
    issue_id: str
    description: str
    violation_type: str
    has_psv: bool
    research_package_version: str | None = None
    issue_hash: str | None = None


class JobStatisticsResults(BaseModel):
    issues_not_processed: int = 0
    new_issues_to_process: int = 0
    existing_issues_to_process: int = 0
    failed_to_fetch_bulk: int = 0
    missing_issues_from_bulk: int = 0
    issues_started_to_process: int = 0
    issues_finished_to_process: int = 0
    error_during_sv_processing: int = 0
    issues_not_changed_skip: int = 0
    issues_running_gen_ai: int = 0
    issues_returned_from_gen_ai: int = 0
    issues_with_sv_from_regex: int = 0
    issues_with_sv_from_gen_ai: int = 0
    # fetched_issues_bulks: int = 0
    # issues_has_sv_false: int = 0
    # issues_saved_to_db: int = 0


class PsvJobReport(JobStatisticsResults):
    execution_status: bool
    duration_in_seconds: int = 0

    def as_str_report(self) -> str:
        return json.dumps({key.replace("_", " ").title() for key, value in self.model_dump().items()})

    # @computed_field  # type: ignore[prop-decorator]
    # @property
    # def success_rate(self) -> float:
    #     issues_sent_to_ai = self.issues_need_processing_final_summary + self.issues_need_processing_partial_summary
    #     if issues_sent_to_ai == 0:
    #         return 100
    #     return (1 - (self.errors / issues_sent_to_ai)) * 100

    @computed_field  # type: ignore[prop-decorator]
    @property
    def errors(self) -> int:
        return self.missing_issues_from_bulk + self.failed_to_fetch_bulk + self.error_during_sv_processing

    @computed_field  # type: ignore[prop-decorator]
    @property
    def job_state(self) -> JobState:
        if self.execution_status is False:
            return JobState.TERMINATED
        if self.errors > 0:
            return JobState.COMPLETED_WITH_ERRORS
        return JobState.COMPLETED

    @computed_field  # type: ignore[prop-decorator]
    @property
    def divergent_processing(self) -> int:
        return self.issues_started_to_process - self.issues_finished_to_process

    @computed_field  # type: ignore[prop-decorator]
    @property
    def divergent_gen_ai(self) -> int:
        return self.issues_running_gen_ai - self.issues_returned_from_gen_ai
