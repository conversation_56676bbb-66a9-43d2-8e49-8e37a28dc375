import os
import time
from typing import Any

from datadog import initialize, statsd
from prime_shared.common_types import AccountIdType, SourceIdType

from service.config import get_config
from service.job_type import JobType

from .models import PsvJobReport

DD_METRICS_PREFIX = "prime.job.psv"


class PsvMetricsReporter:
    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, job_id: int):
        self.account_id = account_id
        self.job_id = job_id
        self.source_id = source_id

        self._base_tags = frozenset(
            [
                f"account_id:{self.account_id}",
                f"source_id:{self.source_id}",
                f"job_id:{self.job_id}",
                f"namespace:{get_config().namespace}",
                f"job_type:{JobType.SECURITY_VIOLATION.value}",
            ]
        )
        # Datadog automatically picks up API key from DD_API_KEY environment variable
        statsd_host = os.getenv("DD_AGENT_HOST", "localhost")
        initialize(statsd_host=statsd_host)

    def report_job_metrics(
        self,
        psv_report: PsvJobReport,
        additional_tags: dict[str, Any] | None = None,
    ) -> None:
        base_tags = list(self._base_tags)

        # Add any additional tags
        if additional_tags:
            base_tags.extend([f"{k}:{v}" for k, v in additional_tags.items()])
        finished_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
        base_tags.append(f"job_time:{finished_time}")
        base_tags.append(f"job_state:{psv_report.job_state.value}")

        metrics = {
            f"{DD_METRICS_PREFIX}.new_issues_to_process": psv_report.new_issues_to_process,
            f"{DD_METRICS_PREFIX}.existing_issues_to_process": psv_report.existing_issues_to_process,
            f"{DD_METRICS_PREFIX}.issues_not_processed": psv_report.issues_not_processed,
            f"{DD_METRICS_PREFIX}.issues_not_changed_skip": psv_report.issues_not_changed_skip,
            f"{DD_METRICS_PREFIX}.issues_with_sv_from_regex": psv_report.issues_with_sv_from_regex,
            f"{DD_METRICS_PREFIX}.issues_with_sv_from_gen_ai": psv_report.issues_with_sv_from_gen_ai,
            f"{DD_METRICS_PREFIX}.errors.fetch_bulk": psv_report.failed_to_fetch_bulk,
            f"{DD_METRICS_PREFIX}.errors.missing_issues_from_bulk": psv_report.missing_issues_from_bulk,
            f"{DD_METRICS_PREFIX}.errors.sv_processing": psv_report.error_during_sv_processing,
            f"{DD_METRICS_PREFIX}.errors.divergent_processing": psv_report.divergent_processing,
            f"{DD_METRICS_PREFIX}.errors.divergent_gen_ai": psv_report.divergent_gen_ai,
            # f"{DD_METRICS_PREFIX}.fetched_issues_bulks": psv_report.fetched_issues_bulks,
            # f"{DD_METRICS_PREFIX}.issues_has_sv_false": psv_report.issues_has_sv_false,
            # f"{DD_METRICS_PREFIX}.issues_saved_to_db": psv_report.issues_saved_to_db,
            # f"{DD_METRICS_PREFIX}.success_rate": psv_report.success_rate,
        }

        for metric, value in metrics.items():
            statsd.gauge(metric, value, tags=base_tags)

        statsd.timing(f"{DD_METRICS_PREFIX}.duration_seconds", psv_report.duration_in_seconds, tags=base_tags)

        if psv_report.execution_status is False:
            statsd.event(
                title="PSV Job Execution Failed",
                message=f"Job failed for account {self.account_id}, source {self.source_id}, job {self.job_id}",
                tags=base_tags,
                alert_type="error",
                source_type_name="psv_job",
            )
