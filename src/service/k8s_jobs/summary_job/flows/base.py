from __future__ import annotations

import asyncio
import logging
from abc import ABC, abstractmethod
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from dataclasses import dataclass
from datetime import datetime
from typing import TYPE_CHECKING, cast

from packaging.version import Version
from prime_gen_ai_service_client import (
    AdditionalExtraContextsInner,
    BinaryExtraContextInput,
    CeleryHeaders,
    ContextPipelineOutput,
    ContextSummaryOutput,
    ExtraContextBinary,
    ExtraContextFileData,
    FileData,
)
from prime_gen_ai_service_client import Issue as GenAIIssue
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import alru_cache

from service.db import ServiceDAL, get_service_dal_context
from service.errors import SummaryGenerationError
from service.logic.issues import BaseIssuesManager, BasePrimeIssue

from ...base_job_logic import GenAIBaseJob, SummaryOutput, SummaryOutputData, to_utc
from ..summary_info import Summaries<PERSON>ree, SummaryInfo

if TYPE_CHECKING:
    from ...base_job_logic import PageDownloader

TOTAL_RUNNING_TASKS_PROGRESS = 90  # percentage

LOGGER = logging.getLogger("summary_generation")


@dataclass
class FlowArgs:
    issue_id: str
    summaries_tree: SummariesTree
    account_id: AccountIdType
    source_id: SourceIdType
    issue_manager: BaseIssuesManager[BasePrimeIssue]
    ai_version: Version
    last_run: datetime | None
    force: bool
    celery_headers: CeleryHeaders
    context_downloader: PageDownloader | None


class BaseSummaryFlow(ABC):
    NAME = "base"

    def __init__(self, flow_args: FlowArgs):
        self._issue_id = flow_args.issue_id
        self._flow_args = flow_args
        self._context_downloader = flow_args.context_downloader
        self._issue_manager = flow_args.issue_manager
        self._ai_version = flow_args.ai_version
        self._celery_headers = flow_args.celery_headers
        self._issue: BasePrimeIssue | None = None
        self._ai_issue: GenAIIssue | None = None

    @asynccontextmanager
    async def _get_db_context(self) -> AsyncGenerator[ServiceDAL]:
        async with get_service_dal_context() as service_dal:
            yield service_dal

    @property
    def summary_info(self) -> SummaryInfo:
        return self.summaries_tree[self.issue_id]

    @property
    def account_id(self) -> AccountIdType:
        return self._flow_args.account_id

    @property
    def source_id(self) -> SourceIdType:
        return self._flow_args.source_id

    @property
    def issue_id(self) -> str:
        return self._issue_id

    @property
    def ai_version(self) -> Version:
        return self._flow_args.ai_version

    @property
    def last_run(self) -> datetime | None:
        return self._flow_args.last_run

    @property
    def timestamp(self) -> datetime:
        return self.summary_info.timestamp

    @property
    def summaries_tree(self) -> SummariesTree:
        return self._flow_args.summaries_tree

    @alru_cache
    async def get_hash(self) -> str:
        return await asyncio.to_thread(GenAIBaseJob.get_issue_hash, await self.get_issue())

    @abstractmethod
    async def _save_results(self, results: SummaryOutput) -> None:
        pass

    @abstractmethod
    async def _generate_summary(self) -> ContextPipelineOutput:
        pass

    @abstractmethod
    def _get_summary_info(self) -> SummaryOutput | None:
        pass

    @abstractmethod
    def _clear(self) -> None:
        pass

    @abstractmethod
    def get_relevant_issues(self) -> list[str]:
        pass

    @alru_cache
    async def get_issue(self) -> BasePrimeIssue:
        return await self._issue_manager.get_full_issue(self.issue_id, self.summaries_tree.graph)

    def needs_processing(self) -> bool:
        LOGGER.info("%s: Checking if processing required is required for issue id %s", self.NAME, self.issue_id)
        if self._flow_args.force:
            self._clear()
            return True
        summary_info = self._get_summary_info()
        if summary_info is None:
            LOGGER.info("%s: Summary is null for issue id %s", self.NAME, self.issue_id)
            return True
        is_ver_changed = GenAIBaseJob.version_changed(summary_info.ai_version, self.ai_version)
        if is_ver_changed:
            LOGGER.info("%s, Version changed from %s to %s", self.NAME, summary_info.ai_version, self.ai_version)
            self._clear()
            return True
        if self.last_run is None or to_utc(self.timestamp) > to_utc(self.last_run):
            LOGGER.info("Issue id %s data updated since last job run", self.issue_id)
            return True
        LOGGER.info("%s: No processing required for issue id %s", self.NAME, self.issue_id)
        return False

    async def _needs_summary_creation(self) -> bool:
        LOGGER.info("%s: Checking if summary creation is required for issue id %s", self.NAME, self.issue_id)
        summary_info = self._get_summary_info()
        if summary_info is None or await self.get_hash() != summary_info.issue_hash:
            LOGGER.info("%s: Issue hash is different from the saved hash", self.issue_id)
            return True
        LOGGER.info("%s: No summary creation is required for issue id %s", self.NAME, self.issue_id)
        return False

    async def _on_error(self, reason: str) -> None:
        LOGGER.warning("Failed to generate summary for issue id %s. error: %s", self.issue_id, reason)
        async with self._get_db_context() as service_dal:
            await service_dal.cases_dal.update_case(self.account_id, self.source_id, self.issue_id)
        raise SummaryGenerationError(
            issue_id=self.issue_id,
            account_id=self.account_id,
            source_id=self.source_id,
            error=reason,
        )

    async def _validate_summary_output(self, result: ContextPipelineOutput) -> ContextSummaryOutput:
        LOGGER.info("Validating summary output for issue id %s", self.issue_id)
        if result.error:
            await self._on_error(result.error)
        elif not result.context_summary:
            await self._on_error("Empty summary")
        return cast(ContextSummaryOutput, result.context_summary)

    async def build(self) -> bool:
        if self.issue_id not in self.summaries_tree:
            raise ValueError(f"Issue id {self.issue_id} not in the summaries tree")
        issue_hash = await self.get_hash()
        if self.summary_info.partial is None or issue_hash != self.summary_info.partial.issue_hash:
            self.summary_info.set_modified()
        is_generation_required = await self._needs_summary_creation()
        if not is_generation_required:
            LOGGER.info("%s: Skipping summary generation for issue id %s", self.NAME, self.issue_id)
            if self.summary_info.is_modified():
                await self._save_results(SummaryOutput(issue_hash=issue_hash, ai_version=str(self.ai_version)))
            return False
        await self._summary_flow()
        LOGGER.debug("%s: Finished summary for issue id %s", self.NAME, self.issue_id)
        return True

    async def get_gen_ai_issue(self) -> GenAIIssue:
        return GenAIBaseJob.to_ai_issue(await self.get_issue())

    async def _summary_flow(self) -> None:
        LOGGER.debug("%s: Starting summary flow for issue id %s", self.NAME, self.issue_id)
        await self._pre_fetch_actions()
        result = await self._generate_summary()
        output: ContextSummaryOutput = await self._validate_summary_output(result)
        results = SummaryOutput(
            data=SummaryOutputData(
                questions=output.questions,
                questions_summary=output.questions_summary,
                short_summary=output.short_summary or "",
            ),
            issue_hash=await self.get_hash(),
            ai_version=str(self.ai_version),
        )
        await self._save_results(results)

    async def _pre_fetch_actions(self) -> None:
        relevant_issues = self.get_relevant_issues()
        LOGGER.info("Fetching data for %d relevant issues", len(relevant_issues))
        if relevant_issues:
            async with self._get_db_context() as service_dal:
                cases = await service_dal.cases_dal.get_cases_by(
                    self.account_id, source_id=self.source_id, issues=list(relevant_issues), with_summary=True
                )
                for case in cases:
                    self.summaries_tree[case.issue_id].load_data(case)

    async def _get_extra_context(self) -> list[AdditionalExtraContextsInner]:
        LOGGER.info("Fetching extra context for issue id %s", self.issue_id)
        if self._context_downloader is None:
            LOGGER.info("No extra context downloader available for issue id %s", self.issue_id)
            return []
        pages = await self._context_downloader.get_pages(self.issue_id)
        extra_contexts = [
            AdditionalExtraContextsInner(actual_instance=ExtraContextFileData(extra_context_input=page))
            for page in pages
            if isinstance(page, FileData)
        ]
        binary_extra_context = [
            AdditionalExtraContextsInner(actual_instance=ExtraContextBinary(extra_context_input=page))
            for page in pages
            if isinstance(page, BinaryExtraContextInput)
        ]
        extra_contexts.extend(binary_extra_context)

        LOGGER.info("Fetched [%d] extra context items for issue id [%s]", len(extra_contexts), self.issue_id)
        return extra_contexts
