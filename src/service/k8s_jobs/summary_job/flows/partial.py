from __future__ import annotations

import logging
from typing import cast

from prime_gen_ai_service_client import (
    BaseIssue,
    ContextPipelineInput,
    ContextPipelineOutput,
    ExtraContextChild,
    IssueExtraContextInput,
)

from ...base_job_logic import SummaryOutput, SummaryOutputData
from ...base_job_logic.gen_ai_celery import (
    GENAI_PARTIALLY_SUMMARY_TASK_NAME,
    GENAI_PIPELINES_QUEUE,
    celery_manager_instance,
)
from .base import BaseSummaryFlow

children_context_type = tuple[list[ExtraContextChild], list[ExtraContextChild], list[BaseIssue]]

TOTAL_RUNNING_TASKS_PROGRESS = 90  # percentage

LOGGER = logging.getLogger("summary_generation")


class PartialSummaryFlow(BaseSummaryFlow):
    NAME = "partial"

    def _get_summary_info(self) -> SummaryOutput | None:
        return self.summary_info.partial

    def _clear(self) -> None:
        self.summary_info.partial = None

    def needs_processing(self) -> bool:
        if self._is_descendants_modified():
            return True
        return super().needs_processing()

    async def _needs_summary_creation(self) -> bool:
        if self._is_descendants_modified():
            return True
        if not (self.summaries_tree.graph.has_children(self.issue_id)):
            LOGGER.info("%s: No children for issue id %s, checking for links", self.NAME, self.issue_id)
            has_context = self._context_downloader and await self._context_downloader.has_pages(self.issue_id)
            if not has_context:
                LOGGER.info("%s: Issue %s is leaf without links, skipping summary", self.NAME, self.issue_id)
                return False
        return await super()._needs_summary_creation()

    async def _save_results(self, results: SummaryOutput) -> None:
        LOGGER.info("Saving partial summary for issue id %s", self.issue_id)
        self.summary_info.partial = results
        self.summary_info.set_modified()
        async with self._get_db_context() as service_dal:
            case = await service_dal.cases_dal.get_case(
                account_id=self.account_id, source_id=self.source_id, issue_id=self.issue_id, with_summary=False
            )
            await service_dal.issue_summary_dal.upsert_summary(case=case, summary=results)

    def get_relevant_issues(self) -> list[str]:
        relevant_issues = []
        for child_id in self.summaries_tree.graph.get_children(self.issue_id):
            if child_id not in self.summaries_tree:
                LOGGER.error("Issue %s is not in summaries tree", child_id)
                continue
            relevant_issues.append(child_id)
        return relevant_issues

    def _is_descendants_modified(self) -> bool:
        for child_issue_id in self.summaries_tree.graph.get_children(self.issue_id):
            if child_issue_id not in self.summaries_tree:
                LOGGER.error("Issue %s is not in summaries tree", child_issue_id)
                continue
            if self.summaries_tree[child_issue_id].is_modified():
                LOGGER.info("Issue id %s child %s has updated", self.issue_id, child_issue_id)
                return True
        return False

    async def _get_children_context(self) -> children_context_type:
        raw_data_issues: list[str] = []
        leaves_context_list: list[ExtraContextChild] = []
        containers_children_list: list[ExtraContextChild] = []
        for child_id in self.summaries_tree.graph.get_children(self.issue_id):
            # if leaf and has no extra context, we want the raw data
            child_is_container = self.summaries_tree.graph.has_children(child_id)
            has_context = self._context_downloader and await self._context_downloader.has_pages(child_id)
            if not child_is_container and not has_context:
                raw_data_issues.append(child_id)
                continue
            child_summary = self.summaries_tree[child_id]
            if child_summary.has_data() is False:
                LOGGER.error("Partial summary is missing for issue %s with child %s", child_id, child_summary.key)
                continue
            child_data = cast(SummaryOutputData, cast(SummaryOutput, child_summary.partial).data)
            lst_to_append = containers_children_list if child_is_container else leaves_context_list
            lst_to_append.append(
                ExtraContextChild(
                    extra_context_input=IssueExtraContextInput(
                        issue_id=child_summary.key,
                        questions_summary=child_data.questions_summary,
                    )
                )
            )
        LOGGER.info("Fetching raw data for %s issues: %s", self.issue_id, raw_data_issues)
        issues = await self._issue_manager.get_issues(raw_data_issues)
        leaves_raw_data_list = [
            BaseIssue(
                id=i.attributes.id_,
                summary=i.attributes.summary,
                description=i.description,
                type=i.attributes.issuetype,
            )
            for i in issues.values()
        ]
        return leaves_context_list, containers_children_list, leaves_raw_data_list

    async def _generate_summary(self) -> ContextPipelineOutput:
        LOGGER.info("Generating partial summary for issue id %s", self.issue_id)
        extra_contexts = await self._get_extra_context()
        leaves_context_list, containers_children_list, leaves_raw_data_list = await self._get_children_context()
        input_args = ContextPipelineInput(
            jira_issue=await self.get_gen_ai_issue(),
            leaves_context_list=leaves_context_list,
            leaves_raw_data_list=leaves_raw_data_list,
            containers_children_list=containers_children_list,
            additional_extra_contexts=extra_contexts,
        )
        partial_summary_result = await celery_manager_instance.send_and_wait_for_result(
            input_args=input_args.to_dict(),
            headers=self._celery_headers,
            task_name=GENAI_PARTIALLY_SUMMARY_TASK_NAME,
            queue=GENAI_PIPELINES_QUEUE,
        )
        partial_summary = ContextPipelineOutput(**partial_summary_result)
        LOGGER.info("Partial summary for issue id %s: %s", self.issue_id, partial_summary.model_dump())
        return partial_summary
