from datetime import datetime

from service.db import CaseTable
from service.k8s_jobs.base_job_logic import SummaryOutput, SummaryOutputData
from service.logic.issues_graph import IssuesGraph


class SummaryInfo:
    def __init__(
        self,
        key: str,
        timestamp: datetime,
        partial_hash: str | None = None,
        partial_version: str | None = None,
    ) -> None:
        self.key: str = key
        self.timestamp: datetime = timestamp
        self.partial: SummaryOutput | None = None
        if partial_hash and partial_version:
            self.partial = SummaryOutput(issue_hash=partial_hash, ai_version=partial_version)
        self._is_modified: bool = False

    def __str__(self) -> str:
        return f"{self.key}"

    def set_modified(self) -> None:
        self._is_modified = True

    def is_modified(self) -> bool:
        return self._is_modified

    def has_data(self) -> bool:
        return self.partial is not None and self.partial.data is not None

    def load_data(self, case: CaseTable) -> None:
        if case.partial:
            summary_data = None
            if case.partial.questions and case.partial.summary and case.partial.short:
                summary_data = SummaryOutputData(
                    questions=case.partial.questions,
                    questions_summary=case.partial.summary,
                    short_summary=case.partial.short,
                )
            self.partial = SummaryOutput(
                data=summary_data, issue_hash=case.partial.issue_hash, ai_version=case.partial.ai_version
            )


class SummariesTree:
    def __init__(self, issues_graph: IssuesGraph, summaries: dict[str, SummaryInfo]) -> None:
        self.graph = issues_graph
        self.summaries = summaries

    def is_empty(self) -> bool:
        return self.graph.size() == 0

    def __contains__(self, issue_id: str) -> bool:
        return issue_id in self.summaries

    def __getitem__(self, issue_id: str) -> SummaryInfo:
        return self.summaries[issue_id]

    def size(self) -> int:
        return self.graph.size()

    def get(self, issue_id: str) -> SummaryInfo | None:
        return self.summaries.get(issue_id)
