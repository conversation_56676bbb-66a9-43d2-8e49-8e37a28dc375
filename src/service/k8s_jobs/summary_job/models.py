from __future__ import annotations

import json
from datetime import datetime

from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import BaseModel, computed_field
from pydantic.dataclasses import dataclass
from pydantic_settings import BaseSettings

from service.models.jobs import JobState


@dataclass
class SummaryError:
    issue_id: str
    error: str


class SummaryJobArgs(BaseSettings):
    account_id: AccountIdType
    source_id: SourceIdType
    job_id: int
    force: bool
    last_run: datetime | None = None
    parent_id: str | None = None


class JobStatisticsResults(BaseModel):
    total_issues_in_account: int = 0
    total_issues_required_partial_summary: int = 0
    total_issues_finished_partial_summary: int = 0
    total_issues_with_errors: int = 0
    total_new_cases_created: int = 0


class FullSummaryJobReport(BaseModel):
    issues_total: int
    issues_need_processing_partial_summary: int
    issues_finished_partial_summary: int
    new_cases_created: int

    execution_status: bool
    duration_in_seconds: int = 0

    errors: int

    def as_str_report(self) -> str:
        return json.dumps({key.replace("_", " ").title() for key, value in self.model_dump().items()})

    @computed_field  # type: ignore[prop-decorator]
    @property
    def success_rate(self) -> float:
        issues_sent_to_ai = self.issues_need_processing_partial_summary
        if issues_sent_to_ai == 0:
            return 100
        return (1 - (self.errors / issues_sent_to_ai)) * 100

    @computed_field  # type: ignore[prop-decorator]
    @property
    def job_state(self) -> JobState:
        if self.execution_status is False:
            return JobState.TERMINATED
        if self.errors > 0:
            return JobState.COMPLETED_WITH_ERRORS
        return JobState.COMPLETED
