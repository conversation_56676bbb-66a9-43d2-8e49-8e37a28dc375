import logging

from prime_security_review_service_client import JobDesignDocsCreateArgs

from service.db import get_service_dal_context
from service.k8s_jobs.base_job_logic.job_utils import invoke_security_review_job
from service.models.jobs import JobClassificationCreateArgs

from ..base_job_logic import invoke_job
from ..run_job_main import run_main
from .models import SummaryJobArgs
from .summary_generation import FullSummaryGeneration

LOGGER = logging.getLogger("full-summary-generation")


async def main() -> None:
    job_vars = SummaryJobArgs()
    LOGGER.info("Running Full Summary Generation Job with args: %s", job_vars)
    async with get_service_dal_context() as service_dal:
        job_logic = FullSummaryGeneration(
            source_id=job_vars.source_id,
            account_id=job_vars.account_id,
            job_id=job_vars.job_id,
            service_dal=service_dal,
            force=job_vars.force,
            last_run=job_vars.last_run,
            parent_id=job_vars.parent_id,
        )
        await job_logic.start()

    classification_job_create_args = JobClassificationCreateArgs(
        source_id=job_vars.source_id, force=False, created_by=job_logic.created_by
    )
    await invoke_job(job_vars.account_id, classification_job_create_args)
    design_job_create_args = JobDesignDocsCreateArgs(force=False, created_by=job_logic.created_by)

    await invoke_security_review_job(job_vars.account_id, design_job_create_args)


if __name__ == "__main__":
    run_main(main)
