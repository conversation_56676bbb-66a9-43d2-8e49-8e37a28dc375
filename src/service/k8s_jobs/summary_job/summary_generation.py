from __future__ import annotations

import logging
from collections.abc import Async<PERSON><PERSON><PERSON>, <PERSON>wai<PERSON>, Callable, Coroutine, Generator
from datetime import UTC, datetime
from functools import lru_cache, wraps
from typing import Any

from prime_gen_ai_service_client import CeleryHeaders
from prime_logger import PrimeAttributes, add_prime_attributes_context
from prime_shared.common_dataclasses import PaginationArgs
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import AsyncRateLimit
from prime_utils.monitoring import report_exception
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_not_exception_type,
    stop_after_attempt,
    wait_random,
)

from service.config import get_config
from service.db import IssueDBSummaryInfo, ServiceDAL
from service.errors import IssueIdNotFoundInStorageError, SummaryParentIsMissingError
from service.job_type import JobType
from service.services_clients import get_all_files_info

from ..base_job_logic import GenAIBaseJob, PageDownloader
from ..base_job_logic.gen_ai_celery import celery_manager_instance
from .flows import BaseSummaryFlow, FlowArgs, PartialSummaryFlow
from .models import FullSummaryJobReport, JobStatisticsResults
from .summary_info import SummariesTree, SummaryInfo
from .summary_reporter import SummaryMetricsReporter

TOTAL_RUNNING_TASKS_PROGRESS = 90  # percentage
TASK_RETRY_ATTEMPTS = 3
GET_ISSUE_BATCH_SIZE = 100
LOGGER = logging.getLogger("summary_generation")
SUMMARIES_INFO_PAGINATION_LIMIT = 1000

AsyncMethod = Callable[[str], Coroutine[Any, Any, None]]


def increase_progress_after(
    func: Callable[[FullSummaryGeneration, Any], Awaitable[Any]] | Callable[[FullSummaryGeneration], Awaitable[Any]],
) -> Any:
    @wraps(func)
    async def wrapper(self: FullSummaryGeneration, *args: Any, **kwargs: Any) -> Any:
        result = await func(self, *args, **kwargs)
        await self._progress_manager.increase_step_progress()
        return result

    return wrapper


EXCLUDED_RETRY_EXCEPTIONS = (
    IssueIdNotFoundInStorageError,
    SummaryParentIsMissingError,
)

SUMMARY_RETRY_CONFIG = retry(
    retry=retry_if_not_exception_type(EXCLUDED_RETRY_EXCEPTIONS),
    reraise=True,
    stop=stop_after_attempt(TASK_RETRY_ATTEMPTS),
    wait=wait_random(min=1, max=3),
    before_sleep=before_sleep_log(LOGGER, logging.DEBUG),
)


async def get_summary_hashes_iterator(
    service_dal: ServiceDAL, account_id: AccountIdType, source_id: SourceIdType
) -> AsyncIterator[IssueDBSummaryInfo]:
    LOGGER.info("Loading existing summary_hashes for account %s and source %s", account_id, source_id)
    pagination_args = PaginationArgs(limit=9999, offset=0)
    while True:
        existing_hashes = await service_dal.issue_summary_dal.get_summaries_info(
            account_id, source_id, pagination_args=pagination_args
        )
        if len(existing_hashes) == 0:
            break
        for existing_hash in existing_hashes:
            yield existing_hash
        pagination_args.offset += len(existing_hashes)


class FullSummaryGeneration(GenAIBaseJob):
    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
        force: bool = False,
        last_run: datetime | None = None,
        parent_id: str | None = None,
    ) -> None:
        super().__init__(account_id, source_id, job_id, service_dal)
        self._force = force
        self._last_run: datetime | None = last_run
        self._parent_id = parent_id
        self._job_statistics_results = JobStatisticsResults()
        self._confluence_downloader = PageDownloader(self._account_id, self._source_id)

    @property
    def job_type(self) -> str:
        return JobType.SUMMARY.value

    async def _get_flow_args(self, issue_id: str, summaries_tree: SummariesTree) -> FlowArgs:
        flow_args = FlowArgs(
            issue_id=issue_id,
            summaries_tree=summaries_tree,
            account_id=self._account_id,
            source_id=self._source_id,
            issue_manager=await self._get_issue_manager(),
            ai_version=GenAIBaseJob.get_ai_version(),
            last_run=self._last_run,
            force=self._force,
            celery_headers=self._get_celery_headers(issue_id, "", {}),
            context_downloader=self._confluence_downloader,
        )
        return flow_args

    @SUMMARY_RETRY_CONFIG
    async def call_flow_with_retries(self, flow: BaseSummaryFlow) -> bool:
        return await flow.build()

    @increase_progress_after
    async def call_flow(self, flow: BaseSummaryFlow) -> None:
        LOGGER.info("%s: Starting to execute flow for issue %s", flow.NAME, flow.issue_id)
        with add_prime_attributes_context(PrimeAttributes(issue_id=flow.issue_id)):
            try:
                if await self.call_flow_with_retries(flow):  # noqa: SIM102
                    self._job_statistics_results.total_issues_finished_partial_summary += 1
            except Exception as ex:
                LOGGER.exception("%s: Error while executing method for issue %s", flow.NAME, flow.issue_id)
                await self._on_task_error(ex, flow.issue_id)
        LOGGER.info("%s: Finished executing flow for issue %s", flow.NAME, flow.issue_id)

    @increase_progress_after
    async def _create_summaries_tree(self) -> SummariesTree:
        LOGGER.info("Creating summaries tree")
        issues_tree = await self.get_issues_graph(self._parent_id)
        summaries = {} if issues_tree.is_empty() else await self._generate_summaries_info()
        return SummariesTree(issues_graph=issues_tree, summaries=summaries)

    async def _generate_summaries_info(self) -> dict[str, SummaryInfo]:
        document_type = await self.document_type()
        file_info = await get_all_files_info(self._account_id, self._source_id, document_type, self._last_run)
        summaries: dict[str, SummaryInfo] = {}
        min_time = datetime.min.replace(tzinfo=UTC)
        async for hash_result in get_summary_hashes_iterator(self._service_dal, self._account_id, self._source_id):
            issue_id_file = file_info.get(hash_result.issue_id)
            summary_issue = SummaryInfo(
                hash_result.issue_id,
                issue_id_file.timestamp if (issue_id_file and issue_id_file.timestamp) else min_time,
                hash_result.partial_hash,
                hash_result.partial_version,
            )
            summaries[hash_result.issue_id] = summary_issue
        return summaries

    async def _on_task_error(self, ex: Exception, issue_id: str) -> None:
        LOGGER.exception("Error raised while executing method for issue %s", issue_id)
        self._errors[issue_id] = str(ex)
        report_exception(ex)

    async def _generate_summary(self) -> None:
        LOGGER.info("Generating summary for %s and %s", self._account_id, self._source_id)
        summaries_tree = await self._create_summaries_tree()
        if summaries_tree.is_empty():
            LOGGER.info("No issues to summarize")
            return
        LOGGER.info("Total issues in account tree %s", summaries_tree.size())
        self._job_statistics_results.total_issues_in_account = summaries_tree.size()
        self._setup_progress_step(TOTAL_RUNNING_TASKS_PROGRESS / (summaries_tree.size() * 2))
        LOGGER.info("Starting to parallelize partial summary generation")
        await self._execute(summaries_tree.graph.iterator_from_bottom(), PartialSummaryFlow, summaries_tree)
        self._job_statistics_results.total_issues_with_errors = len(self._errors)

    async def _execute(
        self, tree_iterator: Generator[list[str]], flow_type: type[BaseSummaryFlow], summaries_tree: SummariesTree
    ) -> None:
        for i, issue_ids in enumerate(tree_iterator):
            issue_flows = []
            for issue_id in issue_ids:
                if issue_id in summaries_tree:
                    issue_flows.append(flow_type(await self._get_flow_args(issue_id, summaries_tree)))
                else:
                    LOGGER.error("Issue %s is not in the summaries tree", issue_id)
            tasks = [self.call_flow(issue_flow) for issue_flow in issue_flows if issue_flow.needs_processing()]
            LOGGER.info("%s - Executing %s tasks in level %s", flow_type.__name__, len(tasks), i)
            for result_future in AsyncRateLimit(get_config().max_workers).as_completed(tasks):
                try:
                    await result_future
                except Exception:
                    LOGGER.exception("Error while executing task")
            LOGGER.info("%s - Finished executing %s tasks in level %s", flow_type.__name__, len(tasks), i)
        LOGGER.info("%s - Finished execution", flow_type.__name__)

    async def _run(self) -> None:
        LOGGER.info("Running summary generation job")
        await self._generate_summary()
        celery_manager_instance._celery_app._app.close()
        LOGGER.info("Summary generation job finished")

    @lru_cache
    def _get_summary_celery_headers(self, issue_id: str) -> CeleryHeaders:
        return self._get_celery_headers(issue_id, "", {})

    async def report(self, execution_status: bool) -> None:
        reporter = SummaryMetricsReporter(
            account_id=self._account_id, source_id=self._source_id, job_id=self.get_job_id()
        )
        summary_report = FullSummaryJobReport(
            issues_total=self._job_statistics_results.total_issues_in_account,
            issues_need_processing_partial_summary=self._job_statistics_results.total_issues_required_partial_summary,
            issues_finished_partial_summary=self._job_statistics_results.total_issues_finished_partial_summary,
            new_cases_created=self._job_statistics_results.total_new_cases_created,
            errors=self._job_statistics_results.total_issues_with_errors,
            duration_in_seconds=self.duration,
            execution_status=execution_status,
        )
        LOGGER.info("Reporting job metrics %s", summary_report)
        reporter.report_job_metrics(summary_report)
