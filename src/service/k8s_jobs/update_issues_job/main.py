import logging

from service.db import get_service_dal_context
from service.job_type import JobType
from service.k8s_jobs.base_job_logic import invoke_job
from service.k8s_jobs.run_job_main import run_main
from service.models import JobPsvCreateArgs, JobSummaryCreateArgs

from .models import UpdateIssuesJobArgs
from .update_issues import UpdateIssuesJobLogic

LOGGER = logging.getLogger("update-provider-fields-job")


async def main() -> None:
    job_vars = UpdateIssuesJobArgs()
    LOGGER.info("Running Update Provider Fields Job with args %s", job_vars)
    async with get_service_dal_context() as service_dal:
        job_logic = await UpdateIssuesJobLogic.build(
            account_id=job_vars.account_id,
            source_id=job_vars.source_id,
            job_id=job_vars.job_id,
            service_dal=service_dal,
            last_update_at=job_vars.last_update_at,
            force=job_vars.force,
            update_fields_only=job_vars.update_fields_only,
        )
        await job_logic.start()

    if job_vars.update_fields_only is False and job_vars.source_id:
        job_summary_create_args = JobSummaryCreateArgs(
            created_by=job_logic.created_by, force=False, job=JobType.SUMMARY, source_id=job_vars.source_id
        )
        await invoke_job(job_vars.account_id, job_summary_create_args)

        job_psv_create_args = JobPsvCreateArgs(
            created_by=job_logic.created_by,
            force=False,
            job=JobType.SECURITY_VIOLATION,
            source_id=job_vars.source_id,
        )
        await invoke_job(job_vars.account_id, job_psv_create_args)


if __name__ == "__main__":
    run_main(main)
