from __future__ import annotations

import logging
from datetime import datetime
from typing import cast

from prime_file_manager_service_client import DocumentType
from prime_redis_utils import get_redis_for_account
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType

from service.config import get_config
from service.db import CaseTable, PsvTable, ServiceDAL, UpsertCasesBulkArgs
from service.job_type import JobType
from service.k8s_jobs.update_issues_job.update_case_progress import UpdateCaseProgress
from service.k8s_jobs.update_issues_job.update_design_review_links import UpdateDesignReviewLinks
from service.logic.filters_and_sort import CaseFilters, PSVFilters
from service.logic.issues import BasePrimeIssue, get_jira_fields_stored
from service.logic.issues_graph import GraphGenerator
from service.logic.issues_graph.issues_graph import IssuesGraph
from service.logic.jira_manager import JiraFieldInfo, JiraPrimeIssue
from service.models import provider_field_types
from service.services_clients import ServicesClients, get_all_files_info

from ..base_job_logic import BaseJobLogic, chunk_generator

TOTAL_RUNNING_TASKS_PROGRESS = 95  # percentage
LOGGER = logging.getLogger("update_issues_job")

BULK_SIZE = 200


class UpdateIssuesJobLogic(BaseJobLogic):
    async def report(self, success: bool) -> None:
        pass

    def __init__(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
        jira_fields: list[JiraFieldInfo] | None,
        update_fields_only: bool,
        since: datetime | None,
    ) -> None:
        super().__init__(account_id, source_id, job_id, service_dal)
        self._service_dal = service_dal
        self._jira_fields = jira_fields
        self._since = since
        self._update_fields_only = update_fields_only

    async def termination_signal_handler(self) -> None:
        LOGGER.info("Termination signal received")

    @classmethod
    async def build(
        cls,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
        last_update_at: datetime | None,
        force: bool,
        update_fields_only: bool,
    ) -> UpdateIssuesJobLogic:
        source = await ServicesClients.source_api().get_source(account_id=account_id, source_id=source_id)
        if not source:
            raise ValueError(f"No sources found for account {account_id}")
        redis_client = get_redis_for_account(account_id, get_config(), get_config().namespace)
        jira_fields = None
        if source.source_type == SourceType.JIRA:
            jira_fields = await get_jira_fields_stored(account_id, source.id, redis_client)
        result = cls(
            account_id=account_id,
            source_id=source.id,
            job_id=job_id,
            service_dal=service_dal,
            jira_fields=jira_fields,
            update_fields_only=update_fields_only,
            since=None if force else last_update_at,
        )
        return result

    @property
    def job_type(self) -> str:
        return JobType.UPDATE_ISSUES.value

    async def _run(self) -> None:
        LOGGER.info("Updating issues job for account %s and source %s", self._account_id, self._source_id)
        issues_to_process = await self.get_issues()
        self._setup_progress_step(TOTAL_RUNNING_TASKS_PROGRESS / (len(issues_to_process) / BULK_SIZE + 30))
        if issues_to_process:
            await self._update_provider_fields(issues_to_process)
        generator = GraphGenerator(self._service_dal, self._account_id, self._source_id)

        if await self.source_type() == SourceType.JIRA:
            issues_graph = await generator.update_since(since=self._since)
        else:
            issues_graph = await generator.load_from_db()
        await generator.write_to_to_redis(issues_graph, self._redis_client)
        if await self.source_type() == SourceType.JIRA:
            await self._update_missing_cases(issues_graph, generator)
        await UpdateCaseProgress(self._account_id, self._source_id, self._service_dal).update_case_progress(
            issues_graph, issues_to_process
        )
        await UpdateDesignReviewLinks(
            self._account_id, self._source_id, self._service_dal, self._redis_client
        ).update_design_review_links(issues_to_process)
        LOGGER.info("Updating issues for account %s and source %s done", self._account_id, self._source_id)

    async def _update_provider_fields(self, issues_ids: list[str]) -> None:
        LOGGER.info("Updating provider fields for %s issues", len(issues_ids))
        source_type = await self.source_type()
        for issues_bulk in chunk_generator(issues_ids, BULK_SIZE):
            issues = await (await self._get_issue_manager()).get_issues(issues_bulk)
            case_args = [
                UpsertCasesBulkArgs(
                    issue_id=issue_id, fields={"provider_fields": self._get_fields_data(prime_issue, source_type)}
                )
                for issue_id, prime_issue in issues.items()
                if issue_id in issues_bulk
            ]
            await self._service_dal.cases_dal.upsert_cases(self._account_id, self._source_id, case_args)
            await self._increase_step_progress()

    def _get_fields_data(self, issue: BasePrimeIssue, source_type: SourceType) -> dict[str, provider_field_types]:
        LOGGER.info("Getting fields data for issue %s", issue.attributes.id_)
        if source_type == SourceType.JIRA:
            if self._jira_fields is None:
                raise ValueError("jira_fields is not set with source of type Jira")
            jira_issue = cast(JiraPrimeIssue, issue)

            provider_fields = jira_issue.get_fields_data(self._jira_fields)
        else:
            provider_fields = issue.get_attributes_fields()
        return provider_fields

    async def _relevant_issues(self) -> set[str]:
        entities = [CaseTable.issue_id]
        cases = await self._service_dal.cases_dal.get_cases_by(
            account_id=self._account_id,
            source_id=self._source_id,
            case_filters=CaseFilters.base_workroom_filter(),
            entities=entities,
        )
        LOGGER.info("Found  %s security cases to update provider fields", len(cases))
        container_cases = await self._service_dal.cases_dal.get_cases_by(
            account_id=self._account_id,
            case_filters=CaseFilters.base_container_filter(),
            entities=entities,
        )
        LOGGER.info("Found %s container cases to update provider fields", len(container_cases))
        entities = [PsvTable.issue_id]
        psv_cases = await self._service_dal.psv_dal.get_psvs_by(
            account_id=self._account_id,
            source_id=self._source_id,
            psv_filters=PSVFilters.base_filter(),
            entities=entities,
        )
        LOGGER.info("Found %s psv cases to update provider fields", len(psv_cases))
        relevant_cases = set(list(cases) + list(container_cases) + list(psv_cases))
        LOGGER.info("Found %s relevant cases to update provider fields", len(relevant_cases))
        return relevant_cases

    async def get_issues(self) -> list[str]:
        since = None if self._update_fields_only else self._since
        issue_files = await get_all_files_info(
            self._account_id, self._source_id, document_type=DocumentType.JIRA, since=since
        )
        issues = list(issue_files.keys())
        if self._update_fields_only:
            relevant_cases = await self._relevant_issues()
            issues = [issue for issue in issues if issue in relevant_cases]
        LOGGER.info("Returning %s issues to update provider fields", len(issues))
        return issues

    async def _update_missing_cases(self, issues_graph: IssuesGraph, generator: GraphGenerator) -> None:
        LOGGER.info("Updating missing cases for issue")
        issue_files = await get_all_files_info(self._account_id, self._source_id, document_type=DocumentType.JIRA)
        vertices_to_delete = [issue_id for issue_id in issues_graph if issue_id not in issue_files]
        if vertices_to_delete:
            LOGGER.warning("Issues %s not found in file manager, removing from tree", vertices_to_delete)
            issues_graph.delete_vertices(vertices_to_delete)
            await generator.write_to_to_redis(issues_graph, self._redis_client)
        await self._increase_step_progress()
        filters = CaseFilters()
        db_issue_ids = await self._service_dal.cases_dal.get_issue_ids_by(self._account_id, self._source_id, filters)
        await self._increase_step_progress()
        db_issue_ids_set = set(db_issue_ids)
        missing = [issue_id for issue_id in issue_files if issue_id not in db_issue_ids_set]
        await self._increase_step_progress()
        LOGGER.warning("Found %s issues not in cases db", len(missing))
        if missing:
            await self._update_provider_fields(missing)
            generator = GraphGenerator(self._service_dal, self._account_id, self._source_id)
            issues_graph = await generator.update_by_issues(set(missing))
            await generator.write_to_to_redis(issues_graph, self._redis_client)
