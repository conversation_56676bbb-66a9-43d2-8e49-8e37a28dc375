import json
import logging
from datetime import UTC, datetime, timedelta
from typing import cast

from prime_file_manager_service_client import DocumentType
from prime_redis_utils import AsyncPrefixRedisClient
from prime_security_review_service_client import BodyCreateDesignDocsByReference
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_shared.consts import AUTO_USER
from prime_source_service_client.models import JiraConnectionDetails
from pydantic.dataclasses import dataclass

from service.db import ServiceDAL
from service.db.tables.cases import CaseTable
from service.logic.filters_and_sort.cases import CaseFilters
from service.logic.jira_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JiraPrimeIssue
from service.models import ProviderFieldType, provider_field_types
from service.models.filters_and_sort.filtering import Filter, Operator
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("update_design_review_links")

SUPPORTED_LINK_TYPES = [DocumentType.GDRIVE, DocumentType.CONFLUENCE]

ISSUE_FILE_SUFFIX = ".json"

DATADOG_CUSTOM_FIELD_ID = "customfield_19350"  # Security Review field ID in Jira
DATADOG_ISSUE_TYPE = "Security Review"
DATADOG_ACCOUNT_ID = "us-east-1_Ecwsi7Djd"


@dataclass
class CaseData:
    issue_id: str
    fields: dict[str, provider_field_types]
    case_id: int


class UpdateDesignReviewLinks:
    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        service_dal: ServiceDAL,
        redis_client: AsyncPrefixRedisClient,
    ) -> None:
        self._service_dal = service_dal
        self._account_id = account_id
        self._source_id = source_id
        self._redis_client = redis_client

    async def update_design_review_links(self, issues_ids: list[str]) -> None:
        LOGGER.info("Updating design review links for %s issues", len(issues_ids))
        design_review_projects = await self._get_design_review_projects()
        if not design_review_projects:
            LOGGER.info("No design review boards configured, skipping processing")
            return
        processed_count = 0
        error_count = 0
        cases_data = await self._get_cases_data(issues_ids, design_review_projects)
        jira_fields = await JiraFieldsManager.build(self._account_id, self._source_id, self._redis_client)
        for case_data in cases_data:
            try:
                await self._process_issue_links(case_data, jira_fields)
                processed_count += 1
            except Exception:
                LOGGER.exception("Error processing issue %s", case_data.issue_id)
                error_count += 1
        LOGGER.info("Completed processing: %d successful, %d errors", processed_count, error_count)

    async def _process_issue_links(self, case_data: CaseData, jira_fields: JiraFieldsManager) -> None:
        if self._account_id == DATADOG_ACCOUNT_ID:
            await self._datadog_process(case_data, jira_fields)

    async def _datadog_process(self, case_data: CaseData, jira_fields: JiraFieldsManager) -> None:
        security_review_fields = jira_fields.get_field_by_id(DATADOG_CUSTOM_FIELD_ID)
        if case_data.fields.get("issuetype") != DATADOG_ISSUE_TYPE:  # Ensure the issue type is correct
            LOGGER.info("Issue %s is not a Security Review, skipping", case_data.issue_id)
            return
        case_file = await ServicesClients.files_api().download_origin_file_for_source(
            self._account_id, self._source_id, f"{case_data.issue_id}.json"
        )
        jira_issue = JiraPrimeIssue.from_dict(json.loads(case_file))
        if link := jira_issue.get_field(security_review_fields).value:
            await self._add_link(case_data, cast(str, link))

    async def _get_design_review_projects(self) -> list[str]:
        try:
            connection_details = await ServicesClients.source_api().get_connection_details(
                self._account_id, self._source_id
            )
            if not connection_details or not isinstance(connection_details.actual_instance, JiraConnectionDetails):
                LOGGER.info("Source info is not set for source %s", self._source_id)
                return []
            jira_connection_details: JiraConnectionDetails = connection_details.actual_instance
            design_review_projects = jira_connection_details.design_review_projects
            if design_review_projects is None:
                LOGGER.info("Design review boards are not set for source %s", self._source_id)
                return []
        except Exception:
            LOGGER.exception("Error getting source info for source %s", self._source_id)
            return []
        return design_review_projects

    async def _add_link(self, case_data: CaseData, link: str) -> None:
        if "docs.google.com" not in link:
            LOGGER.warning("Link %s is not a Google docs link", link)
            return
        if await self._design_doc_exists(link):
            return
        await (
            ServicesClients()
            .design_docs()
            .create_design_docs_by_reference(
                account_id=self._account_id,
                body_create_design_docs_by_reference=BodyCreateDesignDocsByReference(
                    creator=str(case_data.fields.get("creator", AUTO_USER)),
                    url=link,
                    case_id=case_data.case_id,
                ),
            )
        )
        LOGGER.info("Created reference design doc for issue %s", case_data.issue_id)

    async def _design_doc_exists(self, url: str) -> bool:
        # TODO: this doest get all the urls due to pagination, need to handle that
        design_docs = await ServicesClients().design_docs().get_design_docs(self._account_id)
        return any(design_doc.url == url for design_doc in design_docs.results)

    async def _get_cases_data(self, issue_ids: list[str], projects: list[str]) -> list[CaseData]:
        since_date_filter = Filter(
            field="provider_fields.created",
            value=(datetime.now(UTC) - timedelta(days=7)).isoformat(),
            op=Operator.GTE,
            inner_field_type=ProviderFieldType.DATE,
        )
        case_fileter = Filter(field="provider_fields.project", value=projects, op=Operator.EQ)
        cases = await self._service_dal.cases_dal.get_cases_by(
            account_id=self._account_id,
            source_id=self._source_id,
            issues=issue_ids,
            entities=[CaseTable.id, CaseTable.issue_id, CaseTable.provider_fields],
            case_filters=CaseFilters(filters=[case_fileter, since_date_filter]),
        )

        cases_data: list[CaseData] = []
        for case_id, issue_id, provider_fields in cases:
            project = provider_fields.get("project", "") if provider_fields else ""
            if not project or project not in projects:  # double check if project is in boards
                LOGGER.warning("Project %s of issue %s is not in design review boards", project, issue_id)
                continue
            cases_data.append(CaseData(case_id=case_id, issue_id=issue_id, fields=provider_fields))
        return cases_data
