import logging
from math import ceil

from prime_shared.common_types import AccountIdType, SourceIdType

from service.db import CaseTable, ServiceDAL
from service.logic.issues_graph.issues_graph import IssuesGraph

DONE_STATUS = ["done", "done_with_issues", "completed", "closed", "resolved"]
LOGGER = logging.getLogger("update_issues_job")


def _is_done(status: str) -> bool:
    return str(status).lower() in DONE_STATUS


class UpdateCaseProgress:
    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        service_dal: ServiceDAL,
    ) -> None:
        self._service_dal = service_dal
        self._account_id = account_id
        self._source_id = source_id

    async def update_case_progress(self, issues_graph: IssuesGraph, issues_ids: list[str]) -> None:
        LOGGER.info("Updating case progress for %s issues", len(issues_ids))
        updated_container: dict[str, tuple[int, int]] = {}
        for issue_id in issues_ids:
            try:
                childs_ids = issues_graph.get_all_descendants(issue_id)
                if childs_ids:
                    progress_percentage, container_progress = await self._handle_container_progress(
                        issue_id, childs_ids
                    )
                    updated_container[issue_id] = (progress_percentage, container_progress)
            except Exception:
                LOGGER.exception("Error updating case progress for %s", issue_id)
        LOGGER.info("Updated case progress for %s issues", len(updated_container))

    async def _handle_container_progress(self, issue_id: str, childs_ids: list[str]) -> tuple[int, int]:
        total_childs = len(childs_ids)
        total_completed_childs = 0
        db_issue_id_provider_fields = await self._service_dal.cases_dal.get_cases_by(
            self._account_id,
            self._source_id,
            issues=childs_ids + [issue_id],
            entities=[CaseTable.issue_id, CaseTable.provider_fields, CaseTable.progress_percentage],
        )
        container_progress = 0
        for i_id, provider_field, issue_progress_percentage in db_issue_id_provider_fields:
            if i_id == issue_id:
                container_progress = issue_progress_percentage
            if _is_done(provider_field.get("status", "")):
                total_completed_childs += 1
        progress_percentage = ceil(max(0, min((total_completed_childs / total_childs) * 100, 100)))
        if progress_percentage != container_progress:
            await self._service_dal.cases_dal.update_case(
                self._account_id, self._source_id, issue_id, progress_percentage=progress_percentage
            )
            LOGGER.info("Updated case progress for %s from %s to %s", issue_id, container_progress, progress_percentage)
        return progress_percentage, container_progress
