import json
import logging
import os
from collections import defaultdict
from typing import Optional

from datadog import initialize, statsd
from prime_file_manager_service_client import DocumentType, FileInfo, FileOriginRequest
from prime_gen_ai_service_client import BinaryExtraContextInput, FileData
from prime_logger import get_prime_attributes
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import AsyncRateLimit, unzip_data

from service.config import get_config
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("page_downloader_utils")

DD_METRICS_PREFIX = "prime.job.page_downloader"


class PageDownloaderMetricsReporter:
    def __init__(self, account_id: AccountIdType, source_id: SourceIdType):
        self.account_id = account_id
        self.source_id = source_id
        self._base_tags = frozenset(
            [
                f"account_id:{self.account_id}",
                f"source_id:{self.source_id}",
                f"job_id:{get_prime_attributes().job_id or 'unknown'}",
                f"namespace:{get_config().namespace}",
            ]
        )
        # Datadog automatically picks up API key from DD_API_KEY environment variable
        statsd_host = os.getenv("DD_AGENT_HOST", "localhost")
        initialize(statsd_host=statsd_host)

    def increment_download_extra_content_page_errors(self, count: int = 1) -> None:
        tags = list(self._base_tags)
        statsd.increment(f"{DD_METRICS_PREFIX}.download_extra_content_page_errors.increment", count, tags=tags)


class PageDownloader:
    def __init__(self, account_id: AccountIdType, source_id: SourceIdType) -> None:
        self._account_id = account_id
        self._source_id = source_id
        self._not_found_pages: set[str] = set()
        self._reporter = PageDownloaderMetricsReporter(account_id=self._account_id, source_id=self._source_id)

    def _parse_page(
        self, file_name: str, file_data: bytes, source_id: SourceIdType, issue_id: str, file_info: FileInfo
    ) -> BinaryExtraContextInput | FileData | None:
        try:
            if file_info.document_type == DocumentType.GDRIVE:
                return self._handle_gdrive_file(file_data, issue_id, file_info)
            else:
                return self._handle_confluence_file(file_data, issue_id)
        except (KeyError, json.JSONDecodeError):
            LOGGER.exception(
                "Failed to parse file %s from source %s for issue %s, file data: %s",
                file_name,
                source_id,
                issue_id,
                file_data,
            )
            self._reporter.increment_download_extra_content_page_errors()
        return None

    async def _download_and_fetch_pages(
        self, issue_id: str, source_id: SourceIdType, files_info: list[FileInfo]
    ) -> list[BinaryExtraContextInput | FileData]:
        req = FileOriginRequest(file_names=[f.origin_id for f in files_info])
        LOGGER.info(
            "Downloading extra content files %s from source %s for issue %s", req.file_names, source_id, issue_id
        )
        try:
            response = await ServicesClients.files_api().download_files_for_source(
                account_id=self._account_id, source_id=source_id, file_origin_request=req
            )
            found = []
            found_origin_ids = []

            for file_name, file_data in unzip_data(response).items():
                file_info = next((file_info for file_info in files_info if file_info.origin_id == file_name), None)

                if file_info is None:
                    LOGGER.warning(
                        "File %s not found in files_info for issue %s, source %s",
                        file_name,
                        issue_id,
                        source_id,
                    )
                    continue

                if page := self._parse_page(file_name, file_data, source_id, issue_id, file_info):
                    found.append(page)
                    found_origin_ids.append(file_name)
            if len(found_origin_ids) != len(req.file_names):
                LOGGER.warning(
                    "Found %s pages out of %s for issue %s, [%s] != [%s]",
                    len(found),
                    len(files_info),
                    issue_id,
                    found_origin_ids,
                    req.file_names,
                )
            return found
        except Exception:
            LOGGER.exception(
                "Failed to download files %s from source %s for issue %s",
                req.file_names,
                source_id,
                issue_id,
            )
            self._reporter.increment_download_extra_content_page_errors()
            return []

    async def has_pages(self, issue_id: str) -> bool:
        descendants = await ServicesClients.relationship_api().get_descendants(
            self._account_id, self._source_id, f"{issue_id}.json"
        )
        if any(
            d.document_type in [DocumentType.CONFLUENCE, DocumentType.DOCUMENT, DocumentType.GDRIVE]
            and d.source_id is not None
            for d in descendants
        ):
            LOGGER.debug("Found descendants for issue [%s]", issue_id)
            return True
        return False

    async def get_pages(self, issue_id: str) -> list[BinaryExtraContextInput | FileData]:
        descendants = await ServicesClients.relationship_api().get_descendants(
            self._account_id, self._source_id, f"{issue_id}.json"
        )
        LOGGER.debug("Found [%s] descendants for issue [%s]", len(descendants), issue_id)
        file_descendants_per_source = defaultdict(list)

        for d in descendants:
            if (
                d.document_type in [DocumentType.CONFLUENCE, DocumentType.DOCUMENT, DocumentType.GDRIVE]
                and d.source_id is not None
            ):
                LOGGER.debug("Will download content file %s for issue %s", d.origin_id, issue_id)
                file_descendants_per_source[d.source_id].append(d)
            else:
                LOGGER.debug(
                    "Skipping content file %s for issue %s with type %s", d.origin_id, issue_id, d.document_type
                )
        if not file_descendants_per_source:
            return []
        tasks = await AsyncRateLimit(len(file_descendants_per_source)).gather_tasks(
            [
                self._download_and_fetch_pages(issue_id, source_id, descendants)
                for source_id, descendants in file_descendants_per_source.items()
            ]
        )
        found = []
        for result in tasks:
            found.extend(result)
        return found

    def _handle_gdrive_file(
        self, file_data: bytes, issue_id: str, file_info: FileInfo
    ) -> Optional[BinaryExtraContextInput]:
        if len(file_data) < 5:
            LOGGER.warning("file length is less than 5 bytes [%s]", file_info.origin_id)
            return None

        file_type = "txt"
        if file_data[0:4] == b"%PDF":
            file_type = "pdf"

        return BinaryExtraContextInput(file_bytes=file_data, file_format=file_type, file_name=file_info.origin_id)

    def _handle_confluence_file(self, file_data: bytes, issue_id: str) -> FileData:
        file_json = json.loads(file_data)
        return FileData(
            issue_id=issue_id,
            link_url=file_json["links"]["webui"],
            location_in_ticket="description",
            title=file_json["title"],
            description=file_json["body"]["storage"]["value"],
        )
