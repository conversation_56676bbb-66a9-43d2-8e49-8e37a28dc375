import logging
from collections.abc import Generator
from datetime import UTC, datetime
from typing import TypeVar

from aiohttp import ClientResponse, ClientSession
from prime_redis_utils import AsyncPrefixRedisClient
from prime_security_review_service_client import JobDesignDocsCreateArgs
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import alru_cache

from service.config import get_config
from service.db import ServiceDAL
from service.logic.issues_graph import GraphGenerator, IssuesGraph
from service.models.jobs import JobCreateArg

LOGGER = logging.getLogger(__name__)

T = TypeVar("T")


@alru_cache
async def get_issues_graph(
    account_id: AccountIdType,
    source_id: SourceIdType,
    service_dal: ServiceDAL,
    redis_client: AsyncPrefixRedisClient,
    parent_id: str | None = None,
) -> IssuesGraph:
    generator = GraphGenerator(service_dal, account_id, source_id)
    issues_graph = await generator.load(redis_client)
    if parent_id:
        LOGGER.info("Getting create_subgraph for parent_id:%s", parent_id)
        issues_graph = issues_graph.create_subgraph(parent_id)
    return issues_graph


def to_utc(datetime_obj: datetime) -> datetime:
    if datetime_obj.tzinfo is None:
        return datetime_obj.replace(tzinfo=UTC)
    return datetime_obj


def _get_rat_jobs_url(account_id: AccountIdType) -> str:
    return f"{get_config().rat_logic_service_url}/jobs/{account_id}/"


async def invoke_job(account_id: AccountIdType, job_create_args: JobCreateArg) -> ClientResponse:
    LOGGER.info("Spawning job with args %s", job_create_args)
    async with (
        ClientSession() as session,
        session.post(_get_rat_jobs_url(account_id), json=job_create_args.model_dump(mode="json")) as response,
    ):
        res = await response.json()
    LOGGER.info("%s Job invoked with status: %s", job_create_args.job, res)
    return response


def _get_security_jobs_url(account_id: AccountIdType) -> str:
    return f"{get_config().security_review_service_url}/jobs/{account_id}/"


async def invoke_security_review_job(
    account_id: AccountIdType, job_create_args: JobDesignDocsCreateArgs
) -> ClientResponse:
    # TODO: merge with invoke_job
    LOGGER.info("Spawning job with args %s", job_create_args)
    async with (
        ClientSession() as session,
        session.post(_get_security_jobs_url(account_id), json=job_create_args.model_dump(mode="json")) as response,
    ):
        res = await response.json()
    LOGGER.info("%s Job invoked with status: %s", job_create_args.job, res)
    return response


def chunk_generator(issues: list[T], bulk_size: int) -> Generator[list[T]]:
    for i in range(0, len(issues), bulk_size):
        yield issues[i : i + bulk_size]
