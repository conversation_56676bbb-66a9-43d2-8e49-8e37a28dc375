from prime_shared.common_types import AccountIdType, SourceIdType
from prime_source_service_client import SourceType
from prime_utils import alru_cache

from service.logic.issues import BaseIssuesManager, BasePrimeIssue, LocalIssuesManager
from service.logic.jira_manager import JiraIssues<PERSON>anager
from service.services_clients import ServicesClients

manager_t = BaseIssuesManager[BasePrimeIssue]

_TYPES_TO_MANAGERS: dict[SourceType, type[manager_t]] = {
    SourceType.JIRA: JiraIssuesManager,
    SourceType.DESIGNDOCS: LocalIssuesManager,
}


class IssueManagerFactory:
    @classmethod
    @alru_cache
    async def _get_source_type(cls, account_id: AccountIdType, source_id: SourceIdType) -> SourceType:
        source = await ServicesClients.source_api().get_source(account_id, source_id)
        return source.source_type

    @classmethod
    async def get_issue_manager(cls, account_id: AccountIdType, source_id: SourceIdType) -> manager_t:
        source_type = await cls._get_source_type(account_id, source_id)
        return cls.get_issue_manager_by_type(source_type)(account_id, source_id)

    @classmethod
    def get_issue_manager_by_type(cls, source_type: SourceType) -> type[manager_t]:
        return _TYPES_TO_MANAGERS[source_type]
