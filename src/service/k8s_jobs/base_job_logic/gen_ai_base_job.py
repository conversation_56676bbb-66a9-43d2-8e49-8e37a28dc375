import hashlib
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from functools import lru_cache
from typing import NamedTuple, cast

from packaging.version import Version, parse
from prime_celery import PrimeCelery
from prime_gen_ai_service_client import CeleryHeaders, ContextIssue, ContextSummaryInput, QuestionsInput
from prime_gen_ai_service_client import Issue as GenAIIssue
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import alru_cache

from service.config import get_config
from service.db import ServiceDAL
from service.logic.issues import (
    BasePrimeIssue,
    GenericPrimeIssue,
    PrimeIssueAttributes,
    get_jira_fields_stored,
)
from service.logic.issues_graph import IssuesGraph
from service.logic.jira_manager import JiraFieldInfo

from .base_job_logic import BaseJobLogic
from .gen_ai_celery import GENAI_PIPELINE_VERSION_TASK_NAME, GENAI_VERSION_QUEUE
from .job_utils import get_issues_graph

LOGGER = logging.getLogger("gen_ai_base_job")


class IssueInfo(NamedTuple):
    issue: BasePrimeIssue
    summary_input: ContextSummaryInput | None


class GenAIBaseJob(BaseJobLogic, ABC):
    @classmethod
    def to_ai_issue(cls, issue: BasePrimeIssue) -> GenAIIssue:
        return GenAIIssue(
            id=issue.attributes.id_,
            summary=issue.attributes.summary,
            description=issue.description,
            type=issue.attributes.issuetype,
            parent=cls.to_ai_issue(issue.parent) if issue.parent else None,
        )

    @classmethod
    def _build_hierarchy(cls, issue: BasePrimeIssue) -> list[BasePrimeIssue]:
        issue_stack: list[BasePrimeIssue] = []
        current_issue: BasePrimeIssue | None = issue
        while current_issue is not None:
            issue_stack.append(current_issue)
            current_issue = current_issue.parent
        return issue_stack

    @classmethod
    async def get_context_issue_from_db(
        cls, issue: BasePrimeIssue, service_dal: ServiceDAL, account_id: AccountIdType, source_id: SourceIdType
    ) -> ContextIssue:
        issue_stack: list[BasePrimeIssue] = cls._build_hierarchy(issue)
        issue_ids = [issue.key for issue in issue_stack]
        try:
            summaries_dict = await service_dal.issue_summary_dal.get_summaries(account_id, source_id, issue_ids)
            LOGGER.info("Found summaries for issues: %s", summaries_dict.keys())
        except Exception:
            LOGGER.warning("Error fetching summaries for issues: %s", issue_ids)
            summaries_dict = {}
        summaries = {}
        for issue_id, summary in summaries_dict.items():
            summaries[issue_id] = ContextSummaryInput(
                questions=QuestionsInput.model_validate(summary.questions.model_dump(), strict=False),
                questions_summary=summary.summary,
                short_summary=summary.short,
            )
        return cls.to_context_issue(issue, summaries)

    @classmethod
    def to_context_issue(cls, issue: BasePrimeIssue, summaries: dict[str, ContextSummaryInput]) -> ContextIssue:
        context_issue: ContextIssue | None = None
        issue_stack: list[BasePrimeIssue] = cls._build_hierarchy(issue)
        while issue_stack:
            issue_item = issue_stack.pop()  # Pop from the end (deepest parent first)
            context_issue = ContextIssue(
                id=issue_item.attributes.id_,
                summary=issue_item.attributes.summary,
                description=issue_item.description,
                type=issue_item.attributes.issuetype,
                parent=context_issue,  # Use the previously created context_issue as parent
                context=summaries.get(issue_item.key, None),
            )
        return cast(ContextIssue, context_issue)

    @classmethod
    def to_prime_issue(
        cls, issue: GenAIIssue, created: datetime, creator: str | None = None, project: str | None = None
    ) -> GenericPrimeIssue:
        return GenericPrimeIssue(
            attributes=PrimeIssueAttributes(
                id=issue.id,
                summary=issue.summary or "SUMMARY",
                issuetype=issue.type or "ISSUE",
                creator=creator,
                created=created,
                project=project,
            ),
            description=issue.description,
            parent=cls.to_prime_issue(issue.parent, created, creator, project) if issue.parent else None,
        )

    def _get_celery_headers(self, issue_id: str, results_queue_name: str, additional: dict[str, str]) -> CeleryHeaders:
        return CeleryHeaders(
            issue_id=issue_id,
            account_id=self._account_id,
            source_id=self._source_id,
            job_id=self._job_id,
            user_id=self._created_by,
            results_queue_name=results_queue_name,
            additional_properties=additional,
        )

    @lru_cache
    @staticmethod
    def get_ai_version() -> Version:
        LOGGER.info("Getting ai version")
        app_name = f"{get_config().namespace}-base-get-version"
        celery_app = PrimeCelery(app_name, get_config(), get_config().celery_redis)
        celery_app._app.conf.task_routes = {GENAI_PIPELINE_VERSION_TASK_NAME: {"queue": GENAI_VERSION_QUEUE}}
        gen_ai_version_task = celery_app.send_task(GENAI_PIPELINE_VERSION_TASK_NAME, queue=GENAI_VERSION_QUEUE)
        ai_version = Version(**(gen_ai_version_task.get(timeout=60)))
        LOGGER.info("AI version is %s", ai_version)
        return ai_version

    @classmethod
    def get_issue_hash(cls, issue: BasePrimeIssue) -> str:
        LOGGER.info("Getting issue %s hash", issue.attributes.id_)
        calc_hash = hashlib.sha256(cls.to_ai_issue(issue).model_dump_json().encode("utf-8")).hexdigest()
        return calc_hash

    @alru_cache
    async def _get_jira_fields(self) -> list[JiraFieldInfo]:
        return await get_jira_fields_stored(self._account_id, self._source_id, self._redis_client)

    async def get_issues_graph(self, parent_id: str | None = None) -> IssuesGraph:
        return await get_issues_graph(
            self._account_id, self._source_id, self._service_dal, self._redis_client, parent_id
        )

    @lru_cache
    @staticmethod
    def version_changed(current_version: str | None, ai_version: Version | None) -> bool:
        if not current_version or not ai_version:
            return True
        current_version_parsed = parse(current_version)
        return current_version_parsed.major < ai_version.major or current_version_parsed.minor < ai_version.minor

    @abstractmethod
    async def report(self, execution_status: bool) -> None:
        pass

    async def termination_signal_handler(self) -> None:
        try:
            await self.report(False)
        except Exception:
            LOGGER.exception("Error during termination handling")
        finally:
            self._shutdown_event.set()
