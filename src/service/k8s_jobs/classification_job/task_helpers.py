from __future__ import annotations

import logging

from prime_gen_ai_service_client import (
    AIPipelinesOutput,
    MitreAttackConcernsFlowOutput,
    PersonalActionableRecommendation,
    PersonalRecommendationOutput,
    PersonalRecommendationsFlowOutput,
    PrivacyConcernsFlowOutput,
)
from prime_shared.common_dataclasses import SecurityFramework

from service.models import (
    CodeType,
    ConcernType,
    Implementation,
    ImplementationStatus,
    IssueAnalysisConcern,
    IssueAnalysisConcernMethodology,
    IssueLinks,
    SecurityControl,
)

from ..base_job_logic import SummaryOutput, SummaryOutputData
from .models import ClassificationJobLogicResult

LOGGER = logging.getLogger("classification-job")

MAX_POD_TIME_SECONDS = 60 * 60 * 24  # 24 hours


def _handle_code_snippet(snippet: str) -> str:
    try:
        snippet = snippet.strip()
        lines = snippet.splitlines()
        whitespace_len = 10000  # some max number, doesn't really matter
        for line in lines[1:]:
            if line:
                whitespace_len = min(whitespace_len, len(line) - len(line.strip()))
        if lines[0].strip().endswith((":", "{", "(")):
            whitespace_len = max(0, whitespace_len - 4)
        lines = [lines[0]] + [line[whitespace_len:] for line in lines[1:]]
        snippet = "\n".join(lines)
    except Exception:
        LOGGER.exception("Failed to handle code snippet")
    return snippet


def _get_concerns(
    privacy_concerns: PrivacyConcernsFlowOutput, mitre_attack_concerns: MitreAttackConcernsFlowOutput
) -> list[IssueAnalysisConcern]:
    concerns = []
    next_id = 1

    for concern in privacy_concerns.concerns:
        concerns.append(
            IssueAnalysisConcern(
                id=next_id,
                short_description=concern.group_name,
                long_description=concern.concern_text,
                methodology=IssueAnalysisConcernMethodology(
                    category=concern.linddun_category,
                    type=ConcernType.LINDDUN,
                ),
            )
        )
        next_id += 1

    for c in mitre_attack_concerns.concerns:
        concerns.append(
            IssueAnalysisConcern(
                id=next_id,
                short_description=c.group_name,
                long_description=c.concern_text,
                methodology=IssueAnalysisConcernMethodology(
                    category=c.tactic_id,
                    type=ConcernType.MITRE,
                ),
            )
        )
        next_id += 1

    return concerns


def convert_to_controls(personal_recommendations: list[PersonalRecommendationOutput]) -> list[SecurityControl]:
    ret_controls = []
    idx = 1
    for personal_rec in personal_recommendations:
        for rec in personal_rec.recommendations:
            ret_controls.append(
                SecurityControl(
                    id=str(idx),
                    name=rec.title,
                    description=rec.description,
                    control_names=[control.id_ for control in sum(rec.controls.values(), [])],
                    framework=SecurityFramework.PRIME,
                )
            )
            idx += 1
    return ret_controls


def _get_framework_recommendations(
    personal_recommendations: list[PersonalRecommendationOutput], concerns: list[IssueAnalysisConcern]
) -> list[Implementation]:
    recommendations = []
    for concern_group in personal_recommendations:
        concern_id = next(c for c in concerns if c.long_description == concern_group.concern.concern_text).id
        for recommendation_group in concern_group.recommendations:
            controls = sum(recommendation_group.controls.values(), [])
            for control in controls:
                for control_recommendation in control.actionable_recommendations:
                    recommendations.append(  # noqa: PERF401
                        Implementation(
                            id=0,  # we will set it later
                            concern_id=concern_id,
                            control_id=control.id_,
                            recommendation=control_recommendation.text,
                            status=ImplementationStatus.UNKNOWN,
                            raci=control_recommendation.raci,
                            controls={},
                        )
                    )
    return recommendations


def _get_implementation_controls(actionable_recommendation: PersonalActionableRecommendation) -> dict[str, set[str]]:
    implementation_controls: dict[str, set[str]] = {}

    if actionable_recommendation.controls is None:
        return implementation_controls

    for framework, items in actionable_recommendation.controls.items():
        implementation_controls[framework] = set()
        for control in items:
            implementation_controls[framework].add(control.id_)

    return implementation_controls


def _get_personal_recommendations(
    personal_recommendations: list[PersonalRecommendationOutput],
    concerns: list[IssueAnalysisConcern],
    controls: list[SecurityControl],
) -> list[Implementation]:
    implementations = []
    for recommendation_group in personal_recommendations:
        concern_id = next(c for c in concerns if c.long_description == recommendation_group.concern.concern_text).id
        for rec in recommendation_group.recommendations:
            control_id = next(c for c in controls if c.name == rec.title).id
            for actionable_recommendation in rec.actionable_recommendations:
                implementation_controls = _get_implementation_controls(actionable_recommendation)
                implementations.append(  # noqa: PERF401
                    Implementation(
                        id=0,  # we will set it later
                        concern_id=concern_id,
                        control_id=control_id,
                        recommendation=actionable_recommendation.text,
                        status=ImplementationStatus.UNKNOWN,
                        raci=[action_raci.name for action_raci in actionable_recommendation.raci],
                        code_snippets=(
                            {CodeType.JAVA: _handle_code_snippet(actionable_recommendation.code_snippet)}
                            if actionable_recommendation.code_snippet
                            else {}
                        ),
                        controls=implementation_controls,
                    )
                )
    return implementations


def get_recommendations_and_controls(
    personal_recommendations: list[PersonalRecommendationOutput], concerns: list[IssueAnalysisConcern]
) -> tuple[list[SecurityControl], list[Implementation]]:
    recommendations: list[Implementation] = []
    recommendations.extend(_get_framework_recommendations(personal_recommendations, concerns))
    controls = convert_to_controls(personal_recommendations)
    recommendations.extend(_get_personal_recommendations(personal_recommendations, concerns, controls))
    return controls, recommendations


def get_recommendations_and_concerns(
    personal_recommendation_output: PersonalRecommendationsFlowOutput | None,
    privacy_concerns: PrivacyConcernsFlowOutput | None,
    mitre_attack_concerns: MitreAttackConcernsFlowOutput | None,
) -> tuple[list[IssueAnalysisConcern], list[SecurityControl], list[Implementation]]:
    personal_recommendation_output = personal_recommendation_output or PersonalRecommendationsFlowOutput(
        personal_recommendations=[]
    )
    privacy_concerns_output = privacy_concerns or PrivacyConcernsFlowOutput(concerns=[])
    mitre_attack_concerns = mitre_attack_concerns or MitreAttackConcernsFlowOutput(concerns=[])
    concerns = _get_concerns(privacy_concerns_output, mitre_attack_concerns)

    controls, recommendations = get_recommendations_and_controls(
        personal_recommendation_output.personal_recommendations, concerns
    )
    for i, rec in enumerate(recommendations, start=1):
        rec.id = i
    return concerns, controls, recommendations


def get_summary(output: AIPipelinesOutput, research_package_version: str, issue_hash: str) -> SummaryOutput | None:
    if output.summary:
        return SummaryOutput(
            data=SummaryOutputData(
                questions=output.summary.questions,
                questions_summary=output.summary.questions_summary,
                short_summary=output.summary.short_summary or "",
            ),
            issue_hash=issue_hash,
            ai_version=research_package_version,
        )
    return None


def ai_output_to_classification_task_results(  # noqa: C901
    output: AIPipelinesOutput,
    case_id: int,
    issue_hash: str,
    research_package_version: str,
    issue_links: list[IssueLinks],
) -> ClassificationJobLogicResult:
    cia_score = output.cia.cia_score if output.cia else None
    concerns, controls, recommendations = get_recommendations_and_concerns(
        output.personal_recommendations, output.privacy_concerns, output.mitre_attack_concerns
    )
    result = ClassificationJobLogicResult(
        case_id=case_id,
        classification=output.yes_no.needs_security if output.yes_no else False,
        issue_hash=issue_hash,
        short_assessment=output.assessment_result.short_summary if output.assessment_result else "",
        long_assessment=output.assessment_result.long_summary if output.assessment_result else "",
        risk_score=int(output.risk_score.average_score) if output.risk_score else None,
        confidence=int(output.risk_score.confidence) if output.risk_score else None,
        recommendations=recommendations,
        concerns=concerns,
        controls=controls,
        research_package_version=research_package_version,
        confidentiality_score=cia_level_to_score(cia_score.confidentiality.score if cia_score else None),
        integrity_score=cia_level_to_score(cia_score.integrity.score if cia_score else None),
        availability_score=cia_level_to_score(cia_score.availability.score if cia_score else None),
        keywords=[],
        is_automated=output.ticket_automated.is_automated if output.ticket_automated else False,
        is_security_enhancement=output.security_enhancement.answer if output.security_enhancement else False,
        issue_links=issue_links,
        fire_summary=output.fire.fire_summary if output.fire else None,
        summary=get_summary(output, research_package_version, issue_hash),
    )
    if result.classification and result.risk_score == 0:
        result.risk_score = 1
    return result


def cia_level_to_score(level: str | None) -> int | None:
    if level is None:
        return None
    match level.lower():
        case "low":
            return 1
        case "medium":
            return 5
        case "high":
            return 10
    raise ValueError(f"Invalid level {level}")
