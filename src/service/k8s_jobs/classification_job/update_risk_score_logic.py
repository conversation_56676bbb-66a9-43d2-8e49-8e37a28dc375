from __future__ import annotations

import logging
from collections import Counter, defaultdict

from prime_shared.common_types import AccountIdType, SourceIdType

from service.db import ServiceDAL
from service.logic import get_container_children_for_score
from service.logic.issues_graph import IssuesGraph
from service.models.levels import RiskScoreCategory, risk_score_to_category

from .models import ClassificationJobLogicResult

LOGGER = logging.getLogger("update_risk_score_logic")

ANALYZE_RISK_FACTOR = 3
INTERVENE_RISK_FACTOR = 6
MONITOR_RISK_FACTOR = 1
FLUSH_COUNTER = 100


class UpdateRiskScoreLogic:
    def __init__(self, service_dal: ServiceDAL):
        self._service_dal = service_dal
        self._flush_counter = 0

    @staticmethod
    def _calculate_risk_score(scores_vs_count: dict[RiskScoreCategory, int]) -> int:
        total = sum(scores_vs_count.values())
        if total == 0:
            return 0

        intervene = scores_vs_count[RiskScoreCategory.INTERVENE] * INTERVENE_RISK_FACTOR
        analyze = scores_vs_count[RiskScoreCategory.ANALYZE] * ANALYZE_RISK_FACTOR
        monitor = scores_vs_count[RiskScoreCategory.MONITOR] * MONITOR_RISK_FACTOR

        sum_of_scores = intervene + analyze + monitor

        return min(100, sum_of_scores)

    async def update_risk_score_for_tree(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_graph: IssuesGraph
    ) -> None:
        bottom_iterator = issue_graph.iterator_from_bottom()
        try:
            next(bottom_iterator)  # skip all leaf
        except StopIteration:
            LOGGER.debug("No issues to analyze")
            return
        for issues in bottom_iterator:
            for issue_id in issues:
                await self._calculate_node_risk_score(account_id, source_id, issue_id)
                await self._flush_if_needed()
        await self._service_dal.session.commit()

    async def _flush_if_needed(self) -> None:
        self._flush_counter += 1
        if self._flush_counter % FLUSH_COUNTER == 0:
            await self._service_dal.session.flush()

    async def _calculate_node_risk_score(
        self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str
    ) -> None:
        LOGGER.info("Calculating risk score for %s", issue_id)
        try:
            case = await self._service_dal.cases_dal.get_case(
                account_id=account_id, source_id=source_id, issue_id=issue_id
            )
            scores = await self._get_scores(account_id, source_id, issue_id)
            scores_vs_count: dict[RiskScoreCategory, int] = defaultdict(lambda: 0)
            scores_vs_count.update(Counter(risk_score_to_category(score) for score in scores))
            node_risk_score = self._calculate_risk_score(scores_vs_count)
            if case.issue_analysis_id:
                issue_analysis = await self._service_dal.issues_analysis_dal.get_issue_analysis_by_id(
                    case.issue_analysis_id
                )
                issue_analysis.risk_score = node_risk_score
            else:
                await self._service_dal.add_classification_result_to_case(
                    account_id=account_id,
                    result=ClassificationJobLogicResult(
                        case_id=case.id,  # type: ignore[arg-type]
                        risk_score=node_risk_score,
                        classification=False,
                    ),
                )
        except Exception:
            LOGGER.exception("Failed to get case for %s", issue_id)
            return

    async def _get_scores(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str) -> list[int | None]:
        results = await get_container_children_for_score(self._service_dal, account_id, source_id, issue_id)
        return [child_info.risk_score for child_info in results]
