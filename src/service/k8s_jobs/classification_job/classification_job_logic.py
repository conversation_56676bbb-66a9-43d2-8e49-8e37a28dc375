from __future__ import annotations

import asyncio
import logging
from datetime import UTC, datetime

from packaging.version import Version
from prime_celery import Prime<PERSON>elery
from prime_file_manager_service_client import FileInfo, FileOriginRequest
from prime_gen_ai_service_client import AIPipelinesInput, CeleryHeaders
from prime_logger import PrimeAttributes, add_prime_attributes_context
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import AsyncRateLimit
from pydantic import StrictInt

from service.config import get_config
from service.db import ServiceDAL
from service.errors import AIPipelineRunError
from service.job_type import JobType
from service.logic.issues import BasePrimeIssue
from service.services_clients import ServicesClients, get_all_files_info

from ..base_job_logic import (
    GenAIBaseJob,
)
from ..base_job_logic.gen_ai_celery import (
    CELERY_APP_NAME_PREFIX,
    GENAI_PIPELINE_RUN_TASK_NAME,
    GENAI_PIPELINES_QUEUE,
    get_results_queue_name,
)
from .classification_issues_tracker import Classification<PERSON>ssuesT<PERSON>
from .classification_reporter import ClassificationMetricsReporter
from .classification_results_collector import accept_result
from .classification_watcher import TasksWatchdog
from .models import (
    CaseInfo,
    ClassificationJobLogicResult,
    ClassificationJobSummaryReport,
    GenAITaskMetaData,
    OldResultsHandlerReturn,
)
from .notify_scan_done import NotifyScanDone
from .update_risk_score_logic import UpdateRiskScoreLogic

LOGGER = logging.getLogger("classification-job")

TOTAL_RUNNING_TASKS_PROGRESS = 95  # percentage
WAIT_FOR_QUEUE = 60 * 60 * 24  # 24 hours
EX_REDIS_TASKS_KEY = 24 * 3600  # 24 hours


def get_celery_app(celery_app_name: str) -> PrimeCelery:
    celery_app = PrimeCelery(celery_app_name, get_config(), get_config().celery_redis)
    celery_app._app.conf.task_routes = {
        GENAI_PIPELINE_RUN_TASK_NAME: {"queue": GENAI_PIPELINES_QUEUE},
    }
    return celery_app


class ClassificationJobLogic(GenAIBaseJob):
    def __init__(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
        force: bool = False,
        last_run: datetime | None = None,
        parent_id: str | None = None,
    ) -> None:
        super().__init__(account_id, source_id, job_id, service_dal)
        self._force = force
        self._issue_id_to_case_id: dict[str, int] = {}
        self._parent_id = parent_id
        self._ai_version: Version = GenAIBaseJob.get_ai_version()
        self._last_run: datetime = last_run if last_run and force is False else datetime.min.replace(tzinfo=UTC)
        self._handle_old_results_output = OldResultsHandlerReturn(total_results=0, results_left=0)
        self._issues_to_process_total: int = 0
        self._issues_total: int = 0

        self._celery_app = get_celery_app(f"{CELERY_APP_NAME_PREFIX}-{job_id}")
        self._results_queue_name = get_results_queue_name(self._account_id, self._source_id)
        self._issues_tracker = ClassificationIssuesTracker(self._redis_client, source_id, job_id)
        self._watchdog = TasksWatchdog(
            progress_tracker=self._issues_tracker,
            progress_manager=self._progress_manager,
            source_id=self._source_id,
            job_id=self._job_id,
            redis_client=self._redis_client,
        )

    @property
    def task_name(self) -> str:
        return "Prime AI Jira Process"

    @property
    def job_type(self) -> str:
        return JobType.CLASSIFICATION.value

    async def _on_process_issue_error(self, issue_id: str, error: str) -> ClassificationJobLogicResult | None:
        await self._issues_tracker.issue_collected(issue_id)
        self._issues_tracker.add_processing_error(issue_id)
        self._errors[issue_id] = error
        return None

    async def is_source_id_exists(self) -> bool:
        try:
            source_id_int = StrictInt(self._source_id)
            source = await ServicesClients.source_api().get_source(account_id=self._account_id, source_id=source_id_int)
            return source is not None and source.id == source_id_int
        except Exception:
            LOGGER.exception("Failed to get source with id %s", self._source_id)
            return False

    def _get_gen_ai_celery_headers(self, issue_id: str, metadata: GenAITaskMetaData) -> CeleryHeaders:
        celery_header_fields = CeleryHeaders.model_fields.keys()
        metadata_dict = metadata.model_dump(exclude_none=True)
        additional_props = {k: v for k, v in metadata_dict.items() if k not in celery_header_fields}
        return self._get_celery_headers(issue_id, self._results_queue_name, additional_props)

    async def _run_ai_pipeline_for_issue(self, issue: BasePrimeIssue, metadata: GenAITaskMetaData) -> str:
        LOGGER.debug("Running AI pipeline for issue %s", issue.key)
        try:
            headers = self._get_gen_ai_celery_headers(issue.attributes.id_, metadata)
            ai_pipelines_input = AIPipelinesInput(
                context_issue=await self.get_context_issue_from_db(
                    issue, self._service_dal, self._account_id, self._source_id
                ),
                with_recommendations=False,
            )
            gen_ai_task_result = self._celery_app.send_task(
                GENAI_PIPELINE_RUN_TASK_NAME,
                kwargs={"task_input_dict": ai_pipelines_input.to_dict()},
                headers=headers.to_dict(),
                queue=GENAI_PIPELINES_QUEUE,
            )
            LOGGER.debug("taskid from gen ai for issue %s >> %s", issue.attributes.id_, gen_ai_task_result.id)
            return str(gen_ai_task_result.id)
        except Exception:
            LOGGER.exception("Failed to run AI pipelines for issue %s", issue.attributes.id_)
            raise AIPipelineRunError(
                account_id=self._account_id,
                source_id=self._source_id,
                issue_id=issue.attributes.id_,
                job_id=self.get_job_id(),
            ) from None

    async def _process_issue(self, case_info: CaseInfo) -> str | None:
        await self._progress_manager.increase_step_progress()
        issue_manager = await self._get_issue_manager()
        issue_graph = await self.get_issues_graph(self._parent_id)
        issue = await issue_manager.get_issue(case_info.issue_id, issue_graph)
        current_issue_hash = await asyncio.to_thread(self.get_issue_hash, issue)
        if case_info.issue_hash and case_info.issue_hash == current_issue_hash:
            LOGGER.info("Skipping issue %s as it has not changed", case_info.issue_id)
            await self._progress_manager.increase_step_progress()  # increase it again because we skipped the issue
            return None
        metadata = GenAITaskMetaData(
            account_id=self._account_id,
            source_id=self._source_id,
            case_id=case_info.case_id,
            job_id=self.get_job_id(),
            issue_id=issue.attributes.id_,
            issue_hash=current_issue_hash,
            results_queue_name=self._results_queue_name,
            ai_version=str(self._ai_version),
        )
        gen_ai_task_id = await self._run_ai_pipeline_for_issue(issue, metadata)
        await self._issues_tracker.add_issue_sent_to_ai(case_info.issue_id, gen_ai_task_id)
        return gen_ai_task_id

    async def _process_issue_wrapper(self, case_info: CaseInfo) -> str | None:
        with add_prime_attributes_context(PrimeAttributes(issue_id=case_info.issue_id)):
            LOGGER.info("Start processing issue %s", case_info.issue_id)
            try:
                genai_task_id = await self._process_issue(case_info)
            except Exception as ex:
                LOGGER.exception("Failed to process issue %s", case_info.issue_id)
                genai_task_id = None
                try:
                    await self._on_process_issue_error(case_info.issue_id, str(ex))
                except Exception:
                    LOGGER.exception("Failed to generate error result for issue %s", case_info.issue_id)
            LOGGER.info("Finished processing issue %s", case_info.issue_id)
            return genai_task_id

    async def process_issues_parallel(self, issues_to_process: dict[str, CaseInfo]) -> None:
        LOGGER.info("Processing %s issues", len(issues_to_process))
        tasks = [self._process_issue_wrapper(case_info) for case_info in issues_to_process.values()]
        gatherer = AsyncRateLimit(get_config().max_workers)
        for result_future in gatherer.as_completed(tasks):
            await result_future

    async def _get_issues_to_process(self) -> dict[str, CaseInfo]:
        def _should_process(_issue_info: FileInfo, _case_info: CaseInfo | None) -> bool:
            if _issue_info.timestamp is None:
                return False
            if _case_info is None:
                return False
            if self._force or GenAIBaseJob.version_changed(_case_info.ai_version, self._ai_version):
                _case_info.issue_hash = None
                return True
            return _issue_info.timestamp > max(self._last_run, _case_info.updated_at)

        LOGGER.info("Getting issues to process")
        rows = await self._service_dal.cases_dal.get_cases_for_classification(
            self._account_id, self._source_id, parent_id=self._parent_id
        )
        if self._parent_id:
            LOGGER.info("Classification for parent issue: %s", self._parent_id)
            file_origin_request = FileOriginRequest(file_names=[f"{case[0]}.json" for case in rows])
        else:
            file_origin_request = None
        existing_issues = await get_all_files_info(
            self._account_id, self._source_id, await self.document_type(), file_origin_request=file_origin_request
        )
        self._issues_total = len(existing_issues)
        LOGGER.info("Found %s issues in file manager ", self._issues_total)
        db_cases = {row[0]: CaseInfo(*row) for row in rows}
        ret_cases: dict[str, CaseInfo] = {}
        for issue_id, issue_info in existing_issues.items():
            case_info = db_cases.get(issue_id)
            if case_info is None:
                LOGGER.error("Case not found for issue %s", issue_id)
                continue
            if not _should_process(issue_info, case_info):
                continue
            ret_cases[issue_id] = case_info
        LOGGER.info("Found %s issues to process", len(ret_cases))
        return ret_cases

    async def _run(self) -> None:
        await self._progress_manager.increase_step_progress()
        LOGGER.info("Running task %s for account %s", self.task_name, self._account_id)

        if await self.is_source_id_exists() is False:
            raise ValueError(f"Source with id {self._source_id} not found")
        await self._progress_manager.increase_step_progress()

        self._handle_old_results_output = await self.handle_old_results()
        await self._issues_tracker.cleanup()

        issues_to_process = await self._get_issues_to_process()
        self._issues_to_process_total = len(issues_to_process)
        if len(issues_to_process) > 0:
            # for each task, 1 step for downloading and for step for classification
            factor = TOTAL_RUNNING_TASKS_PROGRESS / (len(issues_to_process) * 2)
            self._setup_progress_step(factor)
        self._issues_tracker.set_total_issues(len(issues_to_process))

        await self.process_issues_parallel(issues_to_process)
        with self._celery_app.workers(
            [self._results_queue_name],
            include=[accept_result.__module__],
            force_stop_on_exit=True,
            concurrency=get_config().max_workers,
        ):
            await self._watchdog.wait_for_results()

        await self.update_risk_scores_for_source()
        LOGGER.info("Execution completed. %s issues processed", len(issues_to_process))

    async def update_risk_scores_for_source(self) -> None:
        update_risk_score_logic = UpdateRiskScoreLogic(self._service_dal)
        await update_risk_score_logic.update_risk_score_for_tree(
            self._account_id, self._source_id, await self.get_issues_graph(self._parent_id)
        )

    async def handle_old_results(self) -> OldResultsHandlerReturn:
        if self._celery_app.is_queue_empty(self._results_queue_name):
            LOGGER.info("No old results found in queue")
            return OldResultsHandlerReturn(total_results=0, results_left=0)
        old_results_count = self._celery_app.get_queue_size(self._results_queue_name)
        LOGGER.info("Found %s existing results in queue. Waiting for them to be processed", old_results_count)
        with self._celery_app.workers([self._results_queue_name], include=[accept_result.__module__]):
            tasks_done = self._celery_app.wait_till_queue_empty(self._results_queue_name, timeout=WAIT_FOR_QUEUE)
            old_results_left = self._celery_app.get_queue_size(self._results_queue_name)
            if not tasks_done:
                LOGGER.error("Timeout reached. %s tasks are still in queue", old_results_left)
        return OldResultsHandlerReturn(total_results=old_results_count, results_left=old_results_left)

    async def _on_task_done(self) -> None:
        await super()._on_task_done()
        try:
            LOGGER.info("Sending notification")
            notifier = await NotifyScanDone.build(
                self._account_id, self._service_dal, self._start_time, self.get_job_id()
            )
            if notifier:
                await notifier.notify()
        except Exception:
            LOGGER.exception("Failed to send notification")

    async def report(self, execution_status: bool) -> None:
        reporter = ClassificationMetricsReporter(
            account_id=self._account_id, source_id=self._source_id, job_id=self.get_job_id()
        )
        summary_report = ClassificationJobSummaryReport(
            issues_total=self._issues_total,
            issues_need_processing=self._issues_to_process_total,
            issues_downloaded=self._issues_tracker.issues_downloaded,
            issues_sent_to_ai=self._issues_tracker.issues_sent_to_genai,
            old_issues_total=self._handle_old_results_output.total_results,
            old_issues_left=self._handle_old_results_output.results_left,
            error_job_collector=len(await self._issues_tracker.get_job_collector_errors()),
            error_classification=self._issues_tracker.processing_error,
            error_issues_not_handled=await self._issues_tracker.get_num_issues_pending(),
            error_gen_ai=len(await self._issues_tracker.get_gen_ai_errors()),
            duration_in_seconds=self.duration,
            execution_status=execution_status,
        )
        LOGGER.info("Reporting job metrics %s", summary_report)
        reporter.report_job_metrics(summary_report)
