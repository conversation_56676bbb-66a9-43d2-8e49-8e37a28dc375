import asyncio
import logging
import time

from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import SourceIdType
from prime_utils.progress_manager import ProgressManager

from .classification_issues_tracker import ClassificationIssuesTracker

LOGGER = logging.getLogger("classification-watchdog")

# Constants moved from original file
TIMEOUT_WAITING_TO_RESULT = 60 * 60 * 24  # 24 Hours
MAX_POD_TIME_SECONDS = 60 * 60 * 24  # 24 hours
TIME_SLEEP_BETWEEN_CELERY_CHECKS_SEC = 10


class TasksWatchdog:
    def __init__(
        self,
        progress_tracker: ClassificationIssuesTracker,
        progress_manager: ProgressManager,
        source_id: SourceIdType,
        job_id: int,
        redis_client: AsyncPrefixRedisClient,  # Type hint for redis client
    ) -> None:
        self._issues_tracker: ClassificationIssuesTracker = progress_tracker
        self._progress_manager: ProgressManager = progress_manager
        self._source_id = source_id
        self._job_id = job_id
        self._redis_client = redis_client

    async def _step_progress_by(self, total_finished_tasks: int) -> None:
        for _ in range(total_finished_tasks):
            await self._progress_manager.increase_step_progress()

    async def wait_for_results(self) -> None:
        LOGGER.info("Starting to wait for results")
        watchdog_time = 0
        start_time = time.time()
        last_tasks_count = await self._issues_tracker.get_num_issues_pending()
        LOGGER.info("Waiting for all genai tasks to finish: %d", last_tasks_count)
        issues_per_iteration = await self._issues_tracker.get_pending_issue_ids()
        while True:
            current_tasks_count = await self._issues_tracker.get_num_issues_pending()
            if current_tasks_count == 0:
                break
            diff = last_tasks_count - current_tasks_count
            LOGGER.debug("Waiting for %d tasks to finish (waiting=%s)", current_tasks_count, watchdog_time)
            if diff > 0:
                await self._step_progress_by(diff)
                current_issues = await self._issues_tracker.get_pending_issue_ids()
                handled_issues = set(issues_per_iteration) - set(current_issues)
                LOGGER.info("Handled issues: %s", str(handled_issues))
                watchdog_time = 0
                issues_per_iteration = current_issues
                last_tasks_count = current_tasks_count
            else:
                watchdog_time += TIME_SLEEP_BETWEEN_CELERY_CHECKS_SEC
            await asyncio.sleep(TIME_SLEEP_BETWEEN_CELERY_CHECKS_SEC)
            total_running_time = time.time() - start_time
            if total_running_time > MAX_POD_TIME_SECONDS or watchdog_time > TIMEOUT_WAITING_TO_RESULT:
                await self._log_errors(current_tasks_count)
                break

        if current_tasks_count > 0:
            LOGGER.warning("Not all tasks finished in time")
        else:
            LOGGER.info("All tasks finished")

    async def _gen_ai_errors(self) -> list[str]:
        error_issues: list[str] = []
        while issue := await self._redis_client.lpop(f"{self._source_id}:{self._job_id}:job-errors"):  # type: ignore[misc]
            error_issues.append(issue.decode())
        if error_issues:
            LOGGER.error("Found %s issues with gen-ai errors. Issues are: %s", len(error_issues), error_issues)
        return error_issues

    async def _job_collector_errors(self) -> list[str]:
        error_issues: list[str] = []
        while issue := await self._redis_client.lpop(f"{self._source_id}:{self._job_id}:job-collector-errors"):  # type: ignore[misc]
            error_issues.append(issue.decode())
        if error_issues:
            LOGGER.error("Found %s issues with job-collector-errors. Issues are: %s", len(error_issues), error_issues)
        return error_issues

    async def _log_errors(self, current_tasks_count: int) -> None:
        missed_issues = await self._issues_tracker.get_pending_issue_ids()
        LOGGER.error("Timeout reached. %d tasks are still running", current_tasks_count)
        LOGGER.warning("Missed issues: %s", missed_issues)
        genai_err = await self._issues_tracker.get_gen_ai_errors()
        LOGGER.warning("GenAI errors: %s", genai_err)
        collector_err = await self._issues_tracker.get_job_collector_errors()
        LOGGER.warning("Collector errors: %s", collector_err)
