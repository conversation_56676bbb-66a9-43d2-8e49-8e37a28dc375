import logging

from service.db import get_service_dal_context
from service.k8s_jobs.classification_job.classification_job_logic import ClassificationJobLogic
from service.k8s_jobs.classification_job.models import ClassificationJobArgs
from service.k8s_jobs.run_job_main import run_main

LOGGER = logging.getLogger("classification-job")


async def main() -> None:
    job_vars = ClassificationJobArgs()
    LOGGER.info("Running Classification Job with args: %s", job_vars)
    async with get_service_dal_context() as service_dal:
        job_logic = ClassificationJobLogic(
            account_id=job_vars.account_id,
            source_id=job_vars.source_id,
            job_id=job_vars.job_id,
            service_dal=service_dal,
            force=job_vars.force,
            last_run=job_vars.last_run,
            parent_id=job_vars.parent_id,
        )
        await job_logic.start()
    LOGGER.info("Finished Classification Job %s", job_vars.job_id)


if __name__ == "__main__":
    run_main(main)
