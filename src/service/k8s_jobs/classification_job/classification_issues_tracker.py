from __future__ import annotations

import logging
from collections.abc import Awaitable, Callable
from functools import wraps
from typing import Any, TypeVar, cast

from prime_redis_utils import AsyncPrefixRedisClient
from prime_shared.common_types import SourceIdType

from .task_helpers import MAX_POD_TIME_SECONDS

T = TypeVar("T")

LOGGER = logging.getLogger(__name__)

BASE_DATA = {"_initialized": "1"}


def ensure_initialized(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
    @wraps(func)
    async def wrapper(self: ClassificationIssuesTracker, *args: Any, **kwargs: Any) -> T:
        initialized = await self._redis_client.hexists(self._tracker_key, "_initialized")  # type: ignore[misc]
        if not initialized:
            await self._setup()
        return await func(self, *args, **kwargs)

    return wrapper


class ClassificationIssuesTracker:
    def __init__(self, redis_client: AsyncPrefixRedisClient, source_id: SourceIdType, job_id: int):
        self._redis_client = redis_client
        self._source = source_id
        self._job_id = job_id

        self._tracker_key = f"{source_id}:collector:tracker"
        self._genai_err_key = f"{source_id}:collector:job-errors"
        self._job_collector_err_key = f"{source_id}:collector:job-collector-errors"
        self._total_issues: int = 0
        self._issues_sent_to_genai: int = 0
        self._processing_error: int = 0

    async def cleanup(self) -> None:
        await self._redis_client.delete(self._tracker_key)
        await self._redis_client.delete(self._genai_err_key)
        await self._redis_client.delete(self._job_collector_err_key)
        self._issues_sent_to_genai = 0
        self._processing_error = 0
        self._total_issues = 0

        await self._setup()

    async def _setup(self) -> None:
        # Initialize the hash with a sentinel value
        await self._redis_client.hset(self._tracker_key, mapping=BASE_DATA)  # type: ignore[misc]
        await self._redis_client.expire(self._tracker_key, MAX_POD_TIME_SECONDS)

        await self._redis_client.expire(self._genai_err_key, MAX_POD_TIME_SECONDS)
        await self._redis_client.expire(self._job_collector_err_key, MAX_POD_TIME_SECONDS)

    async def issue_collected(self, issue_id: str) -> None:
        if await self._redis_client.hexists(self._tracker_key, issue_id):  # type: ignore[misc]
            await self._redis_client.hdel(self._tracker_key, issue_id)  # type: ignore[misc]

    @ensure_initialized
    async def get_num_issues_pending(self) -> int:
        num = await self._redis_client.hlen(self._tracker_key)  # type: ignore[misc]
        # Subtract 1 to account for the sentinel value if it exists
        base_data_keys_list = list(BASE_DATA.keys())
        for key in base_data_keys_list:
            if await self._redis_client.hexists(self._tracker_key, key):  # type: ignore[misc]
                num = max(0, num - 1)
        return cast(int, num)  # Redis HLEN always returns an integer

    @ensure_initialized
    async def get_pending_issue_ids(self) -> list[str]:
        items = await self._redis_client.hkeys(self._tracker_key)  # type: ignore[misc]
        # Filter out the sentinel value
        return [item.decode("utf-8") for item in items if item.decode("utf-8") not in BASE_DATA]

    # JOB COLLECTOR ERRORS
    async def add_job_collector_error(self, issue_id: str) -> None:
        await self._redis_client.lpush(self._job_collector_err_key, issue_id)  # type: ignore[misc]

    async def get_job_collector_errors(self) -> list[str]:
        return await self._collect_errors(
            self._job_collector_err_key, "Found %s issues with job-collector-errors. Issues are: %s"
        )

    # GENAI ERRORS
    async def add_gen_ai_error(self, issue_id: str) -> None:
        await self._redis_client.lpush(self._genai_err_key, issue_id)  # type: ignore[misc]

    async def get_gen_ai_errors(self) -> list[str]:
        return await self._collect_errors(self._genai_err_key, "Found %s issues with gen-ai errors. Issues are: %s")

    # PROCESSING ERRORS
    def add_processing_error(self, issue_id: str) -> None:
        self._processing_error += 1

    @property
    def processing_error(self) -> int:
        return self._processing_error

    # ISSUES SENT TO GENAI
    @ensure_initialized
    async def add_issue_sent_to_ai(self, issue_id: str, val: str) -> None:
        await self._redis_client.hset(self._tracker_key, issue_id, val)  # type: ignore[misc]
        self._issues_sent_to_genai += 1

    @property
    def issues_sent_to_genai(self) -> int:
        return self._issues_sent_to_genai

    # TOTAL ISSUES
    def set_total_issues(self, total_issues: int) -> None:
        self._total_issues = total_issues

    @property
    def issues_downloaded(self) -> int:
        return self._total_issues

    async def _collect_errors(self, redis_key: str, log_message: str) -> list[str]:
        error_issues: list[str] = []
        while issue := await self._redis_client.lpop(redis_key):  # type: ignore[misc]
            error_issues.append(issue.decode())
        if error_issues:
            LOGGER.error(log_message, len(error_issues), error_issues)
        return error_issues
