from __future__ import annotations

import asyncio
import logging
from asyncio import create_task
from datetime import UTC, datetime, timedelta
from typing import Optional, cast

from prime_config_service_client import NotificationsConfig
from prime_notification_service_client import (
    MailNotifyReq,
    OutputNotificationType,
    ScanCompletedMailNotificationRequest,
    ScanCompletedSlackNotificationRequest,
    SlackNotifyReq,
)
from prime_shared.common_types import AccountIdType
from pydantic import BaseModel

from service.db import ServiceDAL
from service.job_type import JobType
from service.logic.filters_and_sort import PSVFilters
from service.logic.statistics import CasesByRiskCategory
from service.models import PsvStatus

# from service.logic.statistics import CountCaseByRiskCategory
from service.models.filters_and_sort import Filter, Operator
from service.services_clients import ServicesClients

LOGGER = logging.getLogger(__name__)


class NotifyScanDone(BaseModel):
    new_interventions: int
    new_cases: int
    intervene_amount: int
    analyze_amount: int
    monitor_amount: int
    total_sum: int
    account_id: AccountIdType
    user: str
    new_open_psv_amount: int
    total_open_psv_amount: int
    mail_recipients: Optional[list[str]] = None
    slack_channel_ids: Optional[list[str]] = None

    @staticmethod
    def _get_mail_recipients(notification_settings: NotificationsConfig) -> list[str] | None:
        return (
            notification_settings.scan_completed_notifications_settings.mail_recipients
            if notification_settings.scan_completed_notifications_settings
            and notification_settings.scan_completed_notifications_settings.mail_enabled
            else None
        )

    @staticmethod
    def _get_slack_channel_ids(notification_settings: NotificationsConfig) -> list[str] | None:
        return (
            notification_settings.scan_completed_notifications_settings.slack_channel_ids
            if notification_settings.scan_completed_notifications_settings
            and notification_settings.scan_completed_notifications_settings.slack_enabled
            else None
        )

    @classmethod
    async def _get_start_time(
        cls, service_dal: ServiceDAL, account_id: AccountIdType, current_job_start: datetime
    ) -> datetime:
        update_issue_job = await service_dal.scheduler_dal.jobs_dal.get_latest_completed_job(
            account_id,
            job_type=JobType.UPDATE_ISSUES,
            job_args={"update_fields_only": "false"},
        )
        if not update_issue_job:
            return current_job_start
        update_issue_job_created_at = cast(datetime, update_issue_job.created_at)
        if update_issue_job_created_at > current_job_start:
            return current_job_start - timedelta(days=1)
        return max(update_issue_job_created_at, current_job_start - timedelta(days=1))

    @classmethod
    async def build(
        cls, account_id: AccountIdType, service_dal: ServiceDAL, start: datetime, job_id: int
    ) -> NotifyScanDone | None:
        notification_settings = await ServicesClients.config_notification_api().notifications_settings(
            account_id=account_id
        )
        mail_recipients = cls._get_mail_recipients(notification_settings)
        slack_channel_ids = cls._get_slack_channel_ids(notification_settings)
        if mail_recipients is None and slack_channel_ids is None:
            LOGGER.info("Scan completed notifications are disabled, skipping")
            return None
        now = datetime.now(UTC)
        total_cases = await CasesByRiskCategory(service_dal, account_id, now, now).get_stats()
        start = await cls._get_start_time(service_dal, account_id, start)
        new_cases = await CasesByRiskCategory(service_dal, account_id, start, now).get_stats()

        open_psv_filters = (
            PSVFilters()
            .has_psv()
            .add_filters(
                [
                    Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN),
                ]
            )
        )
        new_open_psv_filters = (
            PSVFilters()
            .has_psv()
            .add_filters(
                [
                    Filter(field="created_at", op=Operator.GTE, value=[str(start)]),
                    Filter(field="status", op=Operator.EQ, value=PsvStatus.OPEN),
                ]
            )
        )
        open_psv_count = await service_dal.psv_dal.get_psv_count(account_id=account_id, psv_filters=open_psv_filters)
        new_open_psv_count = await service_dal.psv_dal.get_psv_count(
            account_id=account_id, psv_filters=new_open_psv_filters
        )

        job = await service_dal.scheduler_dal.jobs_dal.get_job_by_id(account_id=account_id, job_id=job_id)
        return cls(
            new_interventions=new_cases.intervene[-1].y - new_cases.intervene[-2].y,
            new_cases=new_cases.intervene[-1].y
            + new_cases.analyze[-1].y
            + new_cases.monitor[-1].y
            - new_cases.intervene[-2].y
            - new_cases.analyze[-2].y
            - new_cases.monitor[-2].y,
            intervene_amount=total_cases.intervene[-1].y,
            analyze_amount=total_cases.analyze[-1].y,
            monitor_amount=total_cases.monitor[-1].y,
            total_sum=total_cases.intervene[-1].y + total_cases.analyze[-1].y + total_cases.monitor[-1].y,
            account_id=account_id,
            user=job.created_by,
            new_open_psv_amount=new_open_psv_count,
            total_open_psv_amount=open_psv_count,
            mail_recipients=mail_recipients,
            slack_channel_ids=slack_channel_ids,
        )

    async def _send_slack_notification(self) -> None:
        if self.slack_channel_ids is None:
            LOGGER.info("Scan completed slack notification is disabled, skipping")
            return None
        req = ScanCompletedSlackNotificationRequest(
            user=self.user,
            slack_channel_ids=self.slack_channel_ids,
            new_interventions=self.new_interventions,
            new_cases=self.new_cases,
            intervene_amount=self.intervene_amount,
            analyze_amount=self.analyze_amount,
            monitor_amount=self.monitor_amount,
            total_sum=self.total_sum,
            new_open_psv_amount=self.new_open_psv_amount,
            total_open_psv_amount=self.total_open_psv_amount,
            notification_type=OutputNotificationType.SCAN_COMPLETED.value,
        )
        LOGGER.info("Sending slack notification %s", req)
        await ServicesClients.notification_api().notify_slack(
            account_id=self.account_id, slack_notify_req=SlackNotifyReq(actual_instance=req)
        )

    async def _send_mail_notification(self) -> None:
        if self.mail_recipients is None:
            LOGGER.info("Scan completed mail notification is disabled, skipping")
            return None
        req = ScanCompletedMailNotificationRequest(
            user=self.user,
            mail_recipients=self.mail_recipients,
            new_interventions=self.new_interventions,
            new_cases=self.new_cases,
            intervene_amount=self.intervene_amount,
            analyze_amount=self.analyze_amount,
            monitor_amount=self.monitor_amount,
            total_sum=self.total_sum,
            new_open_psv_amount=self.new_open_psv_amount,
            total_open_psv_amount=self.total_open_psv_amount,
            notification_type=OutputNotificationType.SCAN_COMPLETED.value,
        )
        LOGGER.info("Sending mail notification %s", req)
        await ServicesClients.notification_api().notify_mail(
            account_id=self.account_id, mail_notify_req=MailNotifyReq(actual_instance=req)
        )

    async def notify(self) -> None:
        tasks = [
            create_task(self._send_mail_notification(), name="send_mail_notification"),
            create_task(self._send_slack_notification(), name="send_mail_notification"),
        ]

        async for task in asyncio.as_completed(tasks):
            try:
                await task
            except Exception:
                LOGGER.exception(
                    "Failed to send notification %s",
                    task.get_name(),  # type: ignore[attr-defined]
                )
