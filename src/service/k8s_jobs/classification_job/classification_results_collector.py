import asyncio
import logging
import threading
from typing import Any, cast

from celery import Celery, shared_task
from celery import Task as CeleryBaseTask
from celery.exceptions import BackendError
from celery.signals import setup_logging
from prime_gen_ai_service_client import AIPipelinesOutput
from prime_logger import PrimeAttributes, add_prime_attributes_context, init_root_logger
from prime_redis_utils import get_redis_for_account
from prime_shared.common_types import AccountIdType, SourceIdType

from service.config import get_config
from service.db import get_service_dal_context
from service.errors.case_errors import ClassificationGenerationError
from service.models import IssueLinks, IssueLinkType
from service.services_clients import ServicesClients

from ..base_job_logic.gen_ai_celery import RESULTS_TASK_NAME
from .classification_issues_tracker import ClassificationIssuesTracker
from .classification_reporter import ClassificationMetricsReporter
from .models import ClassificationJobLogicResult, GenAITaskMetaData
from .task_helpers import ai_output_to_classification_task_results

LOGGER = logging.getLogger("classification-job-collector")


@setup_logging.connect  # type: ignore[misc]
def setup_prime_logging(**kwargs: Any) -> None:
    init_root_logger(config=get_config())


def get_current_event_loop() -> asyncio.AbstractEventLoop | None:
    try:
        return asyncio.get_running_loop()
    except RuntimeError:
        return None


def _create_event_loop() -> asyncio.AbstractEventLoop:
    if loop := get_current_event_loop():
        return loop
    thread_id = threading.current_thread().ident
    loop = asyncio.get_event_loop_policy().new_event_loop()
    LOGGER.info("Creating new event loop %s on thread %s", id(loop), thread_id)
    if not loop.is_running():
        LOGGER.info("Starting event loop %s on thread %s", id(loop), thread_id)
        thread_name = f"Thread {thread_id} runner with loop {id(loop)}"
        threading.Thread(target=loop.run_forever, name=thread_name, daemon=True).start()
        asyncio.set_event_loop(loop)
        while not loop.is_running():
            pass
    return loop


def ensure_event_loop(loop: asyncio.AbstractEventLoop | None) -> asyncio.AbstractEventLoop:
    if loop is None or not loop.is_running():
        loop = _create_event_loop()
    return loop


class CollectorTask(CeleryBaseTask):  # type: ignore[misc]
    def __init__(self) -> None:
        self._loop: asyncio.AbstractEventLoop | None = None
        self._lock = threading.Lock()
        LOGGER.info("CollectorTask initialized")

    @property
    def celery_client(self) -> Celery:
        return self._app

    @property
    def loop(self) -> asyncio.AbstractEventLoop:
        with self._lock:
            self._loop = ensure_event_loop(self._loop)
        return self._loop


@shared_task(name=RESULTS_TASK_NAME, bind=True, base=CollectorTask)  # type: ignore[misc]
def accept_result(self: CollectorTask, results: dict[str, Any]) -> None:
    headers = self.request.headers
    metadata = GenAITaskMetaData.model_validate(headers)
    LOGGER.debug("Celery accept issue %s", metadata.issue_id)
    attributes = PrimeAttributes(
        account_id=metadata.account_id,
        source_id=metadata.source_id,
        job_id=metadata.job_id,
        issue_id=metadata.issue_id,
    )
    with add_prime_attributes_context(attributes):
        LOGGER.info("Running process result for issue %s", metadata.issue_id)
        asyncio.run_coroutine_threadsafe(_process_result(metadata, results), self.loop).result()


async def get_issue_links_by_id(issue_id: str, account_id: AccountIdType, source_id: SourceIdType) -> list[IssueLinks]:
    descendants_links = await ServicesClients.relationship_api().get_descendants_links(
        account_id, source_id, f"{issue_id}.json"
    )
    results = [
        IssueLinks(url=child_link.link, link_type=IssueLinkType.document_type_to_link_type(child_link.link_type))
        for child_link in descendants_links
    ]
    if results:
        LOGGER.info("Got %s links for issue %s", len(results), issue_id)
    return results


async def _process_result(metadata: GenAITaskMetaData, results: dict[str, Any]) -> None:
    LOGGER.info("Processing results for issue %s", metadata.issue_id)
    redis_client = get_redis_for_account(metadata.account_id, get_config(), get_config().namespace)
    issue_tracker = ClassificationIssuesTracker(redis_client, metadata.source_id, metadata.job_id)

    reporter = ClassificationMetricsReporter(
        account_id=metadata.account_id, source_id=metadata.source_id, job_id=metadata.job_id
    )

    try:
        if "error" in results:
            error = results["error"]
            LOGGER.error("Received Error from AI pipelines. issue id %s, error: %s", metadata.issue_id, error)
            reporter.increment_errors()
            await issue_tracker.add_gen_ai_error(metadata.issue_id)
        else:
            output = AIPipelinesOutput.model_validate(results)
            issue_links = await get_issue_links_by_id(metadata.issue_id, metadata.account_id, metadata.source_id)
            result = ai_output_to_classification_task_results(
                output, cast(int, metadata.case_id), metadata.issue_hash, metadata.ai_version, issue_links
            )
            await _save_result_to_db(result, metadata)
            reporter.increment_analyzed_items()
    except Exception as e:
        reporter.increment_errors()
        await issue_tracker.add_job_collector_error(metadata.issue_id)
        LOGGER.exception("Error processing results for issue %s", metadata.issue_id)
        msg = ClassificationGenerationError(
            account_id=metadata.account_id, source_id=metadata.source_id, issue_id=metadata.issue_id, error=str(e)
        ).get_full_msg()
        raise BackendError(msg) from None
    finally:
        LOGGER.info("Result process finished for issue %s and case_id %s", metadata.issue_id, metadata.case_id)
        await issue_tracker.issue_collected(metadata.issue_id)
        # await redis_client.aclose()


async def _save_result_to_db(result: ClassificationJobLogicResult, metadata: GenAITaskMetaData) -> None:
    async with get_service_dal_context() as service_dal:
        if not result.case_id:
            raise ValueError("Case id is required to add issue analysis")
        LOGGER.debug("Adding classification result to case %s", result.case_id)
        await service_dal.add_classification_result_to_case(metadata.account_id, result)
        await service_dal.update_case_summary(metadata.account_id, metadata.source_id, metadata.issue_id, result)
