from __future__ import annotations

import json
from datetime import datetime
from enum import StrEnum, auto
from typing import Named<PERSON><PERSON><PERSON>, Self

from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import BaseModel, ConfigDict, Field, computed_field, model_validator
from pydantic_settings import BaseSettings

from service.models import (
    BaseStepResults,
    Implementation,
    IssueAnalysisConcern,
    SecurityControl,
)
from service.models.issue_analysis import IssueLinks

from ..base_job_logic import SummaryOutput


def set_default_updated_at(v: datetime | None) -> datetime:
    return v if v is not None else datetime.min


class CaseInfo:
    def __init__(
        self, issue_id: str, case_id: int, updated_at: datetime | None, issue_hash: str | None, ai_version: str | None
    ) -> None:
        self.issue_id = issue_id
        self.case_id = case_id
        self.updated_at = updated_at or datetime.min
        self.issue_hash = issue_hash
        self.ai_version = ai_version


class GenAITaskMetaData(BaseModel):
    model_config = ConfigDict(extra="allow")

    account_id: AccountIdType
    source_id: SourceIdType
    case_id: int | None
    issue_id: str
    job_id: int
    issue_hash: str
    results_queue_name: str
    ai_version: str


class ClassificationJobArgs(BaseSettings):
    account_id: AccountIdType
    source_id: SourceIdType
    job_id: int
    force: bool
    last_run: datetime | None = None
    datadog_enabled: bool = False
    parent_id: str | None = None


class ClassificationJobLogicResult(BaseStepResults):
    case_id: int
    classification: bool
    keywords: list[str] = Field(default_factory=list)
    issue_hash: str | None = None
    concerns: list[IssueAnalysisConcern] | None = None
    short_assessment: str | None = None
    long_assessment: str | None = None
    risk_score: int | None = None
    confidence: int | None = None
    controls: list[SecurityControl] | None = None
    recommendations: list[Implementation] | None = None
    research_package_version: str | None = None
    confidentiality_score: int | None = None
    integrity_score: int | None = None
    availability_score: int | None = None
    is_automated: bool | None = None
    is_security_enhancement: bool | None = None
    fire_summary: str | None = None
    issue_links: list[IssueLinks] = Field(default_factory=list)
    summary: SummaryOutput | None = None

    @model_validator(mode="after")
    def validate_required_fields_when_final_answer_is_true(self: Self) -> Self:  # noqa: N804
        if self.classification:
            required_fields = [
                "risk_score",
                "confidence",
                "concerns",
                "recommendations",
                "integrity_score",
                "availability_score",
                "controls",
            ]
            missing_fields = [field for field in required_fields if getattr(self, field) is None]
            if missing_fields:
                raise ValueError(f"Missing required fields when final_answer is True: {', '.join(missing_fields)}")
        return self


class ClassificationJobSummaryState(StrEnum):
    TERMINATED = auto()
    COMPLETED = auto()
    FAILED = auto()
    JOB_TIMEOUT = auto()
    COMPLETED_WITH_ERRORS = auto()


class ClassificationJobSummaryReport(BaseModel):
    issues_total: int
    issues_need_processing: int
    issues_downloaded: int
    issues_sent_to_ai: int
    error_issues_not_handled: int

    old_issues_total: int
    old_issues_left: int

    execution_status: bool
    duration_in_seconds: int = 0

    error_gen_ai: int
    error_job_collector: int
    error_classification: int

    def as_str_report(self) -> str:
        return json.dumps({key.replace("_", " ").title() for key, value in self.model_dump().items()})

    @computed_field  # type: ignore[prop-decorator]
    @property
    def success_rate(self) -> float:
        total_errors = self.errors_total
        if self.issues_sent_to_ai == 0:
            return 100
        return (1 - (total_errors / self.issues_sent_to_ai)) * 100

    @computed_field  # type: ignore[prop-decorator]
    @property
    def job_state(self) -> ClassificationJobSummaryState:
        if self.execution_status is False:
            return ClassificationJobSummaryState.TERMINATED
        if self.error_issues_not_handled > 0:
            return ClassificationJobSummaryState.JOB_TIMEOUT
        if self.error_gen_ai > 0 or self.error_issues_not_handled > 0 or self.error_job_collector > 0:
            return ClassificationJobSummaryState.COMPLETED_WITH_ERRORS
        return ClassificationJobSummaryState.COMPLETED

    @computed_field  # type: ignore[prop-decorator]
    @property
    def errors_total(self) -> int:
        return self.error_gen_ai + self.error_job_collector + self.error_classification + self.error_issues_not_handled


class OldResultsHandlerReturn(NamedTuple):
    total_results: int
    results_left: int


class IssuesToProcessReturn(NamedTuple):
    cases: dict[str, CaseInfo]
    total_files: int
