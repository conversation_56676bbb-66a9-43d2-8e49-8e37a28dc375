apiVersion: batch/v1
kind: Job
metadata:
  generateName: "{{ .Values.job_info.name }}-"
  labels:
    app: primesec
    job: "{{ .Values.job_info.job_type }}"
    job_id: "{{ .Values.job_info.id }}"
    account-id: "{{ .Values.job_env.account_id }}"
    service-name: "{{ .Values.service.name }}"
    {{- if .Values.job_env.source_id }}
    source-id: "{{ .Values.job_env.source_id }}"
    {{- end }}
    {{- if .Values.datadog_enabled }}
    tags.datadoghq.com/service: "{{ .Values.service.name }}"
    admission.datadoghq.com/enabled: "true"
    {{- end }}
  annotations:
    "helm.sh/hook": post-install
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded,hook-failed
    eks.amazonaws.com/skip-containers: "datadog-lib-python-init"
spec:
  ttlSecondsAfterFinished: 0
  backoffLimit: 0
  template:
    metadata:
      labels:
        app: primesec
        job: "{{ .Values.job_info.job_type }}"
        job_id: "{{ .Values.job_info.id }}"
        account-id: "{{ .Values.job_env.account_id }}"
        {{- if .Values.job_env.source_id }}
        source-id: "{{ .Values.job_env.source_id }}"
        {{- end }}
        service-name: "{{ .Values.service.name }}"
        {{- if .Values.datadog_enabled }}
        tags.datadoghq.com/service: "{{ .Values.service.name }}"
        admission.datadoghq.com/enabled: "true"
        {{- end }}
      annotations:
        {{ if .Values.datadog_enabled }}
        admission.datadoghq.com/python-lib.version: v3.2.1
        {{- end }}
    spec:
      containers:
        - name: "{{ .Values.job_info.name }}"
          image: "{{ .Values.job_info.image }}"
          imagePullPolicy: "Always"
          command: [ "python" ]
          args: [ "-m", "{{ .Values.job_info.module_path }}" ]
          env:
            - name: ACCOUNT_ID
              value: "{{ .Values.job_env.account_id }}"
            {{- if .Values.job_env.source_id }}
            - name: SOURCE_ID
              value: "{{ .Values.job_env.source_id }}"
            {{- end }}
            - name: JOB_ID
              value: "{{ .Values.job_info.id }}"
            {{- range $key, $value := .Values.job_env.additional_env }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
          envFrom:
            - configMapRef:
                name: "{{ .Values.service.name }}-common-env-vars"
            - secretRef:
                name: "{{ .Values.service.name }}-common-secrets"
            - configMapRef:
                name: "{{ .Values.service.name }}-env-vars"
            - secretRef:
                name: "{{ .Values.service.name }}-secrets"
      restartPolicy: "Never" 