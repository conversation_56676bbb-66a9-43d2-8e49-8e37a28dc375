from __future__ import annotations

import logging
import random
from collections import Counter, defaultdict
from datetime import UTC, datetime

import orjson
from prime_file_manager_service_client import DocumentType, FileUploadMetadataRequest
from prime_redis_utils import get_redis_for_account
from prime_shared.common_types import AccountIdType, SourceIdType
from prime_utils import AsyncRateLimit

from service.config import get_config
from service.db import ServiceDAL
from service.job_type import JobType
from service.logic.jira_manager import <PERSON><PERSON><PERSON><PERSON>In<PERSON>, <PERSON><PERSON><PERSON><PERSON>sM<PERSON><PERSON>, JiraIssuesManager
from service.models import ProviderFieldData, ProviderFieldType
from service.services_clients import ServicesClients, get_all_files_info

from ..base_job_logic import BaseJobLogic, chunk_generator

LOGGER = logging.getLogger("build_fields_data")
TOTAL_RUNNING_TASKS_PROGRESS = 90  # percentage
MOST_COMMON = 10
BULK_SIZE = 100

supported_field_types = str | int | bool
fields_data_t = dict[str, Counter[supported_field_types]]
issues_provider_fields_t = dict[str, list[ProviderFieldData]]


class BuildFieldsDataJobLogic(BaseJobLogic):
    async def report(self, success: bool) -> None:
        pass

    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
        issue_manager: JiraIssuesManager,
        jira_fields: list[JiraFieldInfo],
    ) -> None:
        super().__init__(account_id, source_id, job_id, service_dal)
        self._jira_issues_manager = issue_manager
        self._jira_fields = jira_fields

    async def termination_signal_handler(self) -> None:
        LOGGER.info("Termination signal received")

    @classmethod
    async def build(
        cls,
        account_id: AccountIdType,
        source_id: SourceIdType,
        job_id: int,
        service_dal: ServiceDAL,
    ) -> BuildFieldsDataJobLogic:
        redis_client = get_redis_for_account(account_id, get_config(), get_config().namespace)
        issue_manager = JiraIssuesManager(account_id, source_id)
        jira_fields = await JiraFieldsManager.get_fields(account_id, source_id, redis_client)
        result = cls(
            account_id=account_id,
            source_id=source_id,
            job_id=job_id,
            service_dal=service_dal,
            issue_manager=issue_manager,
            jira_fields=jira_fields,
        )
        return result

    @property
    def job_type(self) -> str:
        return JobType.BUILD_FIELDS_DATA.value

    async def _upload_fields_data(self, source_id: int, fields_data: fields_data_t) -> None:
        LOGGER.info("Uploading fields data for account %s", self._account_id)
        domain = await ServicesClients.source_api().get_domain_from_source(self._account_id, source_id=source_id)
        metadata = FileUploadMetadataRequest(
            origin_id="fields_data.json",
            source_id=source_id,
            document_type=DocumentType.DOCUMENT,
            downloadable_link=f"fields_data_json_{self._account_id}_{source_id}",
            domain=domain,
            timestamp=datetime.now(UTC),
        )
        fields = {key: [str(x[0]) for x in counter.most_common(MOST_COMMON)] for key, counter in fields_data.items()}
        upload = metadata.model_dump_json(exclude_none=True).encode(), orjson.dumps(fields)
        try:
            await ServicesClients.files_api().upload_files(self._account_id, upload)  # type: ignore[arg-type]
        except Exception:
            LOGGER.exception("Failed to upload fields data for account %s", self._account_id)

    async def _run(self) -> None:
        LOGGER.info("Running provider fields retriever for account %s, source %s", self._account_id, self._source_id)
        issues_ids = await self._get_issues_to_process()
        await self._progress_manager.update_progress(5)
        self._setup_progress_step(TOTAL_RUNNING_TASKS_PROGRESS / (len(issues_ids) / BULK_SIZE + 10))
        fields_data = await self._get_fields_data(issues_ids)
        await self._upload_fields_data(self._source_id, fields_data)
        LOGGER.info(
            "Finished provider fields retriever for account %s and source %s", self._account_id, self._source_id
        )

    async def _get_fields_data(self, issues_ids: list[str]) -> fields_data_t:
        fields_data: fields_data_t = defaultdict(Counter)
        tasks = [self._get_provider_fields(chunk_issues) for chunk_issues in chunk_generator(issues_ids, BULK_SIZE)]
        for result in AsyncRateLimit(get_config().max_workers).as_completed(tasks):
            chunk_issue_fields: issues_provider_fields_t = await result
            self._handle_issues_fields(chunk_issue_fields, fields_data)
        return fields_data

    async def _get_provider_fields(self, issue_ids: list[str]) -> issues_provider_fields_t:
        LOGGER.info("Fetching provider fields for %s issues", len(issue_ids))
        issues = await self._jira_issues_manager.get_issues(issue_ids)
        results = {}
        for issue_id, issue in issues.items():
            try:
                results[issue_id] = [issue.get_field(field_info) for field_info in self._jira_fields]
            except Exception as ex:
                LOGGER.exception("Error fetching provider fields for issue %s", issue_id)
                self._errors[issue_id] = str(ex)
        await self._increase_step_progress()
        return results

    async def _get_issues_to_process(self) -> list[str]:
        all_files = list((await get_all_files_info(self._account_id, self._source_id, DocumentType.JIRA)).values())
        sample_size = get_config().build_fields_data_sample_size
        if sample_size:
            all_files = random.sample(all_files, k=min(len(all_files), sample_size))
        return [jira_file.origin_id.replace(".json", "") for jira_file in all_files]

    @classmethod
    def _handle_issues_fields(cls, chunk_issue_fields: issues_provider_fields_t, fields_data: fields_data_t) -> None:
        for issue_fields in chunk_issue_fields.values():
            for field in issue_fields:
                if field.type in [ProviderFieldType.STRING, ProviderFieldType.DATE]:
                    continue
                values = field.value if isinstance(field.value, list) else [field.value]
                for field_value in values:
                    if field_value in (None, "") or not isinstance(field_value, supported_field_types):
                        continue
                    fields_data[field.id][field_value] += 1
