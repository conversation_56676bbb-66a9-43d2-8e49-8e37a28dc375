import logging

from service.db import get_service_dal_context
from service.k8s_jobs.run_job_main import run_main

from .build_fields_data import BuildFieldsDataJobLogic
from .models import BuildFieldsDataJobArgs

LOGGER = logging.getLogger("update-provider-fields-job")


async def main() -> None:
    job_vars = BuildFieldsDataJobArgs()
    LOGGER.info("Running Build Provider Fields Metadata Job with args %s", job_vars)
    async with get_service_dal_context() as service_dal:
        job_logic = await BuildFieldsDataJobLogic.build(
            account_id=job_vars.account_id,
            source_id=job_vars.source_id,
            job_id=job_vars.job_id,
            service_dal=service_dal,
        )
        await job_logic.start()


if __name__ == "__main__":
    run_main(main)
