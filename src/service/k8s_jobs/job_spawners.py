import logging
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any

from prime_jobs import (
    <PERSON><PERSON><PERSON><PERSON>,
    JobCancelReason,
    JobConfig,
    JobInfo,
    JobNameType,
    JobSpawnerFactory,
    JobSpawnerHelmDeployer,
    ServiceInfo,
    scheduler_dal_ctx,
)
from prime_shared.common_types import AccountIdType, SourceIdType
from pydantic import BaseModel, Field

from service.config import get_config
from service.job_type import JobType
from service.k8s_jobs.security_violation_job.models import PsvJobArgs

from .build_fields_data_job.models import BuildFieldsDataJobArgs
from .classification_job.models import ClassificationJobArgs
from .summary_job.models import SummaryJobArgs
from .update_issues_job.models import UpdateIssuesJobArgs

JOB_NAME_MAX_LENGTH = 53

LOGGER = logging.getLogger(__name__)

job_info_types = UpdateIssuesJobArgs | ClassificationJobArgs | BuildFieldsDataJobArgs | SummaryJobArgs | PsvJobArgs


class RatJobEnv(BaseModel):
    account_id: str
    source_id: int | None = None
    additional_env: dict[str, str] = Field(default_factory=dict)


class RatJobInfo(JobInfo):
    module_path: str


class RatJobSpawnerBase(JobSpawnerHelmDeployer, ABC):
    NAME: JobType

    def __init__(self, account_id: AccountIdType, *args: Any, **kwargs: Any) -> None:
        super().__init__(account_id, *args, **kwargs)
        self._account_id = account_id

    def get_helm_name(self, job_id: int) -> str:
        return self.get_job_name(job_id)

    def get_job_name(self, job_id: int) -> str:
        base_name = f"{self.NAME.value}-job-{job_id}-{self._account_id}"
        sanitized_name = base_name.replace("_", "-").lower()
        return sanitized_name[:JOB_NAME_MAX_LENGTH]

    @classmethod
    def job_path(cls) -> Path:
        return Path(__file__).parent / "helm"

    @property
    def helm_path(self) -> Path:
        return self.job_path()

    def _convert_to_helm_job_env(self, job_args: job_info_types) -> RatJobEnv:
        additional_env = {}
        for key, value in job_args.model_dump().items():
            if key not in ["account_id", "job_id"] and value is not None:
                additional_env[key.upper()] = str(value)
        return RatJobEnv(account_id=self._account_id, additional_env=additional_env)

    @abstractmethod
    async def _get_job_args(self, job_id: int) -> BaseModel:
        pass

    async def get_job_config(self, job_id: int) -> dict[str, Any]:
        return JobConfig(
            job_env=await self._get_job_args(job_id),
            job_info=RatJobInfo(
                name=self.get_job_name(job_id),
                id=job_id,
                image=get_config().jobs_image_url,
                job_type=f"{self.NAME.value}-job",
                module_path=f"service.k8s_jobs.{self.NAME.value.replace('-', '_')}_job.main",
            ),
            service=ServiceInfo(name=get_config().service_name),
            datadog_enabled=get_config().datadog_enabled,
        ).model_dump(mode="json", serialize_as_any=True)

    async def _get_latest_completed_job_time(self, job_args: dict[str, str]) -> datetime | None:
        async with scheduler_dal_ctx() as scheduler_dal:
            latest_job = await scheduler_dal.jobs_dal.get_latest_completed_job(
                account_id=self._account_id,
                job_args=job_args,
                job_type=self.NAME.value,
            )
            return latest_job.created_at if latest_job else None


class RatJobSpawnerBaseCancelDup(RatJobSpawnerBase, ABC):
    @abstractmethod
    def get_unique_args(self) -> dict[str, Any] | None:
        pass

    async def spawn_job(self, job_id: int) -> JobNameType | None:
        unique_args = self.get_unique_args()
        async with scheduler_dal_ctx() as scheduler_dal:
            if await scheduler_dal.jobs_dal.any_active_jobs_by(
                self._account_id,
                job_args=unique_args,
                exclude_ids=[job_id],
                job_type=self.NAME.value,
            ):
                LOGGER.warning(
                    "There is already an active job with args: %s, canceling current job %s..",
                    unique_args,
                    job_id,
                )
                await scheduler_dal.jobs_dal.cancel_job(self._account_id, job_id, JobCancelReason.FLOW_ALREADY_RUNNING)
                return f"job-{job_id}-canceled"
        return await super().spawn_job(job_id)


class SourceBaseJobSpawner(RatJobSpawnerBaseCancelDup, ABC):
    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, *args: Any, **kwargs: Any) -> None:
        super().__init__(account_id, source_id, *args, **kwargs)
        self._source_id = source_id

    def _convert_to_helm_job_env(self, job_args: job_info_types) -> RatJobEnv:
        ret_val = super()._convert_to_helm_job_env(job_args)
        ret_val.source_id = self._source_id
        ret_val.additional_env.pop("source_id", None)
        return ret_val

    def get_job_name(self, job_id: int) -> str:
        base_name = f"{self.NAME.value}-job-{self._account_id}-{self._source_id}"
        return base_name.replace("_", "-").lower()[:JOB_NAME_MAX_LENGTH].rstrip("-")

    def get_unique_args(self) -> dict[str, Any] | None:
        return {"source_id": str(self._source_id)}

    async def _get_latest_completed_job_time(self, job_args: dict[str, str] | None = None) -> datetime | None:
        job_args = job_args or {}
        job_args.update(self.get_unique_args() or {})
        return await super()._get_latest_completed_job_time(job_args=job_args)


class ClassificationJobSpawner(SourceBaseJobSpawner):
    NAME = JobType.CLASSIFICATION

    def __init__(
        self, account_id: AccountIdType, source_id: SourceIdType, force: bool, parent_id: str | None = None
    ) -> None:
        super().__init__(account_id, source_id, force)
        self._force = force
        self._parent_id = parent_id

    async def _get_job_args(self, job_id: int) -> RatJobEnv:
        last_run = await self._get_latest_completed_job_time()
        args = ClassificationJobArgs(
            source_id=self._source_id,
            force=self._force,
            account_id=self._account_id,
            job_id=job_id,
            last_run=last_run,
            parent_id=self._parent_id,
        )
        return self._convert_to_helm_job_env(args)


class SummaryJobSpawner(SourceBaseJobSpawner):
    NAME = JobType.SUMMARY

    def __init__(
        self, account_id: AccountIdType, source_id: SourceIdType, force: bool, parent_id: str | None = None
    ) -> None:
        super().__init__(account_id, source_id)
        self._force = force
        self._parent_id = parent_id

    async def _get_job_args(self, job_id: int) -> RatJobEnv:
        last_run = await self._get_latest_completed_job_time()
        args = SummaryJobArgs(
            account_id=self._account_id,
            source_id=self._source_id,
            job_id=job_id,
            force=self._force,
            last_run=last_run,
            parent_id=self._parent_id,
        )
        return self._convert_to_helm_job_env(args)


class UpdateIssuesJobSpawner(SourceBaseJobSpawner):
    NAME = JobType.UPDATE_ISSUES

    def __init__(
        self,
        account_id: AccountIdType,
        source_id: SourceIdType,
        force: bool = False,
        update_fields_only: bool = False,
    ) -> None:
        super().__init__(account_id, source_id)
        self._force = force
        self._update_fields_only = update_fields_only

    async def _get_job_args(self, job_id: int) -> RatJobEnv:
        last_update_at = await self._get_latest_completed_job_time(job_args=self.get_unique_args())
        args = UpdateIssuesJobArgs(
            account_id=self._account_id,
            job_id=job_id,
            last_update_at=last_update_at,
            force=self._force,
            source_id=self._source_id,
            update_fields_only=self._update_fields_only,
        )
        return self._convert_to_helm_job_env(args)


class BuildFieldsDataJobSpawner(SourceBaseJobSpawner):
    NAME = JobType.BUILD_FIELDS_DATA

    async def _get_job_args(self, job_id: int) -> RatJobEnv:
        args = BuildFieldsDataJobArgs(account_id=self._account_id, source_id=self._source_id, job_id=job_id)
        return self._convert_to_helm_job_env(args)


class PsvJobSpawner(SourceBaseJobSpawner):
    NAME = JobType.SECURITY_VIOLATION

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, force: bool) -> None:
        super().__init__(account_id, source_id, force)
        self._force = force

    async def _get_job_args(self, job_id: int) -> RatJobEnv:
        last_psv_at = await self._get_latest_completed_job_time()
        args = PsvJobArgs(
            source_id=self._source_id,
            force=self._force,
            account_id=self._account_id,
            job_id=job_id,
            last_psv_at=last_psv_at,
        )
        return self._convert_to_helm_job_env(args)


class RatJobSpawnerFactory(JobSpawnerFactory):
    JOB_SPAWNERS: dict[JobType, type[RatJobSpawnerBase]] = {
        JobType.CLASSIFICATION: ClassificationJobSpawner,
        JobType.UPDATE_ISSUES: UpdateIssuesJobSpawner,
        JobType.BUILD_FIELDS_DATA: BuildFieldsDataJobSpawner,
        JobType.SUMMARY: SummaryJobSpawner,
        JobType.SECURITY_VIOLATION: PsvJobSpawner,
    }

    @classmethod
    async def get_job_spawner_class(cls, create_job: CreateJob) -> type[JobSpawnerHelmDeployer]:
        spawner_name = JobType(create_job.job_type)
        if spawner_class := cls.JOB_SPAWNERS.get(spawner_name):
            return spawner_class
        raise ValueError(f"Spawner not found for job {spawner_name}")
