from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from fastapi import FastAPI
from prime_db_utils import init_tables
from prime_service_kit import get_app

from service.config import get_config
from service.routers import (
    cases_api,
    containers_api,
    issues_api,
    jira_fields,
    jobs_api,
    llm_context_api,
    psv_api,
    trends_api,
)
from service.services_clients import close_clients

service_apis = [
    issues_api,
    jobs_api,
    cases_api,
    trends_api,
    jira_fields,
    psv_api,
    containers_api,
    llm_context_api,
]


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None]:
    init_tables(get_config())
    yield
    await close_clients()


app = get_app(
    config=get_config(),
    authenticated_routers=[],
    public_routers=service_apis,
    lifespan=lifespan,
)
