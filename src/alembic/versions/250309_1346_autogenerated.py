"""autogenerated

Revision ID: 250309_1346
Revises: 250227_1644
Create Date: 2025-03-09 13:46:50.050845

"""

from collections.abc import Sequence
from typing import Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250309_1346"
down_revision: Union[str, None] = "250227_1644"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE jobs SET job_type = 'security_violation' WHERE job_type = 'psv'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE job SET job_type = 'psv' WHERE job_type = 'security_violation'")
    # ### end Alembic commands ###
