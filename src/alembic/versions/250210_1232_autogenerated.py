"""autogenerated

Revision ID: 250210_1232
Revises: 250206_1442
Create Date: 2025-02-10 12:32:08.552448

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250210_1232"
down_revision: Union[str, None] = "250206_1442"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("issues_analysis", sa.Column("is_security_before", sa.<PERSON>(), nullable=True))
    op.add_column("issues_analysis", sa.<PERSON>umn("is_security_during", sa.<PERSON>(), nullable=True))
    op.add_column("issues_analysis", sa.Column("is_security_after", sa.<PERSON>(), nullable=True))
    op.create_index(
        op.f("ix_issues_analysis_is_security_after"), "issues_analysis", ["is_security_after"], unique=False
    )
    op.create_index(
        op.f("ix_issues_analysis_is_security_before"), "issues_analysis", ["is_security_before"], unique=False
    )
    op.create_index(
        op.f("ix_issues_analysis_is_security_during"), "issues_analysis", ["is_security_during"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_issues_analysis_is_security_during"), table_name="issues_analysis")
    op.drop_index(op.f("ix_issues_analysis_is_security_before"), table_name="issues_analysis")
    op.drop_index(op.f("ix_issues_analysis_is_security_after"), table_name="issues_analysis")
    op.drop_column("issues_analysis", "is_security_after")
    op.drop_column("issues_analysis", "is_security_during")
    op.drop_column("issues_analysis", "is_security_before")
    # ### end Alembic commands ###
