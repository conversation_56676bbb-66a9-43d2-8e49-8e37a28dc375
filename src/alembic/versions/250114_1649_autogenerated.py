"""autogenerated

Revision ID: 250114_1649
Revises: 250113_2215
Create Date: 2025-01-14 16:49:04.249449

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250114_1649"
down_revision: Union[str, None] = "250113_2215"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("issue_summary_attributes", "long")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("issue_summary_attributes", sa.Column("long", sa.VARCHAR(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
