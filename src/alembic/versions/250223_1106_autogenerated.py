"""autogenerated

Revision ID: 250223_1106
Revises: 250220_1148
Create Date: 2025-02-23 11:06:03.836688

"""

from collections.abc import Sequence
from typing import Union

from alembic import op
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision: str = "250223_1106"
down_revision: Union[str, None] = "250220_1148"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema="public",
        enum_name="jobcancelreason",
        new_values=["CRON_JOB_DUP", "USER_CANCELED", "FLOW_ALREADY_RUNNING"],
        affected_columns=[TableReference(table_schema="public", table_name="jobs", column_name="cancel_reason")],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema="public",
        enum_name="jobcancelreason",
        new_values=["CRON_JOB_DUP", "USER_CANCELED"],
        affected_columns=[TableReference(table_schema="public", table_name="jobs", column_name="cancel_reason")],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###
