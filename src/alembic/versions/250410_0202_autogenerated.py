"""autogenerated

Revision ID: 250410_0202
Revises: 250407_1108
Create Date: 2025-04-10 02:02:12.800428

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250410_0202'
down_revision: Union[str, None] = '250407_1108'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('cases', 'provider_fields',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin')
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('CAST(concerns AS JSONB[])')], unique=False, postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('(concerns::jsonb[])')], unique=False, postgresql_using='gin')
    op.alter_column('cases', 'provider_fields',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    # ### end Alembic commands ###
