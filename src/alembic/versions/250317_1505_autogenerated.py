"""autogenerated

Revision ID: 250317_1505
Revises: 250317_1402
Create Date: 2025-03-17 15:06:00.771724

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250317_1505"
down_revision: Union[str, None] = "250317_1402"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_cases_issue_analysis_id"), "cases", ["issue_analysis_id"], unique=False)
    op.create_foreign_key(None, "cases", "issues_analysis", ["issue_analysis_id"], ["id"])
    op.drop_index("ix_issues_analysis_case_id", table_name="issues_analysis")
    op.drop_index("idx_concerns", table_name="issues_analysis", postgresql_using="gin")
    op.create_index(
        "idx_concerns",
        "issues_analysis",
        [sa.literal_column("CAST(concerns AS JSONB[])")],
        unique=False,
        postgresql_using="gin",
        postgresql_ops={"concerns": "jsonb_path_ops"},
    )
    op.drop_constraint("issues_analysis_case_id_fkey", "issues_analysis", type_="foreignkey")
    op.drop_column("issues_analysis", "case_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("issues_analysis", sa.Column("case_id", sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key("issues_analysis_case_id_fkey", "issues_analysis", "cases", ["case_id"], ["id"])
    op.drop_index(
        "idx_concerns",
        table_name="issues_analysis",
        postgresql_using="gin",
        postgresql_ops={"concerns": "jsonb_path_ops"},
    )
    op.create_index(
        "idx_concerns",
        "issues_analysis",
        [sa.literal_column("(concerns::jsonb[])")],
        unique=False,
        postgresql_using="gin",
    )
    op.create_index("ix_issues_analysis_case_id", "issues_analysis", ["case_id"], unique=False)
    op.drop_constraint(None, "cases", type_="foreignkey")
    op.drop_index(op.f("ix_cases_issue_analysis_id"), table_name="cases")
    # ### end Alembic commands ###
