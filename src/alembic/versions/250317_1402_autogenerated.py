"""autogenerated

Revision ID: 250317_1402
Revises: 250313_2203
Create Date: 2025-03-17 14:02:42.153587

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250317_1402"
down_revision: Union[str, None] = "250313_2203"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "idx_cases_parent_relationship", "cases", ["account_id", "source_id", "parent_issue_id"], unique=False
    )
    op.drop_index("idx_concerns", table_name="issues_analysis", postgresql_using="gin")
    op.create_index(
        "idx_concerns",
        "issues_analysis",
        [sa.literal_column("CAST(concerns AS JSONB[])")],
        unique=False,
        postgresql_using="gin",
        postgresql_ops={"concerns": "jsonb_path_ops"},
    )
    op.create_index(op.f("ix_issues_analysis_case_id"), "issues_analysis", ["case_id"], unique=False)
    op.create_index(op.f("ix_issues_analysis_confidence"), "issues_analysis", ["confidence"], unique=False)
    op.create_index(op.f("ix_issues_analysis_risk_score"), "issues_analysis", ["risk_score"], unique=False)
    op.create_index(
        op.f("ix_potential_security_violations_has_psv"), "potential_security_violations", ["has_psv"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_potential_security_violations_has_psv"), table_name="potential_security_violations")
    op.drop_index(op.f("ix_issues_analysis_risk_score"), table_name="issues_analysis")
    op.drop_index(op.f("ix_issues_analysis_confidence"), table_name="issues_analysis")
    op.drop_index(op.f("ix_issues_analysis_case_id"), table_name="issues_analysis")
    op.drop_index(
        "idx_concerns",
        table_name="issues_analysis",
        postgresql_using="gin",
        postgresql_ops={"concerns": "jsonb_path_ops"},
    )
    op.create_index(
        "idx_concerns",
        "issues_analysis",
        [sa.literal_column("(concerns::jsonb[])")],
        unique=False,
        postgresql_using="gin",
    )
    op.drop_index("idx_cases_parent_relationship", table_name="cases")
    # ### end Alembic commands ###
