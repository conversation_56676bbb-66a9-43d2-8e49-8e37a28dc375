"""autogenerated

Revision ID: 250113_2215
Revises: 250112_1459
Create Date: 2025-01-13 22:15:14.973538

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op
from prime_gen_ai_service_client import QuestionsOutput
from service.db.tables.utils.json_as_pydantic import JSONAsPydantic
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "250113_2215"
down_revision: Union[str, None] = "250112_1459"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "issue_summary_attributes",
        "questions",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=JSONAsPydantic(QuestionsOutput),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.alter_column(
        "issue_summary_attributes",
        "questions",
        existing_type=JSONAsPydantic(QuestionsOutput),
        type_=postgresql.JSONB(astext_type=sa.Text()),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
