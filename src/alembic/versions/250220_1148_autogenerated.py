"""autogenerated

Revision ID: 250220_1148
Revises: 250219_1624
Create Date: 2025-02-20 11:48:08.491577

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250220_1148"
down_revision: Union[str, None] = "250219_1624"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("issues_analysis", "case_id", existing_type=sa.INTEGER(), nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("issues_analysis", "case_id", existing_type=sa.INTEGER(), nullable=True)
    # ### end Alembic commands ###
