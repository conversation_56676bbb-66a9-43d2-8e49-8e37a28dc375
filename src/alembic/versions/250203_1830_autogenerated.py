"""autogenerated

Revision ID: 250203_1830
Revises: 250202_1316
Create Date: 2025-02-03 18:30:08.351530

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "250203_1830"
down_revision: Union[str, None] = "250202_1316"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("cases", sa.Column("parent_issue_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column(
        "cases", sa.Column("issue_type", sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default="")
    )
    op.add_column("cases", sa.Column("partial_id", sa.Integer(), nullable=True))
    op.add_column("cases", sa.Column("final_id", sa.Integer(), nullable=True))
    op.add_column("cases", sa.Column("summary_generation_error", sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.create_index(op.f("ix_cases_issue_type"), "cases", ["issue_type"], unique=False)
    op.create_index(op.f("ix_cases_parent_issue_id"), "cases", ["parent_issue_id"], unique=False)
    op.create_foreign_key(None, "cases", "issue_summary_attributes", ["final_id"], ["id"])
    op.create_foreign_key(None, "cases", "issue_summary_attributes", ["partial_id"], ["id"])

    op.execute("""
        UPDATE cases c
        SET parent_issue_id = i.parent_id,
            issue_type = i.issue_type,
            partial_id = i.partial_id,
            final_id = i.final_id,
            summary_generation_error = i.generation_error
        FROM issue_summary i
        WHERE c.id = i.case_id
    """)
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_issue_summary_account_id", table_name="issue_summary")
    op.drop_index("ix_issue_summary_created_at", table_name="issue_summary")
    op.drop_index("ix_issue_summary_deleted_at", table_name="issue_summary")
    op.drop_index("ix_issue_summary_issue_type", table_name="issue_summary")
    op.drop_index("ix_issue_summary_parent_id", table_name="issue_summary")
    op.drop_index("ix_issue_summary_updated_at", table_name="issue_summary")
    op.drop_table("issue_summary")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "cases", type_="foreignkey")
    op.drop_constraint(None, "cases", type_="foreignkey")
    op.drop_index(op.f("ix_cases_parent_issue_id"), table_name="cases")
    op.drop_index(op.f("ix_cases_issue_type"), table_name="cases")
    op.drop_column("cases", "summary_generation_error")
    op.drop_column("cases", "final_id")
    op.drop_column("cases", "partial_id")
    op.drop_column("cases", "issue_type")
    op.drop_column("cases", "parent_issue_id")
    op.create_table(
        "issue_summary",
        sa.Column("created_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("deleted_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("account_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("parent_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("issue_type", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("generation_error", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("case_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("partial_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("final_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(["case_id"], ["cases.id"], name="issue_summary_case_id_fkey"),
        sa.ForeignKeyConstraint(["final_id"], ["issue_summary_attributes.id"], name="issue_summary_final_id_fkey"),
        sa.ForeignKeyConstraint(["partial_id"], ["issue_summary_attributes.id"], name="issue_summary_partial_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="issue_summary_pkey"),
    )
    op.create_index("ix_issue_summary_updated_at", "issue_summary", ["updated_at"], unique=False)
    op.create_index("ix_issue_summary_parent_id", "issue_summary", ["parent_id"], unique=False)
    op.create_index("ix_issue_summary_issue_type", "issue_summary", ["issue_type"], unique=False)
    op.create_index("ix_issue_summary_deleted_at", "issue_summary", ["deleted_at"], unique=False)
    op.create_index("ix_issue_summary_created_at", "issue_summary", ["created_at"], unique=False)
    op.create_index("ix_issue_summary_account_id", "issue_summary", ["account_id"], unique=False)
    # ### end Alembic commands ###
