"""autogenerated

Revision ID: 250527_1233
Revises: 250526_1537
Create Date: 2025-05-27 12:33:49.687787

"""
from typing import Sequence, Union
import json

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250527_1233'
down_revision: Union[str, None] = '250526_1537'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Migrate quotes from objects to strings in top_recommendations"""
    connection = op.get_bind()
    
    result = connection.execute(
        sa.text("SELECT id, top_recommendations FROM design_docs WHERE top_recommendations IS NOT NULL")
    )
    
    for row in result:
        doc_id = row[0]
        top_recommendations_raw = row[1]
        
        if not top_recommendations_raw:
            continue
        
        if isinstance(top_recommendations_raw, str):
            top_recommendations = json.loads(top_recommendations_raw)
        else:
            top_recommendations = top_recommendations_raw
        
        updated = False
        new_recommendations = []
        
        for rec_str in top_recommendations:
            if isinstance(rec_str, str):
                rec = json.loads(rec_str)
            else:
                rec = rec_str
                
            new_rec = dict(rec)
            quotes = rec.get("quotes", [])
            
            if quotes and len(quotes) > 0 and isinstance(quotes[0], dict) and "quote_text" in quotes[0]:
                new_quotes = [q["quote_text"] for q in quotes if isinstance(q, dict) and "quote_text" in q]
                new_rec["quotes"] = new_quotes
                updated = True
            
            new_recommendations.append(json.dumps(new_rec))
        
        if updated:
            json_array = f'[{",".join(new_recommendations)}]'
            
            connection.execute(
                sa.text("""
                    WITH json_data AS (
                        SELECT json_array_elements(cast(:json_array AS json)) AS element
                    )
                    UPDATE design_docs 
                    SET top_recommendations = (
                        SELECT array_agg(element)
                        FROM json_data
                    )
                    WHERE id = :doc_id
                """),
                {"json_array": json_array, "doc_id": doc_id}
            )


def downgrade() -> None:
    """No downgrade implemented - this is a one-way data migration"""
    pass
