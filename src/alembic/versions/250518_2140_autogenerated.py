"""autogenerated

Revision ID: 250518_2140
Revises: 250504_1919
Create Date: 2025-05-18 21:40:09.288162

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250518_2140'
down_revision: Union[str, None] = '250515_1719'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('issue_summary_attributes', 'issue_hash',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('issue_summary_attributes', 'ai_version',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('issue_summary_attributes', 'summary',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('issue_summary_attributes', 'questions',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('issue_summary_attributes', 'short',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin')
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('CAST(concerns AS JSONB[])')], unique=False, postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('(concerns::jsonb[])')], unique=False, postgresql_using='gin')
    op.alter_column('issue_summary_attributes', 'short',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('issue_summary_attributes', 'questions',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('issue_summary_attributes', 'summary',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('issue_summary_attributes', 'ai_version',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('issue_summary_attributes', 'issue_hash',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###
