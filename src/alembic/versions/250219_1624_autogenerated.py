"""autogenerated

Revision ID: 250219_1624
Revises: 250218_1808
Create Date: 2025-02-19 16:24:32.219989

"""

from collections.abc import Sequence
from typing import Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250219_1624"
down_revision: Union[str, None] = "250218_1808"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("account_id_file_origin_id_unique_constraint", "design_docs", type_="unique")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "account_id_file_origin_id_unique_constraint", "design_docs", ["account_id", "file_origin_id"]
    )
    # ### end Alembic commands ###
