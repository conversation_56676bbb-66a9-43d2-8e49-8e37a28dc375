"""autogenerated

Revision ID: 250515_1719
Revises: 250504_1919
Create Date: 2025-05-15 17:19:26.146752

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250515_1719'
down_revision: Union[str, None] = '250504_1919'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cases', sa.Column('progress_percentage', sa.Integer(), nullable=False, server_default=sa.text('0')))
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin')
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('CAST(concerns AS JSONB[])')], unique=False, postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('(concerns::jsonb[])')], unique=False, postgresql_using='gin')
    op.drop_column('cases', 'progress_percentage')
    # ### end Alembic commands ###
