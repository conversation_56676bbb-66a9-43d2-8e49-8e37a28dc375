"""autogenerated

Revision ID: 250504_1919
Revises: 250428_1750
Create Date: 2025-05-04 19:19:02.845676

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250504_1919'
down_revision: Union[str, None] = '250428_1750'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column('cases', 'original_risk_score',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Integer(),
               existing_nullable=True)
    op.create_index(op.f('ix_cases_original_risk_score'), 'cases', ['original_risk_score'], unique=False)
    op.alter_column('design_docs', 'file_origin_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('design_docs', 'summary',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('design_docs', 'case_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.create_unique_constraint('account_file_origin_id_unique_constraint', 'design_docs', ['account_id', 'file_origin_id'])


def downgrade() -> None:
    op.drop_constraint('account_file_origin_id_unique_constraint', 'design_docs', type_='unique')
    op.alter_column('design_docs', 'case_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('design_docs', 'summary',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('design_docs', 'file_origin_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_index(op.f('ix_cases_original_risk_score'), table_name='cases')
    op.alter_column('cases', 'original_risk_score',
               existing_type=sa.Integer(),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=True)
