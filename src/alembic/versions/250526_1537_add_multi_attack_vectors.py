"""autogenerated

Revision ID: 250526_1537
Revises: 250518_2140
Create Date: 2025-05-26 15:37:07.250527

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from service.db.tables.utils.json_as_pydantic import JSONAsPydantic
from sqlalchemy.dialects import postgresql



# revision identifiers, used by Alembic.
revision: str = '250526_1537'
down_revision: Union[str, None] = '250518_2140'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('design_docs', sa.Column('attack_scenarios', sqlmodel.sql.sqltypes.AutoString(), nullable = True))  # Update the type to Integer - the original class was deleted
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('design_docs', 'attack_scenarios')
    # ### end Alembic commands ###
