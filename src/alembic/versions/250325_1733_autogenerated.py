"""autogenerated

Revision ID: 250325_1733
Revises: 250317_1505
Create Date: 2025-03-25 17:33:26.468890

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250325_1733'
down_revision: Union[str, None] = '250317_1505'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(f"UPDATE jobs SET job_type = 'update_issues' WHERE job_type = 'update_provider_fields'")


def downgrade() -> None:
    op.execute(f"UPDATE jobs SET job_type = 'update_provider_fields' WHERE job_type = 'update_issues'")
