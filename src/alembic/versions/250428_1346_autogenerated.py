"""autogenerated

Revision ID: 250428_1346
Revises: 250424_1803
Create Date: 2025-04-28 13:46:02.511778

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

from service.db.tables.utils.json_as_pydantic import JSONAsPydantic

# revision identifiers, used by Alembic.
revision: str = '250428_1346'
down_revision: Union[str, None] = '250424_1803'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('design_docs', sa.Column('attack_scenario_dataflow_diagram', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('design_docs', sa.Column('attack_scenario_mermaid_diagram', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('design_docs', sa.Column('attack_scenario_recommendations', sqlmodel.sql.sqltypes.AutoString(), nullable=True))# Update the type to Integer - the original class was deleted
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('design_docs', 'attack_scenario_recommendations')
    op.drop_column('design_docs', 'attack_scenario_mermaid_diagram')
    op.drop_column('design_docs', 'attack_scenario_dataflow_diagram')
    # ### end Alembic commands ###
