"""autogenerated

Revision ID: 250610_1543
Revises: 250607_2243
Create Date: 2025-06-10 15:43:18.537393

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250610_1543'
down_revision: Union[str, None] = '250607_2243'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_design_docs_account_id'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_case_id'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_created_at'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_deleted_at'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_doc_source_type'), table_name='design_docs')
    op.drop_index(op.f('ix_design_docs_updated_at'), table_name='design_docs')
    op.drop_table('design_docs')
    sa.Enum('ORIGINAL', 'REFERENCE', 'CONTAINER', 'URL', name='designdoctype').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('ORIGINAL', 'REFERENCE', 'CONTAINER', 'URL', name='designdoctype').create(op.get_bind())
    op.create_table('design_docs',
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('account_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('file_origin_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('summary', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_by', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('mermaid_diagram', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('top_recommendations', postgresql.ARRAY(postgresql.JSON(astext_type=sa.Text())), autoincrement=False, nullable=True),
    sa.Column('case_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('policy_recommendations', postgresql.ARRAY(postgresql.JSON(astext_type=sa.Text())), autoincrement=False, nullable=True),
    sa.Column('attack_scenario_dataflow_diagram', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('attack_scenario_recommendations', postgresql.ARRAY(postgresql.JSON(astext_type=sa.Text())), autoincrement=False, nullable=True),
    sa.Column('attack_scenario_markdown', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('attack_scenarios', postgresql.ARRAY(postgresql.JSON(astext_type=sa.Text())), autoincrement=False, nullable=True),
    sa.Column('agent_conversation_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('doc_source_type', postgresql.ENUM('ORIGINAL', 'REFERENCE', 'CONTAINER', 'URL', name='designdoctype', create_type=False), server_default=sa.text("'ORIGINAL'::designdoctype"), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['cases.id'], name=op.f('design_docs_case_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('design_docs_pkey')),
    sa.UniqueConstraint('account_id', 'file_origin_id', name=op.f('account_file_origin_id_unique_constraint'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_design_docs_updated_at'), 'design_docs', ['updated_at'], unique=False)
    op.create_index(op.f('ix_design_docs_doc_source_type'), 'design_docs', ['doc_source_type'], unique=False)
    op.create_index(op.f('ix_design_docs_deleted_at'), 'design_docs', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_design_docs_created_at'), 'design_docs', ['created_at'], unique=False)
    op.create_index(op.f('ix_design_docs_case_id'), 'design_docs', ['case_id'], unique=False)
    op.create_index(op.f('ix_design_docs_account_id'), 'design_docs', ['account_id'], unique=False)
    # ### end Alembic commands ###
