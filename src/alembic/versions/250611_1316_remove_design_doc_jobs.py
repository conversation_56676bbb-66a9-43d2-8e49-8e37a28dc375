"""autogenerated

Revision ID: 250611_1316
Revises: 250610_1543
Create Date: 2025-06-11 13:16:05.333993

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250611_1316'
down_revision: Union[str, None] = '250610_1543'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("DELETE FROM jobs WHERE job_type = 'design_docs'")


def downgrade() -> None:
    # No downgrade possible for deleted data
    pass
