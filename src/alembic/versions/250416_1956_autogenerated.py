"""autogenerated

Revision ID: 250416_1956
Revises: 250416_1759
Create Date: 2025-04-16 19:56:53.875151

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

from service.db.tables.utils.json_as_pydantic import JSONAsPydantic

# revision identifiers, used by Alembic.
revision: str = '250416_1956'
down_revision: Union[str, None] = '250416_1759'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('design_docs', sa.Column('policy_recommendations', sqlmodel.sql.sqltypes.AutoString(), nullable=True))# Update the type to Integer - the original class was deleted
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('design_docs', 'policy_recommendations')
    # ### end Alembic commands ###
