"""autogenerated

Revision ID: 250313_2203
Revises: 250309_1346
Create Date: 2025-03-13 22:03:51.088836

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250313_2203"
down_revision: Union[str, None] = "250309_1346"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_cases_final_id"), "cases", ["final_id"], unique=False)
    op.create_index(op.f("ix_cases_partial_id"), "cases", ["partial_id"], unique=False)
    op.create_index(
        "idx_concerns",
        "issues_analysis",
        [sa.literal_column("CAST(concerns AS JSONB[])")],
        unique=False,
        postgresql_using="gin",
        postgresql_ops={"concerns": "jsonb_path_ops"},
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "idx_concerns",
        table_name="issues_analysis",
        postgresql_using="gin",
        postgresql_ops={"concerns": "jsonb_path_ops"},
    )
    op.drop_index(op.f("ix_cases_partial_id"), table_name="cases")
    op.drop_index(op.f("ix_cases_final_id"), table_name="cases")
    # ### end Alembic commands ###
