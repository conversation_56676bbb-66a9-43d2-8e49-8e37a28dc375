"""autogenerated

Revision ID: 250218_1808
Revises: 250212_2134
Create Date: 2025-02-18 18:08:34.002764

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
import sqlmodel
from alembic import op
from service.db.tables.utils.json_as_pydantic import JSONAsPydantic
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "250218_1808"
down_revision: Union[str, None] = "250212_2134"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "design_docs",
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("account_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("file_origin_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("title", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("summary", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_by", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("mermaid_diagram", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("top_recommendations", sqlmodel.sql.sqltypes.AutoString(), nullable=True), # Update the type to string - the original class was deleted
        sa.Column("case_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["case_id"],
            ["cases.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("account_id", "file_origin_id", name="account_id_file_origin_id_unique_constraint"),
    )
    op.create_index(op.f("ix_design_docs_account_id"), "design_docs", ["account_id"], unique=False)
    op.create_index(op.f("ix_design_docs_case_id"), "design_docs", ["case_id"], unique=False)
    op.create_index(op.f("ix_design_docs_created_at"), "design_docs", ["created_at"], unique=False)
    op.create_index(op.f("ix_design_docs_deleted_at"), "design_docs", ["deleted_at"], unique=False)
    op.create_index(op.f("ix_design_docs_updated_at"), "design_docs", ["updated_at"], unique=False)
    op.create_index(
        op.f("ix_potential_security_violations_type"), "potential_security_violations", ["type"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_potential_security_violations_type"), table_name="potential_security_violations")
    op.drop_index(op.f("ix_design_docs_updated_at"), table_name="design_docs")
    op.drop_index(op.f("ix_design_docs_deleted_at"), table_name="design_docs")
    op.drop_index(op.f("ix_design_docs_created_at"), table_name="design_docs")
    op.drop_index(op.f("ix_design_docs_case_id"), table_name="design_docs")
    op.drop_index(op.f("ix_design_docs_account_id"), table_name="design_docs")
    op.drop_table("design_docs")
    # ### end Alembic commands ###
