"""autogenerated

Revision ID: 250119_1951
Revises: 250114_1649
Create Date: 2025-01-19 19:51:41.578259

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "250119_1951"
down_revision: Union[str, None] = "250114_1649"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("issue_summary", "childs")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "issue_summary",
        sa.Column(
            "childs",
            postgresql.ARRAY(postgresql.JSON(astext_type=sa.Text())),
            server_default=sa.text("'{}'::json[]"),
            autoincrement=False,
            nullable=True,
        ),
    )
    # ### end Alembic commands ###
