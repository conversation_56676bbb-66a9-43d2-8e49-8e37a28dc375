"""autogenerated

Revision ID: 250223_2020
Revises: 250223_1106
Create Date: 2025-02-23 20:20:15.216866

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250223_2020"
down_revision: Union[str, None] = "250223_1106"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_cases_issue_type", table_name="cases")
    op.drop_column("cases", "issue_type")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "cases",
        sa.Column(
            "issue_type",
            sa.VARCHAR(),
            server_default=sa.text("''::character varying"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.create_index("ix_cases_issue_type", "cases", ["issue_type"], unique=False)
    # ### end Alembic commands ###
