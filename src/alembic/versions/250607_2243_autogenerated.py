"""autogenerated

Revision ID: 250607_2243
Revises: 250604_1438
Create Date: 2025-06-07 22:43:51.338176

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250607_2243'
down_revision: Union[str, None] = '250604_1438'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('design_docs', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.drop_index(op.f('idx_concerns'), table_name='issues_analysis', postgresql_using='gin')
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('CAST(concerns AS JSONB[])')], unique=False, postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    op.create_index(op.f('idx_concerns'), 'issues_analysis', [sa.literal_column('(concerns::jsonb[])')], unique=False, postgresql_using='gin')
    op.drop_column('design_docs', 'description')
    # ### end Alembic commands ###
