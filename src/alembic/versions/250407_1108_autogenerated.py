"""autogenerated

Revision ID: 250407_1108
Revises: 250406_1022
Create Date: 2025-04-07 11:08:01.886151

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250407_1108'
down_revision: Union[str, None] = '250406_1022'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(f"UPDATE jobs SET job_type = 'build_fields_data' WHERE job_type = 'build_provider_fields_data'")


def downgrade() -> None:
    op.execute(f"UPDATE jobs SET job_type = 'build_provider_fields_data' WHERE job_type = 'build_fields_data'")
