"""autogenerated

Revision ID: 250424_1803
Revises: 250416_1956
Create Date: 2025-04-24 18:03:56.536653

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250424_1803'
down_revision: Union[str, None] = '250416_1956'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('case_history', sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.create_index(op.f('ix_case_history_account_id'), 'case_history', ['account_id'], unique=False)
    # Update case_history.account_id with values from the related cases table
    op.execute(
        sa.text("""
        UPDATE case_history 
        SET account_id = cases.account_id 
        FROM cases 
        WHERE case_history.case_id = cases.id
        """)
    )
    op.alter_column('case_history', 'account_id', nullable=False)

    op.add_column('issues_analysis', sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.create_index(op.f('ix_issues_analysis_account_id'), 'issues_analysis', ['account_id'], unique=False)
    # Update issues_analysis.account_id with values from the related cases table
    op.execute(
        sa.text("""
        UPDATE issues_analysis 
        SET account_id = cases.account_id 
        FROM cases 
        WHERE issues_analysis.id = cases.issue_analysis_id
        """)
    )
    # we want to delete the issues_analysis that have no case
    op.execute(sa.text("""
        DELETE FROM issues_analysis
        WHERE account_id is NULL
        """)
    )
    op.alter_column('issues_analysis', 'account_id', nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_issues_analysis_account_id'), table_name='issues_analysis')
    op.drop_column('issues_analysis', 'account_id')
    op.drop_index(op.f('ix_case_history_account_id'), table_name='case_history')
    op.drop_column('case_history', 'account_id')
    # ### end Alembic commands ###
