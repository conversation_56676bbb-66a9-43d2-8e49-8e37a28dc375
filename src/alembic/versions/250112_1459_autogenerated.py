"""autogenerated

Revision ID: 250112_1459
Revises: 
Create Date: 2025-04-07 22:11:33.791245

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from pydantic import BaseModel

from service.db.tables.utils.json_as_pydantic import JSONAsPydantic
from sqlalchemy.dialects import postgresql

from service.models import IssueLinks, SecurityControl, IssueAnalysisConcern, CaseComment, Implementation


class DBIssueSummaryChild(BaseModel):
    issue_id: str
    child_level: int = 1
    risk_score: int | None = None

# revision identifiers, used by Alembic.
revision: str = '250112_1459'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('OPEN', 'DONE', 'DISMISSED', name='psvstatus').create(op.get_bind())
    sa.Enum('OPEN', 'DONE', 'DISMISSED', name='casestatus').create(op.get_bind())
    sa.Enum('create', 'view', 'update_status', 'override_risk_category', name='caseauditaction').create(op.get_bind())
    sa.Enum('CRON_JOB_DUP', 'USER_CANCELED', name='jobcancelreason').create(op.get_bind())
    sa.Enum('SCHEDULED', 'PENDING', 'RUNNING', 'FINALIZING', 'COMPLETED', 'FAILED', 'CANCELED', name='jobstatus').create(op.get_bind())
    op.create_table('cases',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('source_id', sa.Integer(), nullable=False),
    sa.Column('issue_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', postgresql.ENUM('OPEN', 'DONE', 'DISMISSED', name='casestatus', create_type=False), nullable=False),
    sa.Column('issue_analysis_id', sa.Integer(), nullable=True),
    sa.Column('comments', postgresql.ARRAY(JSONAsPydantic(CaseComment)), nullable=True),
    sa.Column('recommendations', postgresql.ARRAY(JSONAsPydantic(Implementation)), nullable=True),
    sa.Column('original_risk_score', sa.Float(), nullable=True),
    sa.Column('write_back_ref_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('provider_fields', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('dismissed_reason', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('labels', postgresql.ARRAY(sa.VARCHAR()), server_default='{}', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('account_id', 'source_id', 'issue_id', name='account_source_issue_unique_constraint')
    )
    op.create_index('idx_provider_fields', 'cases', ['provider_fields'], unique=False, postgresql_using='gin')
    op.create_index(op.f('ix_cases_account_id'), 'cases', ['account_id'], unique=False)
    op.create_index(op.f('ix_cases_created_at'), 'cases', ['created_at'], unique=False)
    op.create_index(op.f('ix_cases_deleted_at'), 'cases', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_cases_issue_id'), 'cases', ['issue_id'], unique=False)
    op.create_index(op.f('ix_cases_source_id'), 'cases', ['source_id'], unique=False)
    op.create_index(op.f('ix_cases_status'), 'cases', ['status'], unique=False)
    op.create_index(op.f('ix_cases_updated_at'), 'cases', ['updated_at'], unique=False)
    op.create_table('issue_summary_attributes',
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('issue_hash', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('ai_version', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('summary', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('questions', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('long', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('short', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_issue_summary_attributes_account_id'), 'issue_summary_attributes', ['account_id'], unique=False)
    op.create_table('jobs',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('status', postgresql.ENUM('SCHEDULED', 'PENDING', 'RUNNING', 'FINALIZING', 'COMPLETED', 'FAILED', 'CANCELED', name='jobstatus', create_type=False), nullable=False),
    sa.Column('error', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('job_class', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('retry', sa.Integer(), nullable=False),
    sa.Column('job_group_id', sa.Uuid(), nullable=False),
    sa.Column('cron_job_group_id', sa.Integer(), nullable=True),
    sa.Column('job_args', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('cancel_reason', postgresql.ENUM('CRON_JOB_DUP', 'USER_CANCELED', name='jobcancelreason', create_type=False), nullable=True),
    sa.Column('run_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_by', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('job_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('job_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_jobs_account_id'), 'jobs', ['account_id'], unique=False)
    op.create_index(op.f('ix_jobs_created_at'), 'jobs', ['created_at'], unique=False)
    op.create_index(op.f('ix_jobs_cron_job_group_id'), 'jobs', ['cron_job_group_id'], unique=False)
    op.create_index(op.f('ix_jobs_deleted_at'), 'jobs', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_jobs_updated_at'), 'jobs', ['updated_at'], unique=False)
    op.create_table('labels',
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.CheckConstraint("name ~ '^[^\\s]+$'", name='name_no_whitespace_min_length'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('account_id', 'name', name='label_uix_account_id_name')
    )
    op.create_index(op.f('ix_labels_account_id'), 'labels', ['account_id'], unique=False)
    op.create_table('potential_security_violations',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('source_id', sa.Integer(), nullable=False),
    sa.Column('issue_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', postgresql.ENUM('OPEN', 'DONE', 'DISMISSED', name='psvstatus', create_type=False), nullable=False),
    sa.Column('dismissed_reason', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_potential_security_violations_account_id'), 'potential_security_violations', ['account_id'], unique=False)
    op.create_index(op.f('ix_potential_security_violations_created_at'), 'potential_security_violations', ['created_at'], unique=False)
    op.create_index(op.f('ix_potential_security_violations_deleted_at'), 'potential_security_violations', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_potential_security_violations_issue_id'), 'potential_security_violations', ['issue_id'], unique=False)
    op.create_index(op.f('ix_potential_security_violations_source_id'), 'potential_security_violations', ['source_id'], unique=False)
    op.create_index(op.f('ix_potential_security_violations_status'), 'potential_security_violations', ['status'], unique=False)
    op.create_index(op.f('ix_potential_security_violations_updated_at'), 'potential_security_violations', ['updated_at'], unique=False)
    op.create_table('case_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('audit_action', postgresql.ENUM('create', 'view', 'update_status', 'override_risk_category', name='caseauditaction', create_type=False), nullable=False),
    sa.Column('audit_action_args', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('case_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['cases.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_case_history_audit_action'), 'case_history', ['audit_action'], unique=False)
    op.create_index(op.f('ix_case_history_case_id'), 'case_history', ['case_id'], unique=False)
    op.create_index(op.f('ix_case_history_created_at'), 'case_history', ['created_at'], unique=False)
    op.create_index(op.f('ix_case_history_user'), 'case_history', ['user'], unique=False)
    op.create_table('issue_summary',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('account_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('parent_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('issue_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('partial_id', sa.Integer(), nullable=True),
    sa.Column('final_id', sa.Integer(), nullable=True),
    sa.Column('generation_error', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('case_id', sa.Integer(), nullable=False),
    sa.Column('childs', postgresql.ARRAY(JSONAsPydantic(DBIssueSummaryChild)), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['cases.id'], ),
    sa.ForeignKeyConstraint(['final_id'], ['issue_summary_attributes.id'], ),
    sa.ForeignKeyConstraint(['partial_id'], ['issue_summary_attributes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_issue_summary_account_id'), 'issue_summary', ['account_id'], unique=False)
    op.create_index(op.f('ix_issue_summary_created_at'), 'issue_summary', ['created_at'], unique=False)
    op.create_index(op.f('ix_issue_summary_deleted_at'), 'issue_summary', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_issue_summary_issue_type'), 'issue_summary', ['issue_type'], unique=False)
    op.create_index(op.f('ix_issue_summary_parent_id'), 'issue_summary', ['parent_id'], unique=False)
    op.create_index(op.f('ix_issue_summary_updated_at'), 'issue_summary', ['updated_at'], unique=False)
    op.create_table('issues_analysis',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('classification', sa.Boolean(), nullable=True),
    sa.Column('confidentiality', sa.Integer(), nullable=True),
    sa.Column('confidentiality_explanation', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('integrity', sa.Integer(), nullable=True),
    sa.Column('integrity_explanation', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('availability', sa.Integer(), nullable=True),
    sa.Column('availability_explanation', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('third_party_management', sa.Integer(), nullable=True),
    sa.Column('third_party_management_explanation', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('compliance', sa.Integer(), nullable=True),
    sa.Column('compliance_explanation', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('severity', sa.Integer(), nullable=True),
    sa.Column('severity_explanation', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('scope', sa.Integer(), nullable=True),
    sa.Column('scope_explanation', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('confidence', sa.Integer(), nullable=True),
    sa.Column('keywords', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('short_assessment', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('long_assessment', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('concerns', postgresql.ARRAY(JSONAsPydantic(IssueAnalysisConcern)), nullable=True),
    sa.Column('controls', postgresql.ARRAY(JSONAsPydantic(SecurityControl)), nullable=True),
    sa.Column('risk_score', sa.Integer(), nullable=True),
    sa.Column('error', sa.Boolean(), nullable=False),
    sa.Column('issue_hash', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('case_id', sa.Integer(), nullable=True),
    sa.Column('research_package_version', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_automated', sa.Boolean(), nullable=True),
    sa.Column('issue_links', postgresql.ARRAY(JSONAsPydantic(IssueLinks)), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['cases.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_issues_analysis_classification'), 'issues_analysis', ['classification'], unique=False)
    op.create_index(op.f('ix_issues_analysis_created_at'), 'issues_analysis', ['created_at'], unique=False)
    op.create_index(op.f('ix_issues_analysis_deleted_at'), 'issues_analysis', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_issues_analysis_is_automated'), 'issues_analysis', ['is_automated'], unique=False)
    op.create_index(op.f('ix_issues_analysis_updated_at'), 'issues_analysis', ['updated_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_issues_analysis_updated_at'), table_name='issues_analysis')
    op.drop_index(op.f('ix_issues_analysis_is_automated'), table_name='issues_analysis')
    op.drop_index(op.f('ix_issues_analysis_deleted_at'), table_name='issues_analysis')
    op.drop_index(op.f('ix_issues_analysis_created_at'), table_name='issues_analysis')
    op.drop_index(op.f('ix_issues_analysis_classification'), table_name='issues_analysis')
    op.drop_table('issues_analysis')
    op.drop_index(op.f('ix_issue_summary_updated_at'), table_name='issue_summary')
    op.drop_index(op.f('ix_issue_summary_parent_id'), table_name='issue_summary')
    op.drop_index(op.f('ix_issue_summary_issue_type'), table_name='issue_summary')
    op.drop_index(op.f('ix_issue_summary_deleted_at'), table_name='issue_summary')
    op.drop_index(op.f('ix_issue_summary_created_at'), table_name='issue_summary')
    op.drop_index(op.f('ix_issue_summary_account_id'), table_name='issue_summary')
    op.drop_table('issue_summary')
    op.drop_index(op.f('ix_case_history_user'), table_name='case_history')
    op.drop_index(op.f('ix_case_history_created_at'), table_name='case_history')
    op.drop_index(op.f('ix_case_history_case_id'), table_name='case_history')
    op.drop_index(op.f('ix_case_history_audit_action'), table_name='case_history')
    op.drop_table('case_history')
    op.drop_index(op.f('ix_potential_security_violations_updated_at'), table_name='potential_security_violations')
    op.drop_index(op.f('ix_potential_security_violations_status'), table_name='potential_security_violations')
    op.drop_index(op.f('ix_potential_security_violations_source_id'), table_name='potential_security_violations')
    op.drop_index(op.f('ix_potential_security_violations_issue_id'), table_name='potential_security_violations')
    op.drop_index(op.f('ix_potential_security_violations_deleted_at'), table_name='potential_security_violations')
    op.drop_index(op.f('ix_potential_security_violations_created_at'), table_name='potential_security_violations')
    op.drop_index(op.f('ix_potential_security_violations_account_id'), table_name='potential_security_violations')
    op.drop_table('potential_security_violations')
    op.drop_index(op.f('ix_labels_account_id'), table_name='labels')
    op.drop_table('labels')
    op.drop_index(op.f('ix_jobs_updated_at'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_deleted_at'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_cron_job_group_id'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_created_at'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_account_id'), table_name='jobs')
    op.drop_table('jobs')
    op.drop_index(op.f('ix_issue_summary_attributes_account_id'), table_name='issue_summary_attributes')
    op.drop_table('issue_summary_attributes')
    op.drop_index(op.f('ix_cases_updated_at'), table_name='cases')
    op.drop_index(op.f('ix_cases_status'), table_name='cases')
    op.drop_index(op.f('ix_cases_source_id'), table_name='cases')
    op.drop_index(op.f('ix_cases_issue_id'), table_name='cases')
    op.drop_index(op.f('ix_cases_deleted_at'), table_name='cases')
    op.drop_index(op.f('ix_cases_created_at'), table_name='cases')
    op.drop_index(op.f('ix_cases_account_id'), table_name='cases')
    op.drop_index('idx_provider_fields', table_name='cases', postgresql_using='gin')
    op.drop_table('cases')
    sa.Enum('SCHEDULED', 'PENDING', 'RUNNING', 'FINALIZING', 'COMPLETED', 'FAILED', 'CANCELED', name='jobstatus').drop(op.get_bind())
    sa.Enum('CRON_JOB_DUP', 'USER_CANCELED', name='jobcancelreason').drop(op.get_bind())
    sa.Enum('create', 'view', 'update_status', 'override_risk_category', name='caseauditaction').drop(op.get_bind())
    sa.Enum('OPEN', 'DONE', 'DISMISSED', name='casestatus').drop(op.get_bind())
    sa.Enum('OPEN', 'DONE', 'DISMISSED', name='psvstatus').drop(op.get_bind())
    # ### end Alembic commands ###
