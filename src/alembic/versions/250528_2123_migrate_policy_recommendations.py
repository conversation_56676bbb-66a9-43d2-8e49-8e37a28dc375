"""autogenerated

Revision ID: 250528_2123
Revises: 250527_1712
Create Date: 2025-05-28 21:23:15.772466

"""
from typing import Sequence, Union
import json

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250528_2123'
down_revision: Union[str, None] = '250527_1712'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Migrate quotes from objects to strings in policy_recommendations"""
    connection = op.get_bind()

    result = connection.execute(
        sa.text("SELECT id, policy_recommendations FROM design_docs WHERE policy_recommendations IS NOT NULL")
    )

    for row in result:
        doc_id = row[0]
        policy_recommendations_raw = row[1]

        if not policy_recommendations_raw:
            continue

        if isinstance(policy_recommendations_raw, str):
            policy_recommendations = json.loads(policy_recommendations_raw)
        else:
            policy_recommendations = policy_recommendations_raw

        updated = False
        new_recommendations = []

        for rec_str in policy_recommendations:
            if isinstance(rec_str, str):
                rec = json.loads(rec_str)
            else:
                rec = rec_str

            new_rec = dict(rec)
            quotes = rec.get("quotes", [])

            if quotes and len(quotes) > 0 and isinstance(quotes[0], dict) and "quote_text" in quotes[0]:
                new_quotes = [q["quote_text"] for q in quotes if isinstance(q, dict) and "quote_text" in q]
                new_rec["quotes"] = new_quotes
                updated = True

            new_recommendations.append(json.dumps(new_rec))

        if updated:
            json_array = f'[{",".join(new_recommendations)}]'

            connection.execute(
                sa.text("""
                    WITH json_data AS (
                        SELECT json_array_elements(cast(:json_array AS json)) AS element
                    )
                    UPDATE design_docs 
                    SET policy_recommendations = (
                        SELECT array_agg(element)
                        FROM json_data
                    )
                    WHERE id = :doc_id
                """),
                {"json_array": json_array, "doc_id": doc_id}
            )


def downgrade() -> None:
    """No downgrade implemented - this is a one-way data migration"""
    pass