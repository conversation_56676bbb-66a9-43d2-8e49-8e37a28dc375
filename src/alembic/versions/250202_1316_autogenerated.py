"""autogenerated

Revision ID: 250202_1316
Revises: 250119_1951
Create Date: 2025-02-02 13:16:02.621899

"""

from collections.abc import Sequence
from typing import Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250202_1316"
down_revision: Union[str, None] = "250119_1951"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("""
        UPDATE issues_analysis
        SET concerns = ARRAY(
            SELECT json_build_object(
                'id', concern->>'id',
                'short_description', concern->>'short_description',
                'long_description', concern->>'long_description',
                'methodology', json_build_object('category', '', 'type', 'Mitre')
            )::json
            FROM unnest(concerns) AS concern
        )
        WHERE concerns IS NOT NULL;
    """)


def downgrade() -> None:
    op.execute("""
        UPDATE issues_analysis
        SET concerns = ARRAY(
            SELECT json_build_object(
                'id', concern->>'id',
                'short_description', concern->>'short_description',
                'long_description', concern->>'long_description'
            )::json
            FROM unnest(concerns) AS concern
        );
        WHERE concerns IS NOT NULL;

    """)
