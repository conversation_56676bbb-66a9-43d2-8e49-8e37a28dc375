"""autogenerated

Revision ID: 250206_1442
Revises: 250203_1830
Create Date: 2025-02-06 14:42:57.872691

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
import sqlmodel
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250206_1442"
down_revision: Union[str, None] = "250203_1830"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("issues_analysis", sa.Column("fire_summary", sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("issues_analysis", "fire_summary")
    # ### end Alembic commands ###
