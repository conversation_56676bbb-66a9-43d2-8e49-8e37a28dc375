"""autogenerated

Revision ID: 250527_1712
Revises: 250527_1233
Create Date: 2025-05-27 17:12:31.903145

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250527_1712"
down_revision: Union[str, None] = "250527_1233"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("design_docs", sa.Column("agent_conversation_id", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("design_docs", "agent_conversation_id")
    # ### end Alembic commands ###
