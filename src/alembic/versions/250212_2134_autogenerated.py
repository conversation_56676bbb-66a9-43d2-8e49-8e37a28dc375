"""autogenerated

Revision ID: 250212_2134
Revises: 250210_1232
Create Date: 2025-02-12 21:34:06.735378

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250212_2134"
down_revision: Union[str, None] = "250210_1232"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("issues_analysis", sa.Column("is_security_enhancement", sa.<PERSON>(), nullable=True))
    op.drop_index("ix_issues_analysis_is_security_after", table_name="issues_analysis")
    op.drop_index("ix_issues_analysis_is_security_before", table_name="issues_analysis")
    op.drop_index("ix_issues_analysis_is_security_during", table_name="issues_analysis")
    op.create_index(
        op.f("ix_issues_analysis_is_security_enhancement"), "issues_analysis", ["is_security_enhancement"], unique=False
    )
    op.drop_column("issues_analysis", "is_security_after")
    op.drop_column("issues_analysis", "is_security_before")
    op.drop_column("issues_analysis", "is_security_during")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("issues_analysis", sa.Column("is_security_during", sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column("issues_analysis", sa.Column("is_security_before", sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column("issues_analysis", sa.Column("is_security_after", sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_index(op.f("ix_issues_analysis_is_security_enhancement"), table_name="issues_analysis")
    op.create_index("ix_issues_analysis_is_security_during", "issues_analysis", ["is_security_during"], unique=False)
    op.create_index("ix_issues_analysis_is_security_before", "issues_analysis", ["is_security_before"], unique=False)
    op.create_index("ix_issues_analysis_is_security_after", "issues_analysis", ["is_security_after"], unique=False)
    op.drop_column("issues_analysis", "is_security_enhancement")
    # ### end Alembic commands ###
