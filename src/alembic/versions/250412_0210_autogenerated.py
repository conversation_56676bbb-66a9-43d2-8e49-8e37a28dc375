"""autogenerated

Revision ID: 250412_0210
Revises: 250410_0202
Create Date: 2025-04-12 02:10:19.724543

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250412_0210'
down_revision: Union[str, None] = '250410_0202'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('case_history_case_id_fkey', 'case_history', type_='foreignkey')
    op.create_foreign_key(None, 'case_history', 'cases', ['case_id'], ['id'], ondelete='CASCADE')
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin')
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('CAST(concerns AS JSONB[])')], unique=False, postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_concerns', table_name='issues_analysis', postgresql_using='gin', postgresql_ops={'concerns': 'jsonb_path_ops'})
    op.create_index('idx_concerns', 'issues_analysis', [sa.literal_column('(concerns::jsonb[])')], unique=False, postgresql_using='gin')
    op.drop_constraint(None, 'case_history', type_='foreignkey')
    op.create_foreign_key('case_history_case_id_fkey', 'case_history', 'cases', ['case_id'], ['id'])
    # ### end Alembic commands ###
