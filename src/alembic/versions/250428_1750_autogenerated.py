"""autogenerated

Revision ID: 250428_1750
Revises: 250428_1346
Create Date: 2025-04-28 17:50:19.577875

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '250428_1750'
down_revision: Union[str, None] = '250428_1346'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('design_docs', sa.Column('attack_scenario_markdown', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.drop_column('design_docs', 'attack_scenario_mermaid_diagram')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('design_docs', sa.Column('attack_scenario_mermaid_diagram', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('design_docs', 'attack_scenario_markdown')
    # ### end Alembic commands ###
