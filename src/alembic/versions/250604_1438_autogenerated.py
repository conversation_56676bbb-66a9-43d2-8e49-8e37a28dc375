"""autogenerated

Revision ID: 250604_1438
Revises: 250528_2123
Create Date: 2025-06-04 14:38:16.818892

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250604_1438"
down_revision: Union[str, None] = "250528_2123"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum("ORIGINAL", "REFERENCE", "CONTAINER", "URL", name="designdoctype").create(op.get_bind())

    # Step 1: Add column as nullable first
    op.add_column(
        "design_docs",
        sa.Column(
            "doc_source_type",
            postgresql.ENUM("ORIGINAL", "REFERENCE", "CONTAIN<PERSON>", "URL", name="designdoctype", create_type=False),
            nullable=True,  # Make it nullable initially
        ),
    )

    # Step 2: Update existing rows with appropriate values
    op.execute("""
        UPDATE design_docs
        SET doc_source_type = CASE
            WHEN case_id IS NOT NULL THEN 'CONTAINER'::designdoctype
            WHEN url IS NOT NULL THEN 'URL'::designdoctype
            ELSE 'ORIGINAL'::designdoctype
        END
    """)

    # Step 3: Now make the column NOT NULL and set server default
    op.alter_column("design_docs", "doc_source_type", nullable=False, server_default="ORIGINAL")

    op.create_index(op.f("ix_design_docs_doc_source_type"), "design_docs", ["doc_source_type"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_design_docs_doc_source_type"), table_name="design_docs")
    op.drop_column("design_docs", "doc_source_type")
    sa.Enum("ORIGINAL", "REFERENCE", "CONTAINER", "URL", name="designdoctype").drop(op.get_bind())
    # ### end Alembic commands ###
