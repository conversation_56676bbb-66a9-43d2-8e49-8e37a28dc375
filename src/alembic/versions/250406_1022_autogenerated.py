"""autogenerated

Revision ID: 250406_1022
Revises: 250325_1733
Create Date: 2025-04-06 10:22:24.432981

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '250406_1022'
down_revision: Union[str, None] = '250325_1733'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.drop_column('cases', 'summary_generation_error')


def downgrade() -> None:
    op.add_column('cases', sa.Column('summary_generation_error', sa.VARCHAR(), autoincrement=False, nullable=True))