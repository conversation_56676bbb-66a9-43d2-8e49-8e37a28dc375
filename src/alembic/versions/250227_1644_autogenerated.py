"""autogenerated

Revision ID: 250227_1644
Revises: 250223_2020
Create Date: 2025-02-27 16:44:03.169858

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
import sqlmodel
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "250227_1644"
down_revision: Union[str, None] = "250223_2020"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "potential_security_violations",
        sa.Column("research_package_version", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    )
    op.add_column(
        "potential_security_violations", sa.Column("issue_hash", sqlmodel.sql.sqltypes.AutoString(), nullable=True)
    )
    op.add_column("potential_security_violations", sa.Column("has_psv", sa.<PERSON><PERSON>(), nullable=True))
    op.execute("UPDATE potential_security_violations SET has_psv = true")
    op.alter_column("potential_security_violations", "has_psv", existing_type=sa.BOOLEAN(), nullable=False)
    op.execute("""
UPDATE potential_security_violations psv
SET 
    issue_hash = ia.issue_hash,
    research_package_version = ia.research_package_version
FROM cases c
JOIN issues_analysis ia ON c.issue_analysis_id = ia.id and c.deleted_at IS NULL
WHERE 
    psv.account_id = c.account_id
    AND psv.source_id = c.source_id
    AND psv.issue_id = c.issue_id
    AND psv.deleted_at IS NULL""")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("potential_security_violations", "issue_hash")
    op.drop_column("potential_security_violations", "research_package_version")
    op.execute("DELETE FROM potential_security_violations WHERE has_psv = false")
    op.drop_column("potential_security_violations", "has_psv")
    # ### end Alembic commands ###
